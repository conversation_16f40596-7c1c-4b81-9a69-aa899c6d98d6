#!/bin/bash

# Performance Test Runner Script for Exchange API
# Usage: ./run-performance-test.sh [quick|heavy|full]

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
WHITE='\033[1;37m'
GRAY='\033[0;37m'
NC='\033[0m' # No Color

# Default test type
TEST_TYPE=${1:-quick}

echo -e "${GREEN}🚀 Exchange API Performance Test Runner${NC}"
echo -e "${GREEN}=======================================${NC}"

# Check if Maven is available
if ! command -v mvn &> /dev/null; then
    echo -e "${RED}❌ Maven not found. Please install Maven first.${NC}"
    exit 1
fi

# Set test parameters based on type
case $TEST_TYPE in
    "quick")
        TEST_CLASS="SimpleLoadTest"
        TEST_METHOD="quickLoadTest"
        echo -e "${YELLOW}🏃 Running Quick Load Test (50 users, 5 orders each)${NC}"
        ;;
    "heavy")
        TEST_CLASS="SimpleLoadTest"
        TEST_METHOD="heavyLoadTest"
        echo -e "${YELLOW}🔥 Running Heavy Load Test (100 users, 10 orders each)${NC}"
        ;;
    "full")
        TEST_CLASS="OrderPerformanceTest"
        TEST_METHOD="testOrderPerformanceWith100CCU"
        echo -e "${YELLOW}💪 Running Full Performance Test (100 CCU, realistic trading)${NC}"
        ;;
    "extreme")
        TEST_CLASS="ExtremeLoadTest"
        TEST_METHOD="extremeLoadTest"
        echo -e "${RED}🔥 Running EXTREME Load Test (1000 orders/sec for 1 minute)${NC}"
        echo -e "${RED}⚠️  WARNING: This will generate 60,000 orders!${NC}"
        ;;
    "gradual")
        TEST_CLASS="GradualLoadTest"
        TEST_METHOD="gradualLoadTest"
        echo -e "${CYAN}📈 Running GRADUAL Load Test (Progressive: 10->50->200->500 RPS)${NC}"
        echo -e "${CYAN}ℹ️  INFO: Tests system behavior under increasing load${NC}"
        ;;
    "analysis")
        TEST_CLASS="MessageLossAnalysisTest"
        TEST_METHOD="messageLossAnalysisTest"
        echo -e "${PURPLE}🔍 Running MESSAGE LOSS Analysis (Track API to Kafka)${NC}"
        echo -e "${PURPLE}ℹ️  INFO: Analyzes message delivery reliability${NC}"
        ;;
    *)
        echo -e "${RED}❌ Invalid test type. Use: quick, heavy, full, extreme, gradual, or analysis${NC}"
        echo -e "${CYAN}Usage: $0 [quick|heavy|full|extreme|gradual|analysis]${NC}"
        exit 1
        ;;
esac

echo -e "${CYAN}📊 Test Configuration:${NC}"
echo -e "   ${WHITE}- Test Type: $TEST_TYPE${NC}"
echo -e "   ${WHITE}- Target URL: http://10.242.30.58:30113/exchange-api/order/add${NC}"
echo -e "   ${WHITE}- Test Class: $TEST_CLASS${NC}"
echo -e "   ${WHITE}- Test Method: $TEST_METHOD${NC}"
echo ""

# Compile the project first
echo -e "${YELLOW}🔨 Compiling project...${NC}"
if mvn clean compile test-compile -q; then
    echo -e "${GREEN}✅ Compilation successful${NC}"
else
    echo -e "${RED}❌ Compilation failed!${NC}"
    exit 1
fi

# Run the specific test
echo -e "${YELLOW}🧪 Running performance test...${NC}"
echo -e "${CYAN}⏱️  Start time: $(date)${NC}"

TEST_COMMAND="mvn test -Dtest=com.icetea.lotus.matching.performance.$TEST_CLASS#$TEST_METHOD -q"
echo -e "${GRAY}🔧 Command: $TEST_COMMAND${NC}"

# Execute the test and capture start time
START_TIME=$(date +%s)
eval $TEST_COMMAND
TEST_RESULT=$?
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo ""
echo -e "${CYAN}⏱️  End time: $(date)${NC}"
echo -e "${CYAN}⏱️  Total duration: ${DURATION} seconds${NC}"

if [ $TEST_RESULT -eq 0 ]; then
    echo -e "${GREEN}✅ Performance test completed successfully!${NC}"
else
    echo -e "${RED}❌ Performance test failed!${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}📋 Test Summary:${NC}"
echo -e "   ${WHITE}- Test Type: $TEST_TYPE${NC}"
echo -e "   ${WHITE}- Duration: ${DURATION} seconds${NC}"
if [ $TEST_RESULT -eq 0 ]; then
    echo -e "   ${GREEN}- Status: SUCCESS${NC}"
else
    echo -e "   ${RED}- Status: FAILED${NC}"
fi

echo ""
echo -e "${CYAN}💡 Usage examples:${NC}"
echo -e "   ${GRAY}./run-performance-test.sh quick${NC}"
echo -e "   ${GRAY}./run-performance-test.sh heavy${NC}"
echo -e "   ${GRAY}./run-performance-test.sh full${NC}"
echo -e "   ${RED}./run-performance-test.sh extreme${NC}"
echo -e "   ${CYAN}./run-performance-test.sh gradual${NC}"
echo -e "   ${PURPLE}./run-performance-test.sh analysis${NC}"

# Make the script executable
chmod +x "$0" 2>/dev/null || true
