@echo off
echo ===================================================
echo    MATCHING ENGINE PERFORMANCE TEST RUNNER
echo ===================================================
echo.

REM Set JVM options for optimal performance testing
set JAVA_OPTS=-Xms2g -Xmx4g -XX:+UseG1GC -XX:+UseStringDeduplication -XX:MaxGCPauseMillis=200

echo Setting up environment...
echo JVM Options: %JAVA_OPTS%
echo.

REM Clean and compile
echo Cleaning and compiling...
call mvn clean compile test-compile -q
if %ERRORLEVEL% neq 0 (
    echo ❌ Compilation failed!
    pause
    exit /b 1
)

echo ✅ Compilation successful
echo.

REM Run performance tests
echo 🚀 Starting Performance Tests...
echo.

REM Option 1: Run specific performance test class
echo Choose test type:
echo 1. Basic Performance Comparison
echo 2. High Load Stress Test  
echo 3. Latency Distribution Test
echo 4. All Tests (Recommended)
echo.

set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" (
    echo Running Basic Performance Comparison...
    call mvn test -Dtest=MatchingEnginePerformanceTest#testSpotVsFuturesPerformance -Dspring.profiles.active=test
) else if "%choice%"=="2" (
    echo Running High Load Stress Test...
    call mvn test -Dtest=MatchingEnginePerformanceTest#testHighLoadStressTest -Dspring.profiles.active=test
) else if "%choice%"=="3" (
    echo Running Latency Distribution Test...
    call mvn test -Dtest=MatchingEnginePerformanceTest#testLatencyDistribution -Dspring.profiles.active=test
) else if "%choice%"=="4" (
    echo Running All Performance Tests...
    call mvn test -Dtest=MatchingEnginePerformanceTest -Dspring.profiles.active=test
) else (
    echo Invalid choice. Running all tests...
    call mvn test -Dtest=MatchingEnginePerformanceTest -Dspring.profiles.active=test
)

if %ERRORLEVEL% neq 0 (
    echo.
    echo ❌ Performance tests failed!
    echo Check the logs above for details.
) else (
    echo.
    echo ✅ Performance tests completed successfully!
    echo.
    echo 📊 Test Results Summary:
    echo - Check console output above for detailed metrics
    echo - TPS (Transactions Per Second) comparison
    echo - Latency distribution analysis
    echo - Memory and CPU usage patterns
)

echo.
echo ===================================================
echo           PERFORMANCE TEST COMPLETED
echo ===================================================
pause
