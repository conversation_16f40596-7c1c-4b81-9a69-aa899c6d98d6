@echo off
echo ===================================================
echo    AUTOMATED PERFORMANCE TEST RUNNER
echo ===================================================
echo.

REM Set JVM options for optimal performance testing
set JAVA_OPTS=-Xms4g -Xmx8g -XX:+UseG1GC -XX:+UseStringDeduplication -XX:MaxGCPauseMillis=200 -XX:+UnlockExperimentalVMOptions -XX:+UseZGC

echo Setting up environment for automated testing...
echo JVM Options: %JAVA_OPTS%
echo.

REM Clean and compile
echo 🔧 Cleaning and compiling...
call mvn clean compile test-compile -q
if %ERRORLEVEL% neq 0 (
    echo ❌ Compilation failed!
    pause
    exit /b 1
)

echo ✅ Compilation successful
echo.

REM Create results directory if not exists
if not exist "performance-results" mkdir performance-results

REM Run automated performance tests
echo 🚀 Starting Automated Performance Test Suite...
echo.
echo This will run the following tests automatically:
echo   1. Basic Performance Comparison
echo   2. Load Testing Scenarios (Low/Medium/High)
echo   3. Stress Testing (1000 TPS target)
echo   4. Latency Analysis
echo   5. Memory Usage Analysis
echo.
echo Estimated duration: 10-15 minutes
echo Results will be saved to: performance-results/test_[timestamp]/
echo.

set /p confirm="Continue with automated testing? (Y/N): "
if /i not "%confirm%"=="Y" (
    echo Test cancelled by user.
    pause
    exit /b 0
)

echo.
echo 📊 Running automated performance tests...
echo.

REM Run the automated test runner
java %JAVA_OPTS% -cp "target/classes;target/test-classes" -Dspring.profiles.active=test com.icetea.lotus.matching.performance.AutomatedPerformanceTestRunner

if %ERRORLEVEL% neq 0 (
    echo.
    echo ❌ Automated performance tests failed!
    echo Check the logs above for details.
    echo.
) else (
    echo.
    echo ✅ Automated performance tests completed successfully!
    echo.
    echo 📁 Results Location:
    for /f "delims=" %%i in ('dir /b /od performance-results') do set LATEST_DIR=%%i
    echo   Directory: performance-results\%LATEST_DIR%\
    echo   Files:
    echo     - test_suite_results.json (Complete results in JSON format)
    echo     - SUMMARY_REPORT.md (Human-readable summary)
    echo     - Individual test files (01_basic_performance.json, etc.)
    echo.
    echo 📊 Quick Summary:
    if exist "performance-results\%LATEST_DIR%\SUMMARY_REPORT.md" (
        echo Opening summary report...
        start notepad "performance-results\%LATEST_DIR%\SUMMARY_REPORT.md"
    )
    echo.
    echo 💡 Tips:
    echo   - Review SUMMARY_REPORT.md for key findings
    echo   - Use JSON files for detailed analysis
    echo   - Compare results across multiple test runs
    echo   - Monitor trends over time
)

echo.
echo ===================================================
echo        AUTOMATED PERFORMANCE TEST COMPLETED
echo ===================================================

REM Ask if user wants to run another test
echo.
set /p rerun="Run another automated test? (Y/N): "
if /i "%rerun%"=="Y" (
    echo.
    goto :start
)

pause
