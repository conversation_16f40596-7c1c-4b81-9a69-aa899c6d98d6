# Matching Engine Performance Tests

## 📋 **Overview**

Comprehensive performance testing suite cho Matching Engine module, so sánh hiệu năng trực tiếp giữa **Spot Trading** và **Futures Trading** engines.

## 🚀 **Quick Start**

### **Windows:**
```bash
# Chạy tất cả performance tests
run-performance-test.bat

# Hoặc chọn test cụ thể
mvn test -Dtest=MatchingEnginePerformanceTest -Dspring.profiles.active=test
```

### **Linux/Mac:**
```bash
# Compile và chạy tests
mvn clean compile test-compile
mvn test -Dtest=MatchingEnginePerformanceTest -Dspring.profiles.active=test
```

## 📊 **Test Suites**

### **1. MatchingEnginePerformanceTest**
**Mục đích:** So sánh hiệu năng cơ bản giữa Spot và Futures engines

**Tests bao gồm:**
- **Basic Performance Comparison:** 50,000 orders với 10 threads
- **High Load Stress Test:** 1,000 TPS trong 60 giây
- **Latency Distribution Test:** Phân tích P50, P95, P99 latency

**Chạy riêng lẻ:**
```bash
# Basic comparison
mvn test -Dtest=MatchingEnginePerformanceTest#testSpotVsFuturesPerformance

# Stress test
mvn test -Dtest=MatchingEnginePerformanceTest#testHighLoadStressTest

# Latency analysis
mvn test -Dtest=MatchingEnginePerformanceTest#testLatencyDistribution
```

### **2. MatchingEngineBenchmark**
**Mục đích:** Detailed benchmark với multiple scenarios

**Tests bao gồm:**
- **Low Load:** 100 concurrent users
- **Medium Load:** 500 concurrent users  
- **High Load:** 1,000 concurrent users
- **Order Type Comparison:** Market vs Limit orders
- **Memory Usage Analysis:** Memory efficiency comparison

**Chạy riêng lẻ:**
```bash
# Load scenarios
mvn test -Dtest=MatchingEngineBenchmark#benchmarkLowLoad
mvn test -Dtest=MatchingEngineBenchmark#benchmarkMediumLoad
mvn test -Dtest=MatchingEngineBenchmark#benchmarkHighLoad

# Order types
mvn test -Dtest=MatchingEngineBenchmark#benchmarkOrderTypes

# Memory analysis
mvn test -Dtest=MatchingEngineBenchmark#benchmarkMemoryUsage
```

## ⚙️ **Configuration**

### **Test Parameters (application-test.yml):**
```yaml
performance:
  test:
    symbol: "BTC/USDT"
    base-price: 50000
    price-variance: 1000
    warmup-orders: 1000
    test-orders-per-thread: 5000
    concurrent-threads: 10
    
    stress-test:
      target-tps: 1000
      duration-seconds: 60
      max-threads: 20
```

### **JVM Optimization:**
```bash
# Recommended JVM settings for performance testing
-Xms2g -Xmx4g 
-XX:+UseG1GC 
-XX:+UseStringDeduplication 
-XX:MaxGCPauseMillis=200
```

## 📈 **Expected Results**

### **Typical Performance Metrics:**

| **Engine** | **TPS** | **Avg Latency** | **P95 Latency** | **Memory Usage** |
|------------|---------|-----------------|-----------------|------------------|
| **Spot Trading** | 5,000-8,000 | 2-3ms | 8ms | Lower |
| **Futures Trading** | 8,000-12,000 | 3-5ms | 12ms | Higher |

### **Performance Characteristics:**

**🔸 Spot Trading Engine:**
- **Algorithm:** CoinTraderV2
- **Strengths:** Low latency, memory efficient
- **Use Case:** High-frequency spot trading

**🔸 Futures Trading Engine:**
- **Algorithm:** FIFO/PRO_RATA/HYBRID/TWAP
- **Strengths:** High throughput, advanced features
- **Use Case:** Complex futures trading with stop orders

## 🔍 **Test Scenarios**

### **1. Basic Load Test:**
```
- Orders: 50,000 total (5,000 per thread)
- Threads: 10 concurrent
- Order Types: 50% BUY, 50% SELL
- Price Range: ±1,000 USDT from base price
- Quantity: 0.001-1.0 BTC random
```

### **2. Stress Test:**
```
- Target: 1,000 orders/second
- Duration: 60 seconds
- Total Orders: 60,000
- Threads: 20 concurrent
- Monitoring: TPS achievement rate
```

### **3. Latency Test:**
```
- Sample Size: 10,000 orders
- Metrics: Min, Max, Avg, P50, P95, P99
- Order Processing: Sequential
- Measurement: Nanosecond precision
```

## 📊 **Interpreting Results**

### **Key Metrics:**

**🔸 TPS (Transactions Per Second):**
- **Good:** > 5,000 TPS
- **Excellent:** > 10,000 TPS

**🔸 Latency:**
- **P95 < 10ms:** Excellent
- **P95 < 20ms:** Good
- **P95 > 50ms:** Needs optimization

**🔸 Memory Usage:**
- **< 100MB per 10K orders:** Efficient
- **> 500MB per 10K orders:** Memory leak risk

### **Performance Comparison:**

**✅ Futures > Spot TPS:** Expected (advanced algorithms)
**✅ Spot < Futures Latency:** Expected (simpler logic)
**⚠️ Memory Growth:** Monitor for leaks

## 🛠 **Troubleshooting**

### **Common Issues:**

**1. OutOfMemoryError:**
```bash
# Increase heap size
-Xmx8g -Xms4g
```

**2. Low TPS Results:**
```bash
# Check CPU cores
# Increase thread pool size
# Disable unnecessary logging
```

**3. High Latency:**
```bash
# Use G1GC
# Reduce GC pause time
# Check system load
```

### **Debug Mode:**
```bash
# Enable detailed logging
mvn test -Dtest=MatchingEnginePerformanceTest -Dlogging.level.com.icetea.lotus.matching=DEBUG
```

## 📝 **Custom Tests**

### **Create Custom Scenario:**
```java
@Test
void customPerformanceTest() {
    // Configure test parameters
    int orders = 10000;
    int threads = 5;
    
    // Run custom test
    PerformanceResult result = runCustomTest(orders, threads);
    
    // Assert performance requirements
    assertThat(result.tps).isGreaterThan(1000);
    assertThat(result.avgLatencyMs).isLessThan(10);
}
```

## 🎯 **Performance Goals**

### **Target Metrics:**

| **Scenario** | **Spot TPS** | **Futures TPS** | **Max Latency** |
|--------------|--------------|-----------------|-----------------|
| **Low Load** | 3,000+ | 5,000+ | 5ms |
| **Medium Load** | 5,000+ | 8,000+ | 10ms |
| **High Load** | 8,000+ | 12,000+ | 20ms |

### **Acceptance Criteria:**
- ✅ **Futures TPS ≥ 1.5x Spot TPS**
- ✅ **P95 Latency < 20ms**
- ✅ **Memory growth < 10MB/1K orders**
- ✅ **No memory leaks after 100K orders**

## 📞 **Support**

**Issues:** Check logs in `target/surefire-reports/`
**Performance:** Monitor JVM metrics during tests
**Memory:** Use profiler for detailed analysis

---

**Happy Performance Testing! 🚀**
