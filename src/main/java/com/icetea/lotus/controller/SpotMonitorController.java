package com.icetea.lotus.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.icetea.lotus.matching.domain.enums.SpotOrderDirection;
import com.icetea.lotus.matching.infrastructure.exchange.ExchangeCompatibilityService;
import com.icetea.lotus.matching.infrastructure.exchange.TradePlate;
import com.icetea.lotus.matching.infrastructure.exchange.TradePlateItem;
import com.icetea.lotus.matching.infrastructure.integration.MatchingEngineIntegrationService;
import com.icetea.lotus.matching.infrastructure.sharding.SymbolShardingManager;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.AMOUNT;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.ITEMS;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.ORDERID;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.PRICE;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.SYMBOL;

/**
 * Controller for monitoring matching engine operations, managing engine instances, and interacting with matching engines.
 * It provides endpoints for retrieving engine details, trade plates, and matching engine statuses,
 * as well as for resetting matching engines and fetching trading symbols.
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/monitor")
public class SpotMonitorController {
    public static final String DEPTH = "depth";
    public static final String LIMIT_PRICE_ORDER_COUNT = "limit_price_order_count";
    public static final String MARKET_PRICE_ORDER_COUNT = "market_price_order_count";
    public static final String SUCCESS = "success";
    public static final String MESSAGE = "message";
    public static final String SYMBOL_NOT_OWNED_BY_THIS_POD = "Symbol not owned by this pod";
    public static final String POD_NAME = "pod_name";
    public static final String ERROR = "error";
    public static final String LIMIT_PRICE_QUEUE = "limit_price_queue";
    public static final String MARKET_PRICE_QUEUE = "market_price_queue";
    private final Logger log = LoggerFactory.getLogger(SpotMonitorController.class);
    private final ObjectMapper objectMapper = new ObjectMapper();

    private final ExchangeCompatibilityService exchangeService;
    private final MatchingEngineIntegrationService integrationService;
    private final SymbolShardingManager shardingManager;

    @GetMapping("overview")
    public ObjectNode traderOverview(String symbol) {
        log.info("Getting matching engine overview for symbol: {}", symbol);

        ObjectNode result = objectMapper.createObjectNode();

        try {
            // Check if symbol is owned by this pod
            if (!shardingManager.isSymbolOwnedByThisPod(symbol)) {
                return null;
            }

            // Get exchange engine stats
            ObjectNode ask = objectMapper.createObjectNode();
            ObjectNode bid = objectMapper.createObjectNode();

            // Get order book from exchange service
            Object orderBook = exchangeService.getOrderBook(symbol);
            if (orderBook instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> orderBookMap = (Map<String, Object>) orderBook;

                // Extract ask/sell information
                Object asks = orderBookMap.get("asks");
                if (asks instanceof List) {
                    ask.put(LIMIT_PRICE_ORDER_COUNT, ((List<?>) asks).size());
                    ask.put(DEPTH, ((List<?>) asks).size());
                }
                ask.put(MARKET_PRICE_ORDER_COUNT, 0); // Default for matching engine

                // Extract bid/buy information
                Object bids = orderBookMap.get("bids");
                if (bids instanceof List) {
                    bid.put(LIMIT_PRICE_ORDER_COUNT, ((List<?>) bids).size());
                    bid.put(DEPTH, ((List<?>) bids).size());
                }
                bid.put(MARKET_PRICE_ORDER_COUNT, 0); // Default for matching engine
            } else {
                // Default values if no order book
                ask.put(LIMIT_PRICE_ORDER_COUNT, 0);
                ask.put(MARKET_PRICE_ORDER_COUNT, 0);
                ask.put(DEPTH, 0);
                bid.put(LIMIT_PRICE_ORDER_COUNT, 0);
                bid.put(MARKET_PRICE_ORDER_COUNT, 0);
                bid.put(DEPTH, 0);
            }

            result.set("ask", ask);
            result.set("bid", bid);

        } catch (Exception e) {
            log.error("Error getting matching engine overview for symbol: {}", symbol, e);
            return null;
        }

        return result;
    }

    @GetMapping("trader-detail")
    public ObjectNode traderDetail(String symbol) {
        log.info("Getting matching engine detail for symbol: {}", symbol);

        ObjectNode result = objectMapper.createObjectNode();

        try {
            // Check if symbol is owned by this pod
            if (!shardingManager.isSymbolOwnedByThisPod(symbol)) {
                log.warn("Symbol {} not owned by this pod", symbol);
                return null;
            }

            ObjectNode ask = objectMapper.createObjectNode();
            ObjectNode bid = objectMapper.createObjectNode();

            // Get order book details from exchange service
            Object orderBook = exchangeService.getOrderBook(symbol);

            // Serialize order book details
            ask.put(LIMIT_PRICE_QUEUE, objectMapper.writeValueAsString(orderBook));
            ask.put(MARKET_PRICE_QUEUE, "[]"); // Default empty for matching engine
            bid.put(LIMIT_PRICE_QUEUE, objectMapper.writeValueAsString(orderBook));
            bid.put(MARKET_PRICE_QUEUE, "[]"); // Default empty for matching engine

            result.set("ask", ask);
            result.set("bid", bid);

        } catch (Exception e) {
            log.error("Error getting matching engine detail for symbol: {}", symbol, e);
            return null;
        }

        return result;
    }

    @GetMapping("plate")
    public Map<String, Object> traderPlate(String symbol) {
        log.info("Getting trade plate for symbol: {}", symbol);

        Map<String, Object> result = new HashMap<>();

        try {
            // Check if symbol is owned by this pod
            if (!shardingManager.isSymbolOwnedByThisPod(symbol)) {
                log.warn("Symbol {} not owned by this pod", symbol);
                return Map.of();
            }

            // CRITICAL FIX: Get actual trade plates instead of order book
            TradePlate buyTradePlate = exchangeService.getTradePlate(symbol, SpotOrderDirection.BUY);
            TradePlate sellTradePlate = exchangeService.getTradePlate(symbol, SpotOrderDirection.SELL);

            // Convert trade plates to simple arrays
            List<Map<String, Object>> bidItems = new ArrayList<>();
            List<Map<String, Object>> askItems = new ArrayList<>();

            if (buyTradePlate != null) {
                for (TradePlateItem item : buyTradePlate.getItems()) {
                    Map<String, Object> bidItem = new HashMap<>();
                    bidItem.put(PRICE, item.getPrice());
                    bidItem.put(AMOUNT, item.getAmount());
                    bidItem.put(ORDERID, item.getOrderId());
                    bidItems.add(bidItem);
                }
            }

            if (sellTradePlate != null) {
                for (TradePlateItem item : sellTradePlate.getItems()) {
                    Map<String, Object> askItem = new HashMap<>();
                    askItem.put(PRICE, item.getPrice());
                    askItem.put(AMOUNT, item.getAmount());
                    askItem.put(ORDERID, item.getOrderId());
                    askItems.add(askItem);
                }
            }

            result.put("bid", bidItems);
            result.put("ask", askItems);

            log.info("Trade plate for symbol {}: bid items={}, ask items={}",
                    symbol, bidItems.size(), askItems.size());

        } catch (Exception e) {
            log.error("Error getting trade plate for symbol: {}", symbol, e);
            return Map.of();
        }

        return result;
    }


    @GetMapping("/plate-full")
    public Map<String, ObjectNode> traderPlateFull(@RequestParam(name = "symbol") String symbol) {
        log.info("Getting full trade plate for symbol: {}", symbol);

        Map<String, ObjectNode> result = new HashMap<>();

        try {
            // This ensures consistency with what users see in real-time
            TradePlate buyTradePlate = exchangeService.getTradePlate(symbol, SpotOrderDirection.BUY);
            TradePlate sellTradePlate = exchangeService.getTradePlate(symbol, SpotOrderDirection.SELL);

            log.info(" DEBUG: MonitorController trade plate instances - BUY: {}, SELL: {}",
                    System.identityHashCode(buyTradePlate), System.identityHashCode(sellTradePlate));

            ObjectNode bidNode = objectMapper.createObjectNode();
            ObjectNode askNode = objectMapper.createObjectNode();

            // Convert ObjectNode to String then parse back like exchange module does
            if (buyTradePlate != null && !buyTradePlate.getItems().isEmpty()) {
                String bidJson = objectMapper.writeValueAsString(buyTradePlate.toJSON(100));
                bidNode = (ObjectNode) objectMapper.readTree(bidJson);
            } else {
                bidNode.set(ITEMS, objectMapper.createArrayNode());
            }

            if (sellTradePlate != null && !sellTradePlate.getItems().isEmpty()) {
                String askJson = objectMapper.writeValueAsString(sellTradePlate.toJSON(100));
                askNode = (ObjectNode) objectMapper.readTree(askJson);
            } else {
                askNode.set(ITEMS, objectMapper.createArrayNode());
                log.info("DEBUG: Sell trade plate is null or empty");
            }

            // Apply same items array handling logic as exchange module
            if (bidNode.has(ITEMS) && askNode.has(ITEMS) && (!bidNode.get(ITEMS).isArray() || !askNode.get(ITEMS).isArray())) {
                bidNode.set(ITEMS, objectMapper.createArrayNode().add(bidNode.get(ITEMS)));
                askNode.set(ITEMS, objectMapper.createArrayNode().add(askNode.get(ITEMS)));
            }

            result.put("bid", bidNode);
            result.put("ask", askNode);


        } catch (Exception e) {
            log.error("Error getting full trade plate for symbol: {}", symbol, e);
            // CRITICAL FIX: Return empty trade plate instead of null on error
            Map<String, ObjectNode> emptyResult = new HashMap<>();
            ObjectNode emptyBidNode = objectMapper.createObjectNode();
            ObjectNode emptyAskNode = objectMapper.createObjectNode();
            emptyBidNode.set(ITEMS, objectMapper.createArrayNode());
            emptyAskNode.set(ITEMS, objectMapper.createArrayNode());
            emptyResult.put("bid", emptyBidNode);
            emptyResult.put("ask", emptyAskNode);
            return emptyResult;
        }

        return result;
    }

    @GetMapping("symbols")
    public List<String> symbols() {
        try {
            List<String> ownedSymbols = shardingManager.getOwnedSymbols();
            log.info("Getting symbols owned by this pod: {}", ownedSymbols);
            return ownedSymbols;
        } catch (Exception e) {
            log.error("Error getting owned symbols", e);
            return new ArrayList<>();
        }
    }

    @GetMapping("engines")
    public Map<String, Integer> engines() {
        log.info("START engines:");

        Map<String, Integer> symbols = new HashMap<>();

        try {
            List<String> ownedSymbols = shardingManager.getOwnedSymbols();
            log.info("engines symbols: {}", ownedSymbols);
            for (String symbol : ownedSymbols) {
                // Check if engine is active for this symbol
                Object orderBook = exchangeService.getOrderBook(symbol);
                if (orderBook != null) {
                    symbols.put(symbol, 1); // Active
                } else {
                    symbols.put(symbol, 2); // Halted
                }
            }
        } catch (Exception e) {
            log.error("Error getting engines status", e);
        }

        return symbols;
    }

    @GetMapping("reset-trader")
    public Map<String, Object> resetTrader(String symbol) {
        log.info("======[Start]Reset Matching Engine: {} ======", symbol);

        Map<String, Object> result = new HashMap<>();

        try {
            // Check if symbol is owned by this pod
            if (!shardingManager.isSymbolOwnedByThisPod(symbol)) {
                result.put(SUCCESS, false);
                result.put(MESSAGE, SYMBOL_NOT_OWNED_BY_THIS_POD);
                return result;
            }

            // Initialize matching engine for symbol
            integrationService.initializeMatchingEngine(symbol);

            result.put(SUCCESS, true);
            result.put(MESSAGE, "Matching engine reset successfully for symbol: " + symbol);
            result.put(SYMBOL, symbol);
            result.put(POD_NAME, shardingManager.getPodName());

            log.info("======[END]Reset Matching Engine: {} successful======", symbol);

        } catch (Exception e) {
            log.error("Error resetting matching engine for symbol: {}", symbol, e);
            result.put(SUCCESS, false);
            result.put(MESSAGE, "ENGINE_RESET_FAILED");
            result.put(ERROR, e.getMessage());
        }

        return result;
    }

    @GetMapping("start-trader")
    public Map<String, Object> startTrader(String symbol) {
        log.info("======Start Matching Engine: {}======", symbol);

        Map<String, Object> result = new HashMap<>();

        try {
            // Try to claim symbol if not owned
            boolean isOwned = shardingManager.isSymbolOwnedByThisPod(symbol);
            if (!isOwned) {
                boolean claimed = shardingManager.claimSymbol(symbol);
                if (!claimed) {
                    result.put(SUCCESS, false);
                    result.put(MESSAGE, "CURRENCY_PAIR_DOES_NOT_EXIST");
                    return result;
                }
            }

            // Initialize matching engine
            integrationService.initializeMatchingEngine(symbol);

            result.put(SUCCESS, true);
            result.put(MESSAGE, "CURRENCY_PAIR_CREATED_SUCCESSFULLY");
            result.put("symbol", symbol);
            result.put(POD_NAME, shardingManager.getPodName());

        } catch (Exception e) {
            log.error("Error starting matching engine for symbol: {}", symbol, e);
            result.put(SUCCESS, false);
            result.put(MESSAGE, "ENGINE_CREATION_FAILED");
            result.put(ERROR, e.getMessage());
        }

        return result;
    }

    @GetMapping("stop-trader")
    public Map<String, Object> stopTrader(String symbol) {
        log.info("======Stop Matching Engine: {}======", symbol);

        Map<String, Object> result = new HashMap<>();

        try {
            // Check if symbol is owned by this pod
            if (!shardingManager.isSymbolOwnedByThisPod(symbol)) {
                result.put(SUCCESS, false);
                result.put(MESSAGE, "CURRENCY_PAIR_ENGINE_DOES_NOT_EXIST");
                return result;
            }

            // For matching engine, we can release the symbol to "stop" it
            boolean released = shardingManager.releaseSymbol(symbol);

            if (released) {
                result.put(SUCCESS, true);
                result.put(MESSAGE, "ENGINE_STOPPED_SUCCESSFULLY");
                result.put("symbol", symbol);
                result.put(POD_NAME, shardingManager.getPodName());
            } else {
                result.put(SUCCESS, false);
                result.put(MESSAGE, "ENGINE_STOP_FAILED");
            }

        } catch (Exception e) {
            log.error("Error stopping matching engine for symbol: {}", symbol, e);
            result.put(SUCCESS, false);
            result.put(MESSAGE, "ENGINE_STOP_FAILED");
            result.put(ERROR, e.getMessage());
        }

        return result;
    }
}
