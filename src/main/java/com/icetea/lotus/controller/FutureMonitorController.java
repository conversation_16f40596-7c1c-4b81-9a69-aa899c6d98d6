package com.icetea.lotus.controller;

import com.icetea.lotus.matching.domain.entity.SimpleStopOrder;
import com.icetea.lotus.matching.infrastructure.constants.CommonConstance;
import com.icetea.lotus.matching.infrastructure.futurecore.FutureCoreCompatibilityService;
import com.icetea.lotus.matching.infrastructure.messaging.dto.FutureTradePlateMessage;
import com.icetea.lotus.matching.infrastructure.service.MatchingEngineStopOrderManager;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/monitor/future")
public class FutureMonitorController {
    private final FutureCoreCompatibilityService futureCoreService;
    private final MatchingEngineStopOrderManager matchingEngineStopOrderManager;

    @GetMapping("plate-full")
    public ResponseEntity<OrderBookResponse> plateFull(@RequestParam(name = "symbol") String symbol) {
        log.info("Getting full trade plate for symbol {}", symbol);

        try {
            // Lấy order book snapshot từ FutureCoreCompatibilityService
            FutureTradePlateMessage.OrderBookSnapshot orderBookSnapshot = futureCoreService.createOrderBookSnapshot(symbol);

            // Tạo response
            OrderBookResponse response = OrderBookResponse.builder()
                    .symbol(symbol)
                    .timestamp(LocalDateTime.now())
                    .orderBookSnapshot(orderBookSnapshot)
                    .build();

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error getting full trade plate for symbol: {}", symbol, e);

            // Trả về empty response khi có lỗi
            FutureTradePlateMessage.OrderBookSnapshot emptySnapshot = FutureTradePlateMessage.OrderBookSnapshot.builder()
                    .symbol(symbol)
                    .asks(Collections.emptyList())
                    .bids(Collections.emptyList())
                    .build();

            OrderBookResponse errorResponse = OrderBookResponse.builder()
                    .symbol(symbol)
                    .timestamp(LocalDateTime.now())
                    .orderBookSnapshot(emptySnapshot)
                    .build();

            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
        }
    }

    @GetMapping("stop-order")
    public ResponseEntity<List<SimpleStopOrder>> stopOrder(@RequestParam(name = "symbol") String symbol) {
        List<SimpleStopOrder> stopOrders = matchingEngineStopOrderManager.getStopOrdersForSymbol(symbol, CommonConstance.FUTURE);
        return  ResponseEntity.ok(stopOrders);
    }

    /**
     * Response DTO cho API plate-full
     */
    @Getter
    public static class OrderBookResponse {
        // Setters
        // Getters
        @Setter
        private String symbol;
        @Setter
        private LocalDateTime timestamp;
        private FutureTradePlateMessage.OrderBookSnapshot orderBookSnapshot;

        public static OrderBookResponseBuilder builder() {
            return new OrderBookResponseBuilder();
        }

        public static class OrderBookResponseBuilder {
            private String symbol;
            private LocalDateTime timestamp;
            private FutureTradePlateMessage.OrderBookSnapshot orderBookSnapshot;

            public OrderBookResponseBuilder symbol(String symbol) {
                this.symbol = symbol;
                return this;
            }

            public OrderBookResponseBuilder timestamp(LocalDateTime timestamp) {
                this.timestamp = timestamp;
                return this;
            }

            public OrderBookResponseBuilder orderBookSnapshot(FutureTradePlateMessage.OrderBookSnapshot orderBookSnapshot) {
                this.orderBookSnapshot = orderBookSnapshot;
                return this;
            }

            public OrderBookResponse build() {
                OrderBookResponse response = new OrderBookResponse();
                response.symbol = this.symbol;
                response.timestamp = this.timestamp;
                response.orderBookSnapshot = this.orderBookSnapshot;
                return response;
            }
        }
    }
}