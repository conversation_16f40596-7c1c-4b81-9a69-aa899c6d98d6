package com.icetea.lotus.matching.domain.valueobject;

/**
 * <AUTHOR> nguy<PERSON>
 */

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * Money value object cho matching engine
 * Đ<PERSON>i diện cho giá trị tiền tệ với precision cao
 */
@Data
@NoArgsConstructor
public class Money implements Comparable<Money> {
    
    /**
     * Gi<PERSON> trị tiền
     */
    private BigDecimal value;
    
    /**
     * Scale mặc định cho tính toán
     */
    public static final int DEFAULT_SCALE = 8;
    
    /**
     * RoundingMode mặc định
     */
    public static final RoundingMode DEFAULT_ROUNDING_MODE = RoundingMode.HALF_UP;
    
    /**
     * Money ZERO constant
     */
    public static final Money ZERO = new Money(BigDecimal.ZERO);
    
    /**
     * Money ONE constant
     */
    public static final Money ONE = new Money(BigDecimal.ONE);
    
    /**
     * Constructor từ BigDecimal
     */
    public Money(BigDecimal value) {
        this.value = value != null ? value.setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE) : BigDecimal.ZERO;
    }
    
    /**
     * Constructor từ String
     */
    public Money(String value) {
        this(new BigDecimal(value));
    }
    
    /**
     * Constructor từ double
     */
    public Money(double value) {
        this(BigDecimal.valueOf(value));
    }
    
    /**
     * Constructor từ long
     */
    public Money(long value) {
        this(BigDecimal.valueOf(value));
    }
    
    /**
     * Static factory method
     */
    public static Money of(BigDecimal value) {
        return new Money(value);
    }
    
    public static Money of(String value) {
        return new Money(value);
    }
    
    public static Money of(double value) {
        return new Money(value);
    }
    
    public static Money of(long value) {
        return new Money(value);
    }

    /**
     * Static zero method for compatibility
     */
    public static Money numberZero() {
        return ZERO;
    }
    
    /**
     * Cộng
     */
    public Money add(Money other) {
        if (other == null) return this;
        return new Money(this.value.add(other.value));
    }
    
    /**
     * Trừ
     */
    public Money subtract(Money other) {
        if (other == null) return this;
        return new Money(this.value.subtract(other.value));
    }
    
    /**
     * Nhân
     */
    public Money multiply(Money other) {
        if (other == null) return ZERO;
        return new Money(this.value.multiply(other.value));
    }
    
    /**
     * Nhân với BigDecimal
     */
    public Money multiply(BigDecimal multiplier) {
        if (multiplier == null) return ZERO;
        return new Money(this.value.multiply(multiplier));
    }
    
    /**
     * Chia
     */
    public Money divide(Money other) {
        if (other == null || other.isZero()) {
            throw new ArithmeticException("Cannot divide by zero or null");
        }
        return new Money(this.value.divide(other.value, DEFAULT_SCALE, DEFAULT_ROUNDING_MODE));
    }
    
    /**
     * Chia với BigDecimal
     */
    public Money divide(BigDecimal divisor) {
        if (divisor == null || divisor.compareTo(BigDecimal.ZERO) == 0) {
            throw new ArithmeticException("Cannot divide by zero or null");
        }
        return new Money(this.value.divide(divisor, DEFAULT_SCALE, DEFAULT_ROUNDING_MODE));
    }
    
    /**
     * Giá trị tuyệt đối
     */
    public Money abs() {
        return new Money(this.value.abs());
    }
    
    /**
     * Negate
     */
    public Money negate() {
        return new Money(this.value.negate());
    }
    
    /**
     * Kiểm tra bằng 0
     */
    public boolean isZero() {
        return this.value.compareTo(BigDecimal.ZERO) == 0;
    }
    
    /**
     * Kiểm tra dương
     */
    public boolean isPositive() {
        return this.value.compareTo(BigDecimal.ZERO) > 0;
    }
    
    /**
     * Kiểm tra âm
     */
    public boolean isNegative() {
        return this.value.compareTo(BigDecimal.ZERO) < 0;
    }
    
    /**
     * Kiểm tra lớn hơn hoặc bằng
     */
    public boolean isGreaterThanOrEqual(Money other) {
        return this.compareTo(other) >= 0;
    }
    
    /**
     * Kiểm tra nhỏ hơn hoặc bằng
     */
    public boolean isLessThanOrEqual(Money other) {
        return this.compareTo(other) <= 0;
    }
    
    /**
     * Min
     */
    public Money min(Money other) {
        return this.compareTo(other) <= 0 ? this : other;
    }
    
    /**
     * Max
     */
    public Money max(Money other) {
        return this.compareTo(other) >= 0 ? this : other;
    }

    /**
     * Get value (compatibility method)
     */
    public BigDecimal getValue() {
        return this.value;
    }

    /**
     * Get amount (alias for getValue for compatibility)
     */
    public BigDecimal getAmount() {
        return this.value;
    }
    
    @Override
    public int compareTo(Money other) {
        if (other == null) return 1;
        return this.value.compareTo(other.value);
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Money money = (Money) o;
        return Objects.equals(value, money.value);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(value);
    }
    
    /**
     * Lấy giá trị nhỏ hơn giữa hai Money objects
     * @param a Money object thứ nhất
     * @param b Money object thứ hai
     * @return Money object có giá trị nhỏ hơn
     */
    public static Money min(Money a, Money b) {
        if (a == null && b == null) {
            return null;
        }
        if (a == null) {
            return b;
        }
        if (b == null) {
            return a;
        }
        return a.value.compareTo(b.value) <= 0 ? a : b;
    }

    /**
     * Lấy giá trị lớn hơn giữa hai Money objects
     * @param a Money object thứ nhất
     * @param b Money object thứ hai
     * @return Money object có giá trị lớn hơn
     */
    public static Money max(Money a, Money b) {
        if (a == null && b == null) {
            return null;
        }
        if (a == null) {
            return b;
        }
        if (b == null) {
            return a;
        }
        return a.value.compareTo(b.value) >= 0 ? a : b;
    }

    @Override
    public String toString() {
        return value.toPlainString();
    }
}
