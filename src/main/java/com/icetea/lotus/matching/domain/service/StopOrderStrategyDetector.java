package com.icetea.lotus.matching.domain.service;

/**
 * <AUTHOR> nguyen
 */

import com.icetea.lotus.matching.domain.enums.OrderDirection;
import com.icetea.lotus.matching.domain.enums.StopOrderStrategy;
import com.icetea.lotus.matching.domain.enums.StopOrderType;
import com.icetea.lotus.matching.domain.valueobject.Money;
import com.icetea.lotus.matching.domain.valueobject.Symbol;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * Intelligent Strategy Detector
 * Tự động phát hiện strategy phù hợp dựa trên:
 * 1. Market conditions (volatility, trend)
 * 2. Order characteristics (price distance, direction)
 * 3. Stop order type patterns
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class StopOrderStrategyDetector {
    
    // Thresholds for strategy detection
    
    /**
     * Tự động detect strategy dựa trên market conditions và order characteristics
     * 
     * @param stopOrderType Loại stop order
     * @param direction Hướng order (BUY/SELL)
     * @param triggerPrice Giá trigger
     * @param currentPrice Giá hiện tại
     * @return Strategy phù hợp nhất
     */
    public static StopOrderStrategy detectOptimalStrategy(
            StopOrderType stopOrderType,
            OrderDirection direction, 
            Money triggerPrice,
            Money currentPrice) {
        
        if (triggerPrice == null || currentPrice == null) {
            return StopOrderStrategy.TRADITIONAL; // Fallback
        }
        
        // Strategy detection logic with price context for STOP_MARKET
        return detectStrategy(stopOrderType, direction, triggerPrice, currentPrice);
    }
    
    /**
     * Core strategy detection logic
     * Detect strategy based on stop order type and price context
     */
    private static StopOrderStrategy detectStrategy(
            StopOrderType stopOrderType,
            OrderDirection direction,
            Money triggerPrice,
            Money currentPrice) {

        // STRATEGY DETECTION based on stop order type
        return switch (stopOrderType) {
            case BUY_DIP_MARKET, BUY_DIP_LIMIT, SELL_RALLY_MARKET, SELL_RALLY_LIMIT ->
                // These types explicitly indicate BUY_DIP_SELL_RALLY strategy
                    StopOrderStrategy.BUY_DIP_SELL_RALLY;

            case STOP_MARKET, STOP_LIMIT, TAKE_PROFIT, STOP_LOSS ->
                // For STOP_MARKET, detect strategy based on price relationship and direction
                    detectStopOrderStrategy(direction, triggerPrice, currentPrice);
            default ->
                // Default to TRADITIONAL for all other cases (STOP_LIMIT, etc.)
                    StopOrderStrategy.TRADITIONAL;
        };
    }

    /**
     * Detect strategy for STOP_MARKET orders based on price relationship and direction
     */
    private static StopOrderStrategy detectStopOrderStrategy(
            OrderDirection direction,
            Money triggerPrice,
            Money currentPrice) {

        if (triggerPrice == null || currentPrice == null) {
            return StopOrderStrategy.TRADITIONAL; // Fallback
        }

        int priceComparison = currentPrice.compareTo(triggerPrice);

        return switch (direction) {
            case BUY -> {
                if (priceComparison > 0) {
                    // Current > Trigger: BUY khi giá giảm xuống trigger → BUY_DIP
                    yield StopOrderStrategy.BUY_DIP_SELL_RALLY;
                } else if (priceComparison < 0) {
                    // Current < Trigger: BUY khi giá tăng lên trigger → BREAKOUT
                    yield StopOrderStrategy.BREAKOUT_BREAKDOWN;
                } else {
                    // Current = Trigger: Default to TRADITIONAL
                    yield StopOrderStrategy.TRADITIONAL;
                }
            }
            case SELL -> {
                if (priceComparison < 0) {
                    // Current < Trigger: SELL khi giá tăng lên trigger → SELL_RALLY
                    yield StopOrderStrategy.BUY_DIP_SELL_RALLY;
                } else if (priceComparison > 0) {
                    // Current > Trigger: SELL khi giá giảm xuống trigger → BREAKDOWN
                    yield StopOrderStrategy.BREAKOUT_BREAKDOWN;
                } else {
                    // Current = Trigger: Default to TRADITIONAL
                    yield StopOrderStrategy.TRADITIONAL;
                }
            }
        };
    }

    /**
     * Get strategy description for logging/debugging
     */
    public static String getStrategyReason(
            StopOrderType stopOrderType,
            OrderDirection direction,
            Money triggerPrice,
            Money currentPrice,
            Symbol symbol,
            StopOrderStrategy detectedStrategy) {

        return String.format(
            "Strategy: %s | Type: %s | Direction: %s | Trigger: %s | Current: %s | Symbol: %s",
            detectedStrategy.name(),
            stopOrderType.name(),
            direction.name(),
            triggerPrice.getValue(),
            currentPrice.getValue(),
            symbol.getValue()
        );
    }

}
