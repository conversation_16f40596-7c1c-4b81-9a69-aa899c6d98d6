package com.icetea.lotus.matching.domain.enums;

/**
 * Enum chuyên dùng cho trạng thái lệnh spot trading
 * Tương thích hoàn toàn với ExchangeOrderStatus trong Exchange module
 *
 * <AUTHOR> nguyen
 */
public enum SpotOrderStatus {

    /**
     * Đang giao dịch - TRADING trong Exchange
     * Lệnh đang hoạt động trong order book, chờ khớp
     */
    TRADING("TRADING", "Trading", "Đang giao dịch"),

    /**
     * Đã hoàn thành - COMPLETED trong Exchange
     * Lệnh đã được khớp hoàn toàn
     */
    COMPLETED("COMPLETED", "Completed", "Đã hoàn thành"),

    /**
     * Đã hủy - CANCELED trong Exchange
     * Lệnh đã bị hủy bởi người dùng hoặc hệ thống
     */
    CANCELED("CANCELED", "Canceled", "Đã hủy"),

    /**
     * Hết thời gian - OVERTIMED trong Exchange
     * Lệnh đã hết thời gian hiệu lực
     */
    OVERTIMED("OVERTIMED", "Overtimed", "Hết thời gian"),

    /**
     * Đã kích hoạt - TRIGGER trong Exchange
     * Stop order đã được kích hoạt và chuyển thành regular order
     */
    TRIGGER("TRIGGER", "Triggered", "Đã kích hoạt"),

    /**
     * Khớp một phần - PARTIAL_FILLED trong Exchange
     * Lệnh đã được khớp một phần, vẫn còn amount chưa khớp
     */
    PARTIAL_FILLED("PARTIAL_FILLED", "Partially Filled", "Khớp một phần");

    private final String exchangeCode;
    private final String englishName;
    private final String vietnameseName;

    SpotOrderStatus(String exchangeCode, String englishName, String vietnameseName) {
        this.exchangeCode = exchangeCode;
        this.englishName = englishName;
        this.vietnameseName = vietnameseName;
    }

    public String getExchangeCode() {
        return exchangeCode;
    }

    public String getEnglishName() {
        return englishName;
    }

    public String getVietnameseName() {
        return vietnameseName;
    }

    /**
     * Kiểm tra lệnh có đang active không (có thể khớp)
     */
    public boolean isActive() {
        return this == TRADING || this == PARTIAL_FILLED;
    }

    /**
     * Kiểm tra lệnh đã hoàn thành chưa (không thể khớp thêm)
     */
    public boolean isCompleted() {
        return this == COMPLETED || this == CANCELED || this == OVERTIMED;
    }

    /**
     * Kiểm tra lệnh có thể bị hủy không
     */
    public boolean isCancellable() {
        return this == TRADING || this == TRIGGER;
    }

    /**
     * Kiểm tra có phải trạng thái final không (không thể thay đổi)
     */
    public boolean isFinal() {
        return isCompleted();
    }

    /**
     * Kiểm tra có phải stop order đã trigger không
     */
    public boolean isTriggered() {
        return this == TRIGGER;
    }

    /**
     * Chuyển đổi từ Exchange OrderStatus string sang SpotOrderStatus
     */
    public static SpotOrderStatus fromExchangeCode(String exchangeCode) {
        if (exchangeCode == null || exchangeCode.trim().isEmpty()) {
            throw new IllegalArgumentException("Exchange code không được null hoặc rỗng");
        }

        String code = exchangeCode.trim().toUpperCase();

        // Handle special mappings for Exchange compatibility
        switch (code) {
            case "PENDING", "NEW":
                return TRADING; // NEW orders are considered TRADING in our system
            case "FILLED":
                return COMPLETED;
            case "CANCELLED":
                return CANCELED;
            case "EXPIRED":
                return OVERTIMED;
            case "TRIGGERED":
                return TRIGGER;
            case "PARTIAL_FILLED":
                return PARTIAL_FILLED;
            default:
        }

        // Try exact match with our enum values
        for (SpotOrderStatus status : values()) {
            if (status.exchangeCode.equals(code)) {
                return status;
            }
        }

        throw new IllegalArgumentException("Không tìm thấy SpotOrderStatus cho exchange code: " + exchangeCode);
    }

    /**
     * Chuyển đổi từ OrderStatus sang SpotOrderStatus
     */
    public static SpotOrderStatus fromOrderStatus(OrderStatus orderStatus) {
        if (orderStatus == null) {
            throw new IllegalArgumentException("OrderStatus không được null");
        }

        return switch (orderStatus) {
            case NEW, PENDING -> TRADING;
            case PARTIALLY_FILLED -> PARTIAL_FILLED; // Partially filled có trạng thái riêng
            case FILLED -> COMPLETED;
            case CANCELLED -> CANCELED;
            case EXPIRED -> OVERTIMED;
            case TRIGGERED -> TRIGGER;
            case REJECTED -> CANCELED; // Rejected orders map to CANCELED
        };
    }

    /**
     * Chuyển đổi sang OrderStatus
     */
    public OrderStatus toOrderStatus() {
        return switch (this) {
            case TRADING -> OrderStatus.PENDING;
            case PARTIAL_FILLED -> OrderStatus.PARTIALLY_FILLED;
            case COMPLETED -> OrderStatus.FILLED;
            case CANCELED -> OrderStatus.CANCELLED;
            case OVERTIMED -> OrderStatus.EXPIRED;
            case TRIGGER -> OrderStatus.TRIGGERED;
        };
    }

    /**
     * Lấy mô tả đầy đủ
     */
    public String getFullDescription() {
        return String.format("%s (%s) - %s", englishName, exchangeCode, vietnameseName);
    }

    /**
     * Kiểm tra status transition có hợp lệ không
     */
    public boolean canTransitionTo(SpotOrderStatus newStatus) {
        if (newStatus == null) {
            return false;
        }

        return switch (this) {
            case TRADING ->
                    newStatus == COMPLETED || newStatus == CANCELED || newStatus == OVERTIMED || newStatus == PARTIAL_FILLED;
            case PARTIAL_FILLED -> newStatus == COMPLETED || newStatus == CANCELED || newStatus == OVERTIMED;
            case TRIGGER -> newStatus == TRADING || newStatus == CANCELED;
            case COMPLETED, CANCELED, OVERTIMED -> false; // Final states cannot transition
        };
    }

    /**
     * Lấy các status có thể transition tới
     */
    public SpotOrderStatus[] getPossibleTransitions() {
        return switch (this) {
            case TRADING -> new SpotOrderStatus[]{COMPLETED, CANCELED, OVERTIMED, PARTIAL_FILLED};
            case PARTIAL_FILLED -> new SpotOrderStatus[]{COMPLETED, CANCELED, OVERTIMED};
            case TRIGGER -> new SpotOrderStatus[]{TRADING, CANCELED};
            case COMPLETED, CANCELED, OVERTIMED -> new SpotOrderStatus[]{}; // No transitions
        };
    }

    /**
     * Lấy priority cho processing (số càng nhỏ càng ưu tiên)
     */
    public int getProcessingPriority() {
        return switch (this) {
            case TRIGGER -> 1; // Ưu tiên cao nhất - cần xử lý ngay
            case TRADING -> 2; // Ưu tiên cao - đang active
            case PARTIAL_FILLED -> 2; // Ưu tiên cao - vẫn đang active
            case COMPLETED -> 3; // Ưu tiên thấp - đã xong
            case CANCELED, OVERTIMED -> 4; // Ưu tiên thấp nhất - đã kết thúc
        };
    }

    @Override
    public String toString() {
        return exchangeCode;
    }
}
