package com.icetea.lotus.matching.domain.valueobject;

/**
 * <AUTHOR> nguyen
 */

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * OrderId value object
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderId {
    
    private String value;
    
    public static OrderId of(String value) {
        return new OrderId(value);
    }

    // Explicit getValue method for compilation
    public String getValue() {
        return value;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        OrderId orderId = (OrderId) o;
        return Objects.equals(value, orderId.value);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(value);
    }
    
    @Override
    public String toString() {
        return value;
    }
}
