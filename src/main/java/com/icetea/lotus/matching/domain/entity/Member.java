package com.icetea.lotus.matching.domain.entity;

/**
 * <AUTHOR> nguyen
 */

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.Objects;

/**
 * Member entity cho matching engine
 * Simplified version - chỉ chứa fields cần thiết cho matching
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Member {
    
    /**
     * Member ID
     */
    private Long id;
    
    /**
     * Member username
     */
    private String username;
    
    /**
     * Member email
     */
    private String email;
    
    /**
     * Member status
     */
    private String status;
    
    /**
     * Created timestamp
     */
    private Instant createdAt;
    
    /**
     * Updated timestamp
     */
    private Instant updatedAt;
    
    /**
     * Check if member is active
     */
    public boolean isActive() {
        return "ACTIVE".equals(status);
    }
    
    /**
     * Check if member is suspended
     */
    public boolean isSuspended() {
        return "SUSPENDED".equals(status);
    }
    
    // ===== COMPATIBILITY METHODS =====
    
    /**
     * Get ID (compatibility method)
     */
    public Long getId() {
        return this.id;
    }
    
    /**
     * Get username (compatibility method)
     */
    public String getUsername() {
        return this.username;
    }
    
    /**
     * Get email (compatibility method)
     */
    public String getEmail() {
        return this.email;
    }
    
    /**
     * Get status (compatibility method)
     */
    public String getStatus() {
        return this.status;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Member member = (Member) o;
        return Objects.equals(id, member.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return String.format("Member{id=%d, username='%s', email='%s', status='%s'}", 
                id, username, email, status);
    }
}
