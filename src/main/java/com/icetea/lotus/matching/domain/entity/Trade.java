package com.icetea.lotus.matching.domain.entity;

/**
 * <AUTHOR> nguyen
 */

import com.icetea.lotus.matching.domain.enums.OrderDirection;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Objects;

/**
 * Trade entity cho matching engine
 * Đ<PERSON>i diện cho một giao dịch được tạo ra từ việc khớp lệnh
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class Trade {

    /**
     * Trade ID
     */
    private Long tradeId;

    /**
     * Symbol - cặp giao dịch
     * Get symbol (compatibility method)
     */
    private String symbol;

    /**
     * Giá khớp
     * Get price
     */
    private BigDecimal price;

    /**
     * <PERSON>h<PERSON><PERSON> lượng khớp
     */
    private BigDecimal volume;

    /**
     * Thời gian khớp
     * Get timestamp
     */
    private Instant tradeTime;

    /**
     * Taker order ID
     */
    private String takerOrderId;

    /**
     * Maker order ID
     */
    private String makerOrderId;

    /**
     * Taker order Direction
     */
    private OrderDirection takerDirection;

    /**
     * Maker order Direction
     */
    private OrderDirection makerDirection;

    /**
     * Taker member ID
     */
    private Long takerMemberId;

    /**
     * Maker member ID
     */
    private Long makerMemberId;

    /**
     * Taker fee
     */
    private BigDecimal takerFee;

    /**
     * Maker fee
     */
    private BigDecimal makerFee;

    /**
     * Size
     */
    private BigDecimal size;

    // ===== COMPATIBILITY METHODS =====

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Trade trade = (Trade) o;
        return Objects.equals(tradeId, trade.tradeId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(tradeId);
    }

    @Override
    public String toString() {
        return String.format("Trade{id=%s, symbol=%s, price=%s, quantity=%s, maker=%s, taker=%s, timestamp=%s}",
                tradeId, symbol, price, volume, makerOrderId, takerOrderId, tradeTime);
    }
}
