package com.icetea.lotus.matching.domain.entity;

/**
 * <AUTHOR> nguyen
 */

import com.icetea.lotus.matching.domain.enums.OrderDirection;
import com.icetea.lotus.matching.domain.enums.OrderStatus;
import com.icetea.lotus.matching.domain.enums.OrderType;
import com.icetea.lotus.matching.domain.valueobject.Money;
import com.icetea.lotus.matching.domain.valueobject.OrderId;
import com.icetea.lotus.matching.domain.valueobject.Symbol;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Order entity cho matching engine
 * Simplified version - chỉ chứa fields cần thiết cho matching
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class Order {
    
    /**
     * Order ID
     * -- GETTER --
     *  Get order ID (compatibility method)

     */
    private OrderId orderId;
    
    /**
     * Member ID - người đặt lệnh
     * -- GETTER --
     *  Get member ID (compatibility method)

     */
    private Long memberId;
    
    /**
     * Symbol - cặp giao dịch
     * -- GETTER --
     *  Get symbol (compatibility method)

     */
    private Symbol symbol;
    
    /**
     * Giá lệnh
     * -- GETTER --
     *  Get price (compatibility method)

     */
    private Money price;
    
    /**
     * Khối lượng lệnh
     * -- GETTER --
     *  Get quantity (compatibility method)

     */
    private Money size;
    
    /**
     * Khối lượng đã khớp
     */
    @Builder.Default
    private Money filledSize = new Money("0");
    
    /**
     * Hướng lệnh (BUY/SELL)
     *  Get direction (compatibility method)
     */
    private OrderDirection direction;
    
    /**
     * Loại lệnh (MARKET/LIMIT/STOP_LIMIT/STOP_MARKET)
     *  Get type (compatibility method)
     */
    private OrderType type;
    
    /**
     * Trạng thái lệnh
     *  Get status (compatibility method)
     */
    private OrderStatus status;
    
    /**
     * Thời gian tạo lệnh
     *  Get timestamp (compatibility method)
     */
    private Instant timestamp;
    
    /**
     * Leverage (đòn bẩy) - cho futures trading
     */
    private BigDecimal leverage;
    
    /**
     * Stop price - cho stop orders
     */
    private Money stopPrice;
    
    /**
     * Time in force
     */
    private String timeInForce;
    
    /**
     * Reduce only flag
     */
    private Boolean reduceOnly;
    
    /**
     * Post only flag
     */
    private Boolean postOnly;

    private Money fee;

    private String baseSymbol;

    private Long positionId;

    private Boolean isSTP;

    /**
     * Order metadata - additional information
     *  Get metadata
     */
    @Builder.Default
    private Map<String, String> metadata = new HashMap<>();
    
    /**
     * Kiểm tra xem lệnh có phải là lệnh mua không
     */
    public boolean isBuyOrder() {
        return OrderDirection.BUY.equals(direction);
    }
    
    /**
     * Kiểm tra xem lệnh có phải là lệnh bán không
     */
    public boolean isSellOrder() {
        return OrderDirection.SELL.equals(direction);
    }
    
    /**
     * Kiểm tra xem lệnh có phải là market order không
     */
    public boolean isMarketOrder() {
        return OrderType.MARKET.equals(type);
    }
    
    /**
     * Kiểm tra xem lệnh có phải là limit order không
     */
    public boolean isLimitOrder() {
        return OrderType.LIMIT.equals(type);
    }
    
    /**
     * Lấy khối lượng còn lại chưa khớp
     */
    public Money getRemainingQuantity() {
        if (filledSize == null) {
            return size;
        }
        return size.subtract(filledSize);
    }

    /**
     * Cập nhật khối lượng đã khớp
     */
    public void addFilledQuantity(Money additionalFilled) {
        if (additionalFilled == null || additionalFilled.isZero()) {
            return;
        }

        if (this.filledSize == null) {
            this.filledSize = additionalFilled;
        } else {
            this.filledSize = this.filledSize.add(additionalFilled);
        }

        // Update order status based on filled quantity
        updateStatusBasedOnFilled();
    }

    /**
     * Cập nhật trạng thái lệnh dựa trên khối lượng đã khớp
     */
    private void updateStatusBasedOnFilled() {
        if (isFullyFilled()) {
            this.status = OrderStatus.FILLED;
        } else if (filledSize != null && filledSize.isPositive()) {
            this.status = OrderStatus.PARTIALLY_FILLED;
        }
    }
    
    /**
     * Kiểm tra xem lệnh đã được khớp hoàn toàn chưa
     */
    public boolean isFullyFilled() {
        return filledSize != null &&
               filledSize.compareTo(size) >= 0;
    }
    
    /**
     * Kiểm tra xem lệnh có thể khớp với lệnh khác không
     */
    public boolean canMatchWith(Order other) {
        if (other == null || this.equals(other)) {
            return false;
        }

        // Phải là cùng symbol
        if (!Objects.equals(this.symbol, other.symbol)) {
            return false;
        }

        // Phải là hướng ngược nhau
        if (this.direction.equals(other.direction)) {
            return false;
        }

        // Không thể tự khớp với chính mình (same member)
        return !Objects.equals(this.memberId, other.memberId);
    }

    // ===== COMPATIBILITY METHODS =====
    // These methods provide compatibility with future-core naming conventions

    /**
     * Get created at timestamp (alias for getTimestamp)
     */
    public Instant getCreatedAt() {
        return this.timestamp;
    }

    /**
     * Fill quantity (update filled quantity)
     */
    public void fillQuantity(Money quantity) {
        addFilledQuantity(quantity);
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Order order = (Order) o;
        return Objects.equals(orderId, order.orderId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(orderId);
    }
    
    @Override
    public String toString() {
        return String.format("Order{id=%s, symbol=%s, direction=%s, type=%s, price=%s, quantity=%s, status=%s}", 
                orderId, symbol, direction, type, price, size, status);
    }
}
