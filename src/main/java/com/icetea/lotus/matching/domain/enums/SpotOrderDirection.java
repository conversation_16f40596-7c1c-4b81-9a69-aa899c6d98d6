package com.icetea.lotus.matching.domain.enums;

/**
 * Enum chuyên dùng cho hướng lệnh spot trading
 * Tương thích hoàn toàn với ExchangeOrderDirection trong Exchange module
 * 
 * <AUTHOR> nguyen
 */
public enum SpotOrderDirection {
    
    /**
     * Lệnh mua - BUY trong Exchange
     * Mua base currency bằng quote currency
     */
    BUY("BUY", "Buy Order", "Lệnh mua"),
    
    /**
     * Lệnh bán - SELL trong Exchange
     * Bán base currency lấy quote currency
     */
    SELL("SELL", "Sell Order", "Lệnh bán");
    
    private final String exchangeCode;
    private final String englishName;
    private final String vietnameseName;
    
    SpotOrderDirection(String exchangeCode, String englishName, String vietnameseName) {
        this.exchangeCode = exchangeCode;
        this.englishName = englishName;
        this.vietnameseName = vietnameseName;
    }
    
    public String getExchangeCode() {
        return exchangeCode;
    }
    
    public String getEnglishName() {
        return englishName;
    }
    
    public String getVietnameseName() {
        return vietnameseName;
    }
    
    /**
     * Lấy hướng ngược lại
     */
    public SpotOrderDirection getOpposite() {
        return this == BUY ? SELL : BUY;
    }
    
    /**
     * Kiểm tra có phải lệnh mua không
     */
    public boolean isBuy() {
        return this == BUY;
    }
    
    /**
     * Kiểm tra có phải lệnh bán không
     */
    public boolean isSell() {
        return this == SELL;
    }
    
    /**
     * Chuyển đổi từ Exchange OrderDirection string sang SpotOrderDirection
     */
    public static SpotOrderDirection fromExchangeCode(String exchangeCode) {
        if (exchangeCode == null || exchangeCode.trim().isEmpty()) {
            throw new IllegalArgumentException("Exchange code không được null hoặc rỗng");
        }
        
        String code = exchangeCode.trim().toUpperCase();
        for (SpotOrderDirection direction : values()) {
            if (direction.exchangeCode.equals(code)) {
                return direction;
            }
        }
        
        throw new IllegalArgumentException("Không tìm thấy SpotOrderDirection cho exchange code: " + exchangeCode);
    }
    
    /**
     * Chuyển đổi từ OrderDirection sang SpotOrderDirection
     */
    public static SpotOrderDirection fromOrderDirection(SpotOrderDirection orderDirection) {
        if (orderDirection == null) {
            throw new IllegalArgumentException("OrderDirection không được null");
        }
        
        return switch (orderDirection) {
            case BUY -> BUY;
            case SELL -> SELL;
        };
    }
    
    /**
     * Chuyển đổi sang OrderDirection
     */
    public OrderDirection toOrderDirection() {
        return switch (this) {
            case BUY -> OrderDirection.BUY;
            case SELL -> OrderDirection.SELL;
        };
    }
    
    /**
     * Lấy mô tả đầy đủ
     */
    public String getFullDescription() {
        return String.format("%s (%s) - %s", englishName, exchangeCode, vietnameseName);
    }
    
    /**
     * Kiểm tra tính hợp lệ với order type
     */
    public boolean isValidForOrderType(SpotOrderType orderType) {
        // Tất cả directions đều hỗ trợ tất cả order types trong spot trading
        return orderType != null;
    }
    
    /**
     * Lấy currency được tác động chính
     * @param symbol Trading pair symbol (e.g., "BTC/USDT")
     * @return Currency code
     */
    public String getPrimaryCurrency(String symbol) {
        String[] currencies = parseSymbol(symbol);
        return this == BUY ? currencies[0] : currencies[1]; // BUY: base currency, SELL: quote currency
    }
    
    /**
     * Lấy currency được tác động phụ
     * @param symbol Trading pair symbol (e.g., "BTC/USDT")
     * @return Currency code
     */
    public String getSecondaryCurrency(String symbol) {
        String[] currencies = parseSymbol(symbol);
        return this == BUY ? currencies[1] : currencies[0]; // BUY: quote currency, SELL: base currency
    }
    
    /**
     * Parse symbol để lấy base và quote currencies
     */
    private String[] parseSymbol(String symbol) {
        if (symbol == null || symbol.trim().isEmpty()) {
            throw new IllegalArgumentException("Symbol không được null hoặc rỗng");
        }
        
        if (symbol.contains("/")) {
            return symbol.split("/");
        } else if (symbol.contains("-")) {
            return symbol.split("-");
        } else {
            // Handle concatenated format like BTCUSDT
            String[] commonQuotes = {"USDT", "BTC", "ETH", "BNB", "BUSD"};
            
            for (String quote : commonQuotes) {
                if (symbol.endsWith(quote)) {
                    String base = symbol.substring(0, symbol.length() - quote.length());
                    return new String[]{base, quote};
                }
            }
            
            throw new IllegalArgumentException("Không thể parse symbol: " + symbol);
        }
    }
    
    @Override
    public String toString() {
        return exchangeCode;
    }
}
