package com.icetea.lotus.matching.domain.enums;

/**
 * <AUTHOR> nguyen
 */

/**
 * Trạng thái lệnh giao dịch
 */
public enum OrderStatus {
    /**
     * L<PERSON><PERSON> mới, chưa được xử lý
     */
    NEW,
    
    /**
     * Lệnh đang chờ khớp
     */
    PENDING,
    
    /**
     * Lệnh đã khớp một phần
     */
    PARTIALLY_FILLED,
    
    /**
     * Lệnh đã khớp hoàn toàn
     */
    FILLED,
    
    /**
     * Lệnh đã bị hủy
     */
    CANCELLED,
    
    /**
     * Lệnh bị từ chối
     */
    REJECTED,
    
    /**
     * Lệnh đã hết hạn
     */
    EXPIRED,

    /**
     * Lệnh đã kích hoạt (cho stop orders)
     */
    TRIGGERED;
    
    /**
     * Kiểm tra lệnh có đang active không (có thể khớp)
     */
    public boolean isActive() {
        return this == NEW || this == PENDING || this == PARTIALLY_FILLED;
    }
    
    /**
     * <PERSON>ểm tra lệnh đã hoàn thành chưa (không thể khớp thêm)
     */
    public boolean isCompleted() {
        return this == FILLED || this == CANCELLED || this == REJECTED || this == EXPIRED || this == TRIGGERED;
    }
    
    /**
     * Kiểm tra lệnh có thể hủy không
     */
    public boolean isCancellable() {
        return this == NEW || this == PENDING || this == PARTIALLY_FILLED;
    }
}
