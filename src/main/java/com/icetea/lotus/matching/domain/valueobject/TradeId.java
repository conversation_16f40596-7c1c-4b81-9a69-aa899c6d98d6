package com.icetea.lotus.matching.domain.valueobject;

/**
 * <AUTHOR> nguyen
 */

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * TradeId value object
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TradeId {
    
    private String value;
    
    public static TradeId of(String value) {
        return new TradeId(value);
    }

    /**
     * Generate new TradeId with UUID
     */
    public static TradeId generate() {
        return new TradeId(java.util.UUID.randomUUID().toString());
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TradeId tradeId = (TradeId) o;
        return Objects.equals(value, tradeId.value);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(value);
    }
    
    @Override
    public String toString() {
        return value;
    }
}
