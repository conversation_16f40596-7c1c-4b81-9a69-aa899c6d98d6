package com.icetea.lotus.matching.domain.enums;

/**
 * <AUTHOR> nguyen
 */

/**
 * Stop Order Status Enum
 * 
 * Trạng thái của stop orders trong matching engine
 */
public enum StopOrderStatus {
    
    /**
     * Stop order mới được tạo, chưa active
     */
    NEW,
    
    /**
     * Stop order đang active, chờ trigger
     */
    ACTIVE,
    
    /**
     * Stop order đã được trigger và chuyển thành regular order
     */
    TRIGGERED,
    
    /**
     * Stop order bị hủy
     */
    CANCELLED,
    
    /**
     * Stop order bị reject do validation lỗi
     */
    REJECTED;
    
    /**
     * Kiểm tra xem stop order có đang active không
     * 
     * @return true nếu stop order đang active
     */
    public boolean isActive() {
        return this == ACTIVE;
    }
    
    /**
     * Kiểm tra xem stop order có thể bị hủy không
     * 
     * @return true nếu có thể hủy
     */
    public boolean isCancellable() {
        return this == NEW || this == ACTIVE;
    }
    
    /**
     * Kiểm tra xem stop order đã kết thúc chưa
     * 
     * @return true nếu đã kết thúc (triggered, cancelled, rejected)
     */
    public boolean isTerminal() {
        return this == TRIGGERED || this == CANCELLED || this == REJECTED;
    }
}
