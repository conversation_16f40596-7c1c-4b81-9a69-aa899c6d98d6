package com.icetea.lotus.matching.domain.enums;

/**
 * <AUTHOR> nguyen
 */

/**
 * <PERSON><PERSON><PERSON> lệnh giao dịch
 */
public enum OrderType {
    /**
     * Lệnh thị trường - khớp ngay lập tức với giá tốt nhất
     */
    MARKET,
    
    /**
     * Lệnh giới hạn - chỉ khớp với giá chỉ định hoặc tốt hơn
     */
    LIMIT,

    /**
     * Lệnh stop - lệnh stop cơ bản
     */
    STOP,

    /**
     * Lệnh stop limit - kích hoạt khi giá đạt stop price, sau đó trở thành limit order
     */
    STOP_LIMIT,

    /**
     * Lệnh stop market - kích hoạt khi giá đạt stop price, sau đó trở thành market order
     */
    STOP_MARKET,

    STOP_LOSS,

    TAKE_PROFIT;
    
    /**
     * Kiểm tra có phải market order không
     */
    public boolean isMarket() {
        return this == MARKET || this == STOP_MARKET;
    }
    
    /**
     * Kiểm tra có phải limit order không
     */
    public boolean isLimit() {
        return this == LIMIT || this == STOP_LIMIT;
    }
    
    /**
     * Kiểm tra có phải stop order không
     */
    public boolean isStop() {
        return this == STOP || this == STOP_LIMIT || this == STOP_MARKET;
    }
}
