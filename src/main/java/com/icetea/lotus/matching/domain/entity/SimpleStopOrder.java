package com.icetea.lotus.matching.domain.entity;

/**
 * <AUTHOR> nguyen
 */

import com.icetea.lotus.matching.domain.enums.OrderDirection;
import com.icetea.lotus.matching.domain.enums.StopOrderStatus;
import com.icetea.lotus.matching.domain.enums.StopOrderStrategy;
import com.icetea.lotus.matching.domain.enums.StopOrderType;
import com.icetea.lotus.matching.domain.valueobject.Money;
import com.icetea.lotus.matching.domain.valueobject.OrderId;
import com.icetea.lotus.matching.domain.valueobject.Symbol;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.StampedLock;

/**
 * Simple Stop Order với Sign-Based Detection
 * Ý tưởng: <PERSON><PERSON><PERSON> d<PERSON><PERSON> củ<PERSON> (currentPrice - triggerPrice) lúc đặt lệnh
 * Trigger khi dấu thay đổi → Đơn giản và universal cho tất cả stop types
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SimpleStopOrder {
    
    /**
     * Order ID
     */
    private OrderId orderId;
    
    /**
     * Symbol - cặp giao dịch
     */
    private Symbol symbol;

    /**
     * Member - người đặt lệnh
     */
    private Member member;

    /**
     * Hướng lệnh (BUY/SELL)
     */
    private OrderDirection direction;
    
    /**
     * Loại stop order
     */
    private StopOrderType stopOrderType;

    /**
     * Chiến lược stop order (mặc định TRADITIONAL cho backward compatibility)
     */
    private StopOrderStrategy strategy;
    
    /**
     * Giá kích hoạt
     */
    private Money triggerPrice;
    
    /**
     * Giá thực thi (cho Stop Limit)
     */
    private Money executionPrice;
    
    /**
     * Khối lượng
     */
    private Money quantity;

    /**
     * Fee
     */
    private Money fee;
    
    /**
     * Trạng thái stop order
     */
    private StopOrderStatus status;

    /**
     * Leverage (đòn bẩy) - cho futures trading
     */
    private BigDecimal leverage;

    private Long positionId;

    /**
     * Thời gian tạo
     */
    private Long timestamp;

    // OPTIMIZATION: Price gap handling và event-driven triggers
    private final AtomicBoolean priceGapDetected = new AtomicBoolean(false);
    private final StampedLock triggerLock = new StampedLock();
    private Money lastCheckedPrice;
    
    /**
     * SIGN-BASED DETECTION FIELDS
     */
    
    /**
     * Giá hiện tại lúc đặt lệnh (để tính initial sign)
     */
    private Money initialCurrentPrice;
    
    /**
     * Dấu ban đầu của (initialCurrentPrice - triggerPrice)
     * true = positive (currentPrice >= triggerPrice)
     * false = negative (currentPrice < triggerPrice)
     */
    private Boolean initialSign;
    
    /**
     * Khởi tạo sign-based detection khi đặt lệnh
     *
     * @param currentPrice Giá hiện tại lúc đặt lệnh
     */
    public void initializeSignDetection(Money currentPrice) {
        this.initialCurrentPrice = currentPrice;

        if (currentPrice != null && triggerPrice != null) {
            BigDecimal diff = currentPrice.getValue().subtract(triggerPrice.getValue());
            this.initialSign = diff.compareTo(BigDecimal.ZERO) >= 0;
        }
    }

    /**
     * Set strategy cho stop order (mặc định TRADITIONAL nếu null)
     */
    public void setStrategy(StopOrderStrategy strategy) {
        this.strategy = (strategy != null) ? strategy : StopOrderStrategy.TRADITIONAL;
    }

    /**
     * Get strategy hiện tại (mặc định TRADITIONAL nếu null)
     */
    public StopOrderStrategy getStrategy() {
        return (strategy != null) ? strategy : StopOrderStrategy.TRADITIONAL;
    }
    
    /**
     * Kiểm tra trigger bằng sign-based detection với strategy support
     * Sử dụng StopOrderStrategy để có logic trigger linh hoạt hơn
     *
     * @param currentPrice Giá hiện tại
     * @return true nếu trigger theo strategy
     */
    public boolean isTriggeredBySignChange(Money currentPrice) {
        if (currentPrice == null || triggerPrice == null || initialCurrentPrice == null) {
            return false;
        }

        // Sử dụng strategy để kiểm tra trigger (mặc định TRADITIONAL nếu null)
        StopOrderStrategy currentStrategy = (strategy != null) ? strategy : StopOrderStrategy.TRADITIONAL;

        boolean triggered = false;

        // Kiểm tra trigger theo direction và strategy
        if (direction == OrderDirection.BUY) {
            triggered = currentStrategy.isBuyTriggeredBySignChange(
                currentPrice, triggerPrice, initialCurrentPrice, stopOrderType);
        } else if (direction == OrderDirection.SELL) {
            triggered = currentStrategy.isSellTriggeredBySignChange(
                currentPrice, triggerPrice, initialCurrentPrice, stopOrderType);
        }

        return triggered;
    }

    /**
     * Kiểm tra xem stop order có hợp lệ không
     */
    public boolean isValid() {
        return orderId != null
            && symbol != null
            && direction != null
            && stopOrderType != null
            && triggerPrice != null
            && quantity != null
            && quantity.isPositive();
            // Note: initialSign có thể null nếu chưa initialize
    }

    /**
     * Kiểm tra xem đã initialize sign detection chưa
     */
    public boolean isSignInitialized() {
        return initialSign != null && initialCurrentPrice != null;
    }
    
    /**
     * Get price (alias for executionPrice for compatibility)
     */
    public Money getPrice() {
        return executionPrice;
    }

    /**
     * Get stop price (alias for triggerPrice)
     */
    public Money getStopPrice() {
        return triggerPrice;
    }

    /**
     * Get sign description for debugging
     */
    public String getSignDescription() {
        if (initialCurrentPrice == null || triggerPrice == null || initialSign == null) {
            return "NOT_INITIALIZED";
        }

        BigDecimal diff = initialCurrentPrice.getValue().subtract(triggerPrice.getValue());
        return String.format("%s (%.2f - %.2f = %.2f)",
                Boolean.TRUE.equals(initialSign) ? "POSITIVE" : "NEGATIVE",
            initialCurrentPrice.getValue(),
            triggerPrice.getValue(),
            diff);
    }

    /**
     * OPTIMIZATION: Check if price gap was detected
     */
    public boolean hasPriceGapDetected() {
        return priceGapDetected.get();
    }



    @Override
    public String toString() {
        return String.format("SimpleStopOrder{id=%s, symbol=%s, direction=%s, type=%s, trigger=%s, execution=%s, quantity=%s, status=%s, sign=%s}",
            orderId, symbol, direction, stopOrderType, triggerPrice, executionPrice, quantity, status, getSignDescription());
    }

    /**
     * Activate stop order
     */
    public void activate() {
        this.status = StopOrderStatus.ACTIVE;
    }

    /**
     * Check if stop order is active
     *
     * @return true if active
     */
    public boolean isActive() {
        return status == StopOrderStatus.ACTIVE;
    }

    /**
     * Cancel stop order
     */
    public void cancel() {
        this.status = StopOrderStatus.CANCELLED;
    }
}
