package com.icetea.lotus.matching.domain.event;

import com.icetea.lotus.matching.domain.entity.Order;
import com.icetea.lotus.matching.domain.entity.SimpleStopOrder;
import com.icetea.lotus.matching.domain.valueobject.Money;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.time.LocalDateTime;

/**
 * Domain Event: Stop Order Triggered
 * Published when a stop order is triggered and converted to regular order
 * 
 * <AUTHOR> nguyen
 */
@Getter
@RequiredArgsConstructor
public class StopOrderTriggeredEvent {
    
    /**
     * The original stop order that was triggered
     */
    private final SimpleStopOrder stopOrder;
    
    /**
     * The regular order created from the triggered stop order
     */
    private final Order triggeredOrder;
    
    /**
     * The market price that triggered the stop order
     */
    private final Money currentMarketPrice;
    
    /**
     * When the event occurred
     */
    private final LocalDateTime occurredAt = LocalDateTime.now();
    
    /**
     * Event ID for tracking
     */
    private final String eventId = "STOP_TRIGGER_" + System.currentTimeMillis();
    
    /**
     * Get stop order ID
     */
    public String getStopOrderId() {
        return stopOrder.getOrderId().getValue();
    }
    
    /**
     * Get symbol
     */
    public String getSymbol() {
        return stopOrder.getSymbol().getValue();
    }
    
    /**
     * Get triggered order ID
     */
    public String getTriggeredOrderId() {
        return triggeredOrder.getOrderId().getValue();
    }
    
    /**
     * Get member ID
     */
    public Long getMemberId() {
        return stopOrder.getMember().getId();
    }
    
    /**
     * Get trigger reason
     */
    public String getTriggerReason() {
        return stopOrder.hasPriceGapDetected() ? "PRICE_GAP" : "SIGN_CHANGE";
    }
    
    @Override
    public String toString() {
        return String.format("StopOrderTriggeredEvent{stopOrderId=%s, symbol=%s, triggeredOrderId=%s, currentPrice=%s, reason=%s}",
                getStopOrderId(), getSymbol(), getTriggeredOrderId(), currentMarketPrice, getTriggerReason());
    }
}
