package com.icetea.lotus.matching.domain.enums;

/**
 * <AUTHOR> nguyen
 */

import com.icetea.lotus.matching.domain.valueobject.Money;
import java.math.BigDecimal;

/**
 * Enum định nghĩa các chiến lư<PERSON> cho Stop Orders với Sign-Based Detection
 * Mỗi stop order có thể hoạt động theo 2 kịch bản kh<PERSON>c nhau
 *
 * Sign-Based Detection: Tránh false triggers và missed triggers bằng cách
 * kiểm tra sự thay đổi dấu của (currentPrice - triggerPrice)
 */
public enum StopOrderStrategy {

    /**
     * Traditional Strategy (Default for backward compatibility)
     * Sử dụng logic cũ dựa trên stop order type và direction
     * - Stop Loss: LONG (SELL) trigger khi giá ≤, SHORT (BUY) trigger khi giá ≥
     * - Take Profit: LONG (SELL) trigger khi giá ≥, SHORT (BUY) trigger khi giá ≤
     * - Stop Market: BUY trigger khi giá ≥, SELL trigger khi giá ≤
     * - Stop Limit: BUY trigger khi giá ≤, SELL trigger khi giá ≥
     */
    TRADITIONAL,

    /**
     * Breakout/Breakdown Strategy
     * - BUY orders trigger khi giá vượt LÊN trên trigger price (breakout)
     * - SELL orders trigger khi giá vượt XUỐNG dưới trigger price (breakdown)
     *
     * Use cases:
     * - Momentum trading
     * - Trend following
     * - Support/Resistance breakouts
     */
    BREAKOUT_BREAKDOWN,

    /**
     * Buy Dip/Sell Rally Strategy
     * - BUY orders trigger khi giá giảm XUỐNG trigger price (buy dip)
     * - SELL orders trigger khi giá tăng LÊN trigger price (sell rally)
     *
     * Use cases:
     * - Mean reversion trading
     * - Value investing
     * - Counter-trend trading
     */
    BUY_DIP_SELL_RALLY;
    
    /**
     * Kiểm tra trigger condition cho BUY orders với Sign-Based Detection
     *
     * @param currentPrice Giá hiện tại
     * @param triggerPrice Giá trigger
     * @param initialPrice Giá ban đầu khi đặt lệnh
     * @param stopOrderType Loại stop order (chỉ cần cho TRADITIONAL strategy)
     * @return true nếu BUY order được trigger (dấu đã thay đổi)
     */
    public boolean isBuyTriggeredBySignChange(Money currentPrice, Money triggerPrice, Money initialPrice, StopOrderType stopOrderType) {
        if (currentPrice == null || triggerPrice == null || initialPrice == null) {
            return false;
        }

        // For STOP_MARKET orders and variants, use strategy-specific logic
        if (stopOrderType == StopOrderType.STOP_MARKET ||
            stopOrderType == StopOrderType.BUY_DIP_MARKET ||
            stopOrderType == StopOrderType.SELL_RALLY_MARKET) {
            return checkBuyStopMarketByStrategy(currentPrice, triggerPrice, initialPrice);
        }

        // SPECIAL CASE: Khi currentPrice = triggerPrice, luôn trigger (for non-STOP_MARKET orders with other strategies)
        if (currentPrice.getValue().compareTo(triggerPrice.getValue()) == 0) {
            return true;
        }

        // Tính dấu ban đầu và hiện tại
        boolean initialSign = calculateSign(initialPrice, triggerPrice);
        boolean currentSign = calculateSign(currentPrice, triggerPrice);

        // Kiểm tra sign change theo strategy
        return checkBuySignChange(initialSign, currentSign, stopOrderType);
    }

    /**
     * Kiểm tra trigger condition cho SELL orders với Sign-Based Detection
     *
     * @param currentPrice Giá hiện tại
     * @param triggerPrice Giá trigger
     * @param initialPrice Giá ban đầu khi đặt lệnh
     * @param stopOrderType Loại stop order (chỉ cần cho TRADITIONAL strategy)
     * @return true nếu SELL order được trigger (dấu đã thay đổi)
     */
    public boolean isSellTriggeredBySignChange(Money currentPrice, Money triggerPrice, Money initialPrice, StopOrderType stopOrderType) {
        if (currentPrice == null || triggerPrice == null || initialPrice == null) {
            return false;
        }

        // For STOP_MARKET orders and variants, use strategy-specific logic
        if (stopOrderType == StopOrderType.STOP_MARKET ||
            stopOrderType == StopOrderType.BUY_DIP_MARKET ||
            stopOrderType == StopOrderType.SELL_RALLY_MARKET) {
            return checkSellStopMarketByStrategy(currentPrice, triggerPrice, initialPrice);
        }

        // SPECIAL CASE: Khi currentPrice = triggerPrice, luôn trigger (for non-STOP_MARKET orders)
        if (currentPrice.getValue().compareTo(triggerPrice.getValue()) == 0) {
            return true;
        }

        // Tính dấu ban đầu và hiện tại
        boolean initialSign = calculateSign(initialPrice, triggerPrice);
        boolean currentSign = calculateSign(currentPrice, triggerPrice);

        // Kiểm tra sign change theo strategy
        return checkSellSignChange(initialSign, currentSign, stopOrderType);
    }


    
    /**
     * BUY STOP_MARKET Strategy-Specific Logic
     * Xử lý BUY STOP_MARKET theo strategy hiện tại
     *
     * NOTE: Tất cả STOP_MARKET variants đều sử dụng traditional bidirectional logic
     * để đảm bảo consistency với yêu cầu bidirectional trigger
     */
    private boolean checkBuyStopMarketByStrategy(Money currentPrice, Money triggerPrice, Money initialPrice) {
        // Tất cả STOP_MARKET variants sử dụng traditional bidirectional logic
        return checkBuyStopMarketTraditional(currentPrice, triggerPrice, initialPrice);
    }

    /**
     * BUY STOP_MARKET Traditional Logic (True Bidirectional)
     * BUY STOP_MARKET trigger theo yêu cầu:
     * - Current < Trigger → Trigger when Current >= Trigger (breakout upward)
     * - Current > Trigger → Trigger when Current <= Trigger (breakdown downward)
     */
    private boolean checkBuyStopMarketTraditional(Money currentPrice, Money triggerPrice, Money initialPrice) {
        // Determine current position relative to trigger
        int currentVsTrigger = currentPrice.compareTo(triggerPrice);
        int initialVsTrigger = initialPrice.compareTo(triggerPrice);

        // SPECIAL CASE: Khi currentPrice = triggerPrice, luôn trigger (TRADITIONAL strategy requirement)
        if (currentVsTrigger == 0) {
            return true;
        }

        // Case 1: Initial < Trigger → Trigger when Current >= Trigger (breakout upward)
        if (initialVsTrigger < 0) {
            return currentVsTrigger >= 0;
        }

        // Case 2: Initial > Trigger → Trigger when Current <= Trigger (breakdown downward)
        if (initialVsTrigger > 0) {
            return currentVsTrigger <= 0;
        }

        // Case 3: Initial = Trigger → Always trigger (any movement)
        return true;
    }

    /**
     * SELL STOP_MARKET Strategy-Specific Logic
     * Xử lý SELL STOP_MARKET theo strategy hiện tại
     *
     * NOTE: Tất cả STOP_MARKET variants đều sử dụng traditional bidirectional logic
     * để đảm bảo consistency với yêu cầu bidirectional trigger
     */
    private boolean checkSellStopMarketByStrategy(Money currentPrice, Money triggerPrice, Money initialPrice) {
        // Tất cả STOP_MARKET variants sử dụng traditional bidirectional logic
        return checkSellStopMarketTraditional(currentPrice, triggerPrice, initialPrice);
    }

    /**
     * SELL STOP_MARKET Traditional Logic (True Bidirectional)
     * SELL STOP_MARKET trigger theo yêu cầu:
     * - Current < Trigger → Trigger when Current >= Trigger (breakout upward)
     * - Current > Trigger → Trigger when Current <= Trigger (breakdown downward)
     */
    private boolean checkSellStopMarketTraditional(Money currentPrice, Money triggerPrice, Money initialPrice) {
        // Determine current position relative to trigger
        int currentVsTrigger = currentPrice.compareTo(triggerPrice);
        int initialVsTrigger = initialPrice.compareTo(triggerPrice);

        // SPECIAL CASE: Khi currentPrice = triggerPrice, luôn trigger (TRADITIONAL strategy requirement)
        if (currentVsTrigger == 0) {
            return true;
        }

        // Case 1: Initial < Trigger → Trigger when Current >= Trigger (breakout upward)
        if (initialVsTrigger < 0) {
            return currentVsTrigger >= 0;
        }

        // Case 2: Initial > Trigger → Trigger when Current <= Trigger (breakdown downward)
        if (initialVsTrigger > 0) {
            return currentVsTrigger <= 0;
        }

        // Case 3: Initial = Trigger → Always trigger (any movement)
        return true;
    }

    /**
     * Tính dấu của (price - triggerPrice)
     * @param price Giá cần tính
     * @param triggerPrice Giá trigger
     * @return true nếu price >= triggerPrice (positive), false nếu price < triggerPrice (negative)
     */
    private boolean calculateSign(Money price, Money triggerPrice) {
        BigDecimal diff = price.getValue().subtract(triggerPrice.getValue());
        return diff.compareTo(BigDecimal.ZERO) >= 0;  // Back to original logic
    }

    /**
     * Kiểm tra sign change cho BUY orders theo strategy
     */
    private boolean checkBuySignChange(boolean initialSign, boolean currentSign, StopOrderType stopOrderType) {
        return switch (this) {
            case TRADITIONAL -> true;
            case BREAKOUT_BREAKDOWN ->
                // BUY Breakout: Trigger khi dấu chuyển từ NEGATIVE → POSITIVE (giá vượt lên)
                    !initialSign && currentSign;
            case BUY_DIP_SELL_RALLY -> {
                // BUY Dip: Trigger khi dấu chuyển từ POSITIVE → NEGATIVE (giá giảm xuống)
                // Chỉ áp dụng cho BUY_DIP_MARKET và BUY_DIP_LIMIT
                if (stopOrderType == StopOrderType.BUY_DIP_MARKET || stopOrderType == StopOrderType.BUY_DIP_LIMIT) {
                    yield initialSign && !currentSign;
                }
                // Fallback to traditional logic for other types
                yield checkTraditionalBuySignChange(initialSign, currentSign, stopOrderType);
                // Fallback to traditional logic for other types
            }
        };
    }

    /**
     * Kiểm tra sign change cho SELL orders theo strategy
     */
    private boolean checkSellSignChange(boolean initialSign, boolean currentSign, StopOrderType stopOrderType) {
        return switch (this) {
            case TRADITIONAL -> true;
            case BREAKOUT_BREAKDOWN ->
                // SELL Breakdown: Trigger khi dấu chuyển từ POSITIVE → NEGATIVE (giá vượt xuống)
                    initialSign && !currentSign;
            case BUY_DIP_SELL_RALLY -> {
                // SELL Rally: Trigger khi dấu chuyển từ NEGATIVE → POSITIVE (giá tăng lên)
                // Chỉ áp dụng cho SELL_RALLY_MARKET và SELL_RALLY_LIMIT
                if (stopOrderType == StopOrderType.SELL_RALLY_MARKET || stopOrderType == StopOrderType.SELL_RALLY_LIMIT) {
                    yield !initialSign && currentSign;
                }
                // Fallback to traditional logic for other types
                yield checkTraditionalSellSignChange(initialSign, currentSign, stopOrderType);
            }
        };
    }

    /**
     * Traditional sign change logic cho BUY orders
     */
    private boolean checkTraditionalBuySignChange(boolean initialSign, boolean currentSign, StopOrderType stopOrderType) {
        return switch (stopOrderType) {
            case STOP_LOSS ->
                // SHORT position Stop Loss: Trigger khi dấu chuyển từ NEGATIVE → POSITIVE (giá tăng vượt trigger)
                    !initialSign && currentSign;
            case TAKE_PROFIT ->
                // SHORT position Take Profit: Trigger khi dấu chuyển từ POSITIVE → NEGATIVE (giá giảm xuống trigger)
                    initialSign && !currentSign;
            case STOP_MARKET ->
                // BUY Stop Market: Trigger khi dấu chuyển từ NEGATIVE → POSITIVE (breakout)
                    !initialSign && currentSign;
            case STOP_LIMIT ->
                // BUY Stop Limit: Support both traditional directions
                // - NEGATIVE → POSITIVE: Price rises above stop (traditional breakout from below)
                // - POSITIVE → NEGATIVE: Price drops below stop (traditional stop loss from above)
                    (!initialSign && currentSign) || (initialSign && !currentSign);
            case BUY_DIP_MARKET ->
                // BUY Dip Market: Trigger khi dấu chuyển từ POSITIVE → NEGATIVE (mua khi giá xuống)
                    initialSign && !currentSign;
            case BUY_DIP_LIMIT ->
                // BUY Dip Limit: Trigger khi dấu chuyển từ POSITIVE → NEGATIVE (mua khi giá xuống)
                    initialSign && !currentSign;
            default -> false;
        };
    }

    /**
     * Traditional sign change logic cho SELL orders
     */
    private boolean checkTraditionalSellSignChange(boolean initialSign, boolean currentSign, StopOrderType stopOrderType) {
        return switch (stopOrderType) {
            case STOP_LOSS ->
                // LONG position Stop Loss: Trigger khi dấu chuyển từ POSITIVE → NEGATIVE (giá giảm xuống trigger)
                    initialSign && !currentSign;
            case TAKE_PROFIT ->
                // LONG position Take Profit: Trigger khi dấu chuyển từ NEGATIVE → POSITIVE (giá tăng vượt trigger)
                    !initialSign && currentSign;
            case STOP_MARKET ->
                // SELL Stop Market: Trigger khi dấu chuyển từ POSITIVE → NEGATIVE (breakdown)
                    initialSign && !currentSign;
            case STOP_LIMIT ->
                // SELL Stop Limit: Support both traditional directions
                // - POSITIVE → NEGATIVE: Price drops below stop (traditional stop loss from above)
                // - NEGATIVE → POSITIVE: Price rises above stop (traditional stop loss from below)
                    (initialSign && !currentSign) || (!initialSign && currentSign);
            case SELL_RALLY_MARKET ->
                // SELL Rally Market: Trigger khi dấu chuyển từ NEGATIVE → POSITIVE (bán khi giá lên)
                    !initialSign && currentSign;
            case SELL_RALLY_LIMIT ->
                // SELL Rally Limit: Trigger khi dấu chuyển từ NEGATIVE → POSITIVE (bán khi giá lên)
                    !initialSign && currentSign;
            default -> false;
        };
    }

    // ===== UTILITY METHODS =====

    /**
     * Validate sign change với price gap detection
     * Phát hiện trường hợp giá gap qua trigger price
     */
    public boolean validateSignChangeWithGapDetection(Money currentPrice, Money triggerPrice, Money initialPrice, Money previousPrice) {
        if (currentPrice == null || triggerPrice == null || initialPrice == null) {
            return false;
        }

        // Kiểm tra sign change thông thường
        boolean initialSign = calculateSign(initialPrice, triggerPrice);
        boolean currentSign = calculateSign(currentPrice, triggerPrice);
        boolean hasSignChange = initialSign != currentSign;

        // Kiểm tra price gap nếu có previousPrice
        boolean hasGapJump = false;
        if (previousPrice != null) {
            hasGapJump = detectPriceGap(previousPrice, currentPrice, triggerPrice);
        }

        return hasSignChange || hasGapJump;
    }

    /**
     * Phát hiện price gap jump qua trigger price
     */
    private boolean detectPriceGap(Money previousPrice, Money currentPrice, Money triggerPrice) {
        BigDecimal prevValue = previousPrice.getValue();
        BigDecimal currValue = currentPrice.getValue();
        BigDecimal triggerValue = triggerPrice.getValue();

        // Kiểm tra gap jump UP qua trigger
        boolean gapUp = prevValue.compareTo(triggerValue) < 0 && currValue.compareTo(triggerValue) > 0;

        // Kiểm tra gap jump DOWN qua trigger
        boolean gapDown = prevValue.compareTo(triggerValue) > 0 && currValue.compareTo(triggerValue) < 0;

        return gapUp || gapDown;
    }

    /**
     * Debug helper - Mô tả sign change
     */
    public String describeSignChange(Money currentPrice, Money triggerPrice, Money initialPrice) {
        if (currentPrice == null || triggerPrice == null || initialPrice == null) {
            return "INVALID_PARAMS";
        }

        boolean initialSign = calculateSign(initialPrice, triggerPrice);
        boolean currentSign = calculateSign(currentPrice, triggerPrice);

        BigDecimal initialDiff = initialPrice.getValue().subtract(triggerPrice.getValue());
        BigDecimal currentDiff = currentPrice.getValue().subtract(triggerPrice.getValue());

        return String.format("Initial: %s (%.2f - %.2f = %.2f) | Current: %s (%.2f - %.2f = %.2f) | Changed: %s",
            initialSign ? "POSITIVE" : "NEGATIVE",
            initialPrice.getValue(), triggerPrice.getValue(), initialDiff,
            currentSign ? "POSITIVE" : "NEGATIVE",
            currentPrice.getValue(), triggerPrice.getValue(), currentDiff,
            initialSign != currentSign ? "YES" : "NO"
        );
    }

    /**
     * Mô tả strategy
     */
    public String getDescription() {
        switch (this) {
            case TRADITIONAL:
                return "Traditional Strategy - Original stop order logic based on order type";
            case BREAKOUT_BREAKDOWN:
                return "Breakout/Breakdown Strategy - Trigger when price breaks through levels";
            case BUY_DIP_SELL_RALLY:
                return "Buy Dip/Sell Rally Strategy - Trigger when price returns to levels";
            default:
                return "Unknown Strategy";
        }
    }
}
