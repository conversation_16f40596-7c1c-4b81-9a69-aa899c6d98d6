package com.icetea.lotus.matching.domain.enums;

/**
 * <AUTHOR> nguyen
 */

/**
 * H<PERSON>ớng lệnh giao dịch
 */
public enum OrderDirection {
    /**
     * Lệnh mua
     */
    BUY,
    
    /**
     * L<PERSON><PERSON> bán
     */
    SELL;
    
    /**
     * L<PERSON><PERSON> hướng ngược lạ<PERSON>
     */
    public OrderDirection opposite() {
        return this == BUY ? SELL : BUY;
    }
    
    /**
     * Kiểm tra có phải lệnh mua không
     */
    public boolean isBuy() {
        return this == BUY;
    }
    
    /**
     * Ki<PERSON><PERSON> tra có phải lệnh bán không
     */
    public boolean isSell() {
        return this == SELL;
    }
}
