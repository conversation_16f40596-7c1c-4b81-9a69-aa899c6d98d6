package com.icetea.lotus.matching.domain.enums;

/**
 * <AUTHOR> nguyen
 */

/**
 * <PERSON>ạ<PERSON> lệnh chờ (Stop Order)
 */
public enum StopOrderType {
    /**
     * Stop Loss - Lệnh cắt lỗ
     * Kích hoạt khi giá chạm mức stop để hạn chế thua lỗ
     */
    STOP_LOSS,
    
    /**
     * Take Profit - Lệnh chốt lời
     * Kích hoạt khi giá chạm mức target để chốt lời
     */
    TAKE_PROFIT,
    
    /**
     * Stop Market - Lệnh chờ thị trường
     * Chuyển thành market order khi kích hoạt
     */
    STOP_MARKET,
    
    /**
     * Stop Limit - Lệnh chờ giới hạn
     * Chuyển thành limit order khi kích hoạt
     */
    STOP_LIMIT,

    /**
     * Buy Dip Market - Lệnh mua khi giá giảm
     * BUY order trigger khi giá <= trigger (mua khi giá xuống)
     */
    BUY_DIP_MARKET,

    /**
     * Buy Dip Limit - Lệnh mua giới hạn khi giá giảm
     * BUY limit order trigger khi giá <= trigger
     */
    BUY_DIP_LIMIT,

    /**
     * Sell Rally Market - Lệnh bán khi giá tăng
     * SELL order trigger khi giá >= trigger (bán khi giá lên)
     */
    SELL_RALLY_MARKET,

    /**
     * Sell Rally Limit - Lệnh bán giới hạn khi giá tăng
     * SELL limit order trigger khi giá >= trigger
     */
    SELL_RALLY_LIMIT
}
