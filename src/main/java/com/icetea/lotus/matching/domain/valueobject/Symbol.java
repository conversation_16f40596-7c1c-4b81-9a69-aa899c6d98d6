package com.icetea.lotus.matching.domain.valueobject;

/**
 * <AUTHOR> nguyen
 */

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * Symbol value object
 * <PERSON><PERSON><PERSON> diện cho cặp giao dịch (ví dụ: BTC/USDT, ETH/USDT)
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Symbol {
    
    private String value;
    
    public static Symbol of(String value) {
        return new Symbol(value);
    }

    // Explicit getValue method for compilation
    public String getValue() {
        return value;
    }
    
    /**
     * Lấy base currency (ví dụ: BTC từ BTC/USDT)
     */
    public String getBaseCurrency() {
        if (value != null && value.contains("/")) {
            return value.split("/")[0];
        }
        if (value != null && value.contains("-")) {
            return value.split("-")[0];
        }
        // Fallback cho format BTCUSDT
        if (value != null && value.length() > 3) {
            return value.substring(0, 3);
        }
        return value;
    }
    
    /**
     * Lấy quote currency (ví dụ: USDT từ BTC/USDT)
     */
    public String getQuoteCurrency() {
        if (value != null && value.contains("/")) {
            return value.split("/")[1];
        }
        if (value != null && value.contains("-")) {
            return value.split("-")[1];
        }
        // Fallback cho format BTCUSDT
        if (value != null && value.length() > 3) {
            return value.substring(3);
        }
        return null;
    }
    
    /**
     * Normalize symbol format to standard format (BTC/USDT)
     */
    public String normalize() {
        if (value == null) return null;
        
        // Đã là format chuẩn
        if (value.contains("/")) {
            return value.toUpperCase();
        }
        
        // Convert từ hyphen format
        if (value.contains("-")) {
            return value.replace("-", "/").toUpperCase();
        }
        
        // Convert từ concatenated format (BTCUSDT -> BTC/USDT)
        if (value.length() >= 6) {
            String base = value.substring(0, 3);
            String quote = value.substring(3);
            return (base + "/" + quote).toUpperCase();
        }
        
        return value.toUpperCase();
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Symbol symbol = (Symbol) o;
        return Objects.equals(normalize(), symbol.normalize());
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(normalize());
    }
    
    @Override
    public String toString() {
        return value;
    }
}
