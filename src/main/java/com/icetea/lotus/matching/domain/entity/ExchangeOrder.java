package com.icetea.lotus.matching.domain.entity;

import com.icetea.lotus.matching.domain.enums.SpotOrderDirection;
import com.icetea.lotus.matching.domain.enums.SpotOrderStatus;
import com.icetea.lotus.matching.domain.enums.SpotOrderType;
import com.icetea.lotus.matching.infrastructure.stp.SelfTradePreventionMode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * Exchange Order DTO for transferring data from Exchange module to Matching Engine module
 * Sử dụng enum của spot đã được định nghĩa để đảm bảo tương thích
 * 
 * <AUTHOR> nguyen
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExchangeOrder {
    
    // ===== BASIC ORDER INFORMATION =====
    
    /**
     * Order ID - unique identifier
     */
    private String orderId;
    
    /**
     * Trading symbol (e.g., "BTC/USDT")
     */
    private String symbol;
    
    /**
     * Member ID who placed the order
     */
    private Long memberId;
    
    /**
     * Order type using spot enum
     */
    private SpotOrderType type;
    
    /**
     * Order direction using spot enum
     */
    private SpotOrderDirection direction;
    
    /**
     * Order status using spot enum
     */
    private SpotOrderStatus status;
    
    // ===== PRICE AND QUANTITY =====
    
    /**
     * Order price (null for market orders)
     */
    private BigDecimal price;
    
    /**
     * Order amount/quantity
     */
    private BigDecimal amount = BigDecimal.ZERO;
    
    /**
     * Traded amount (filled quantity)
     */
    private BigDecimal tradedAmount = BigDecimal.ZERO;
    
    /**
     * Total turnover (price * traded amount)
     */
    private BigDecimal turnover = BigDecimal.ZERO;

    /**
     * Average execution price (calculated from turnover / tradedAmount)
     *   CRITICAL FIX: Initialize with ZERO to prevent NullPointerException
     */
    private BigDecimal averagePrice = BigDecimal.ZERO;

    // ===== MISSING FIELDS FROM EXCHANGE ORDER =====

    /**
     * Base symbol (settlement unit) - e.g., "USDT" in "BCH/USDT"
     */
    private String baseSymbol;

    /**
     * Coin symbol (trading unit) - e.g., "BCH" in "BCH/USDT"
     */
    private String coinSymbol;

    /**
     * Order resource/source
     */
    private String orderResource;

    /**
     * Self trade prevention mode
     */
    private SelfTradePreventionMode selfTradePreventionMode;

    // ===== STOP ORDER FIELDS =====
    
    /**
     * Stop price for stop orders (trigger price)
     */
    private BigDecimal stopPrice;
    
    // ===== TIMING INFORMATION =====
    
    /**
     * Order creation time (timestamp in milliseconds)
     */
    private Long time;
    
    /**
     * Order completion time
     */
    private Long completedTime;
    
    /**
     * Order cancellation time
     */
    private Long canceledTime;
    
    // ===== FLAGS AND METADATA =====
    
    /**
     * Whether order is completed
     */
    private Boolean completed;
    
    /**
     * Use discount flag - can be String or BigDecimal from Exchange
     */
    private String useDiscount;
    
    /**
     * Order source (WEB, API, MOBILE, etc.)
     */
    private String source;
    
    /**
     * Client order ID (if provided by client)
     */
    private String clientOrderId;
    
    /**
     * Time in force (GTC, IOC, FOK, etc.)
     */
    private String timeInForce;

    private List<Object> detail;

    private Long triggerTime;
    private String triggered;

    // ===== VALIDATION METHODS =====
    
    /**
     * Validate basic order fields
     */
    public boolean isValid() {
        return orderId != null && !orderId.trim().isEmpty()
            && symbol != null && !symbol.trim().isEmpty()
            && memberId != null && memberId > 0
            && type != null
            && direction != null
            && status != null
            && amount != null && amount.compareTo(BigDecimal.ZERO) > 0
            && (type.isMarketOrder() || (price != null && price.compareTo(BigDecimal.ZERO) > 0));
    }
    
    /**
     * Validate stop order specific fields
     */
    public boolean isValidStopOrder() {
        if (!type.isStopOrder()) {
            return true; // Not a stop order, no additional validation needed
        }
        
        // Stop orders must have stop price
        if (stopPrice == null || stopPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        
        // Stop limit orders must have limit price
        if (type == SpotOrderType.STOP_LIMIT) {
            return price != null && price.compareTo(BigDecimal.ZERO) > 0;
        }
        
        return true;
    }
    
    /**
     * Check if order is active (can be matched)
     */
    public boolean isActive() {
        return status != null && status.isActive();
    }
    
    /**
     * Check if order is completed
     */
    public boolean isCompleted() {
        return status != null && status.isCompleted();
    }

    /**
     * Get summary string for logging
     */
    public String getSummary() {
        return String.format("ExchangeOrderDTO[id=%s, symbol=%s, type=%s, direction=%s, " +
                           "status=%s, price=%s, amount=%s, tradedAmount=%s]",
                           orderId, symbol, type, direction, status, price, amount, tradedAmount);
    }
    
    @Override
    public String toString() {
        return getSummary();
    }
}
