package com.icetea.lotus.matching.domain.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * Value object representing turnover amount for spot market buy orders
 * Turnover = amount of quote currency to spend (e.g., $1000 USDT to buy BTC)
 */
@Getter
@EqualsAndHashCode(callSuper = true)
public class TurnoverAmount extends Money {
    
    private static final int DEFAULT_SCALE = 8;
    
    public TurnoverAmount(BigDecimal amount) {
        super(amount);
        validateTurnoverAmount(amount);
    }
    
    public static TurnoverAmount of(BigDecimal amount) {
        return new TurnoverAmount(amount);
    }
    
    public static TurnoverAmount of(String amount) {
        return new TurnoverAmount(new BigDecimal(amount));
    }
    
    public static TurnoverAmount of(double amount) {
        return new TurnoverAmount(BigDecimal.valueOf(amount));
    }
    
    public static TurnoverAmount zeroTurnover() {
        return new TurnoverAmount(BigDecimal.ZERO);
    }
    
    /**
     * Calculate quantity that can be bought with this turnover at given price
     * @param price Price per unit
     * @return Quantity that can be bought
     */
    public Money calculateQuantity(Money price) {
        if (price.isZero()) {
            throw new IllegalArgumentException("Price cannot be zero");
        }
        
        BigDecimal quantity = getAmount().divide(price.getAmount(), DEFAULT_SCALE, RoundingMode.DOWN);
        return Money.of(quantity);
    }
    
    /**
     * Calculate remaining turnover after buying specified quantity at given price
     * @param quantity Quantity bought
     * @param price Price per unit
     * @return Remaining turnover
     */
    public TurnoverAmount subtract(Money quantity, Money price) {
        BigDecimal usedTurnover = quantity.getAmount().multiply(price.getAmount());
        BigDecimal remainingTurnover = getAmount().subtract(usedTurnover);
        
        if (remainingTurnover.compareTo(BigDecimal.ZERO) < 0) {
            return TurnoverAmount.zeroTurnover();
        }
        
        return new TurnoverAmount(remainingTurnover);
    }
    
    /**
     * Check if this turnover amount is sufficient to buy minimum quantity at given price
     * @param price Price per unit
     * @param minQuantity Minimum quantity required
     * @return true if sufficient
     */
    public boolean isSufficientFor(Money price, Money minQuantity) {
        BigDecimal requiredTurnover = price.getAmount().multiply(minQuantity.getAmount());
        return getAmount().compareTo(requiredTurnover) >= 0;
    }
    
    /**
     * Add another turnover amount
     * @param other Other turnover amount
     * @return Sum of turnover amounts
     */
    public TurnoverAmount add(TurnoverAmount other) {
        return new TurnoverAmount(getAmount().add(other.getAmount()));
    }
    
    /**
     * Subtract another turnover amount
     * @param other Other turnover amount
     * @return Difference of turnover amounts
     */
    public TurnoverAmount subtract(TurnoverAmount other) {
        BigDecimal result = getAmount().subtract(other.getAmount());
        return new TurnoverAmount(result.max(BigDecimal.ZERO));
    }
    
    /**
     * Check if this turnover amount is positive
     * @return true if positive
     */
    @Override
    public boolean isPositive() {
        return getAmount().compareTo(BigDecimal.ZERO) > 0;
    }
    
    /**
     * Check if this turnover amount is zero
     * @return true if zero
     */
    @Override
    public boolean isZero() {
        return getAmount().compareTo(BigDecimal.ZERO) == 0;
    }
    
    private void validateTurnoverAmount(BigDecimal amount) {
        if (amount == null) {
            throw new IllegalArgumentException("Turnover amount cannot be null");
        }
        
        if (amount.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Turnover amount cannot be negative: " + amount);
        }
        
        if (amount.scale() > DEFAULT_SCALE) {
            throw new IllegalArgumentException("Turnover amount scale cannot exceed " + DEFAULT_SCALE + ": " + amount);
        }
    }
    
    @Override
    public String toString() {
        return "TurnoverAmount{" +
                "amount=" + getAmount() +
                '}';
    }
}
