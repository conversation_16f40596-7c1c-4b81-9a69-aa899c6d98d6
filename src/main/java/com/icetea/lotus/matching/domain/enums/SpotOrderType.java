package com.icetea.lotus.matching.domain.enums;

/**
 * Enum chuyên dùng cho spot trading - tương thích hoàn toàn với Exchange module
 * Mapping trực tiếp với ExchangeOrderType để đảm bảo tương thích 100%
 *
 * <AUTHOR> nguyen
 */
public enum SpotOrderType {

    /**
     * Lệnh thị trường - MARKET_PRICE trong Exchange
     * Thực hiện ngay lập tức với giá thị trường tốt nhất
     */
    MARKET_PRICE("MARKET_PRICE", "Market Order", "<PERSON><PERSON><PERSON> thị trường"),

    /**
     * Lệnh giới hạn - LIMIT_PRICE trong Exchange
     * Chỉ thực hiện khi đạt giá chỉ định hoặc tốt hơn
     */
    LIMIT_PRICE("LIMIT_PRICE", "Limit Order", "Lệnh giới hạn"),

    /**
     * Lệnh stop limit - STOP_LIMIT trong Exchange
     * Kích hoạt khi giá chạm stop price, sau đó đặt limit order
     */
    STOP_LIMIT("STOP_LIMIT", "Stop Limit Order", "Lệnh stop giới hạn"),

    /**
     * Lệnh stop market - STOP_MARKET trong Exchange
     * Kích hoạt khi giá chạm stop price, sau đó đặt market order
     */
    STOP_MARKET("STOP_MARKET", "Stop Market Order", "Lệnh stop thị trường");
    
    private final String exchangeCode;
    private final String englishName;
    private final String vietnameseName;
    
    SpotOrderType(String exchangeCode, String englishName, String vietnameseName) {
        this.exchangeCode = exchangeCode;
        this.englishName = englishName;
        this.vietnameseName = vietnameseName;
    }
    
    public String getExchangeCode() {
        return exchangeCode;
    }
    
    public String getEnglishName() {
        return englishName;
    }
    
    public String getVietnameseName() {
        return vietnameseName;
    }
    
    /**
     * Kiểm tra có phải lệnh thị trường không
     */
    public boolean isMarketOrder() {
        return this == MARKET_PRICE || this == STOP_MARKET;
    }

    /**
     * Kiểm tra có phải lệnh giới hạn không
     */
    public boolean isLimitOrder() {
        return this == LIMIT_PRICE || this == STOP_LIMIT;
    }

    /**
     * Kiểm tra có phải stop order không
     */
    public boolean isStopOrder() {
        return this == STOP_LIMIT || this == STOP_MARKET;
    }
    
    /**
     * Kiểm tra có cần stop price không
     */
    public boolean requiresStopPrice() {
        return isStopOrder();
    }
    
    /**
     * Kiểm tra có cần limit price không
     */
    public boolean requiresLimitPrice() {
        return isLimitOrder();
    }
    
    /**
     * Chuyển đổi từ Exchange OrderType string sang SpotOrderType
     */
    public static SpotOrderType fromExchangeCode(String exchangeCode) {
        if (exchangeCode == null || exchangeCode.trim().isEmpty()) {
            throw new IllegalArgumentException("Exchange code không được null hoặc rỗng");
        }

        String code = exchangeCode.trim();
        for (SpotOrderType type : values()) {
            if (type.exchangeCode.equals(code)) {
                return type;
            }
        }

        throw new IllegalArgumentException("Không tìm thấy SpotOrderType cho exchange code: " + exchangeCode);
    }

    /**
     * Chuyển đổi từ OrderType sang SpotOrderType
     */
    public static SpotOrderType fromOrderType(OrderType orderType) {
        if (orderType == null) {
            throw new IllegalArgumentException("OrderType không được null");
        }

        return switch (orderType) {
            case MARKET -> MARKET_PRICE;
            case LIMIT -> LIMIT_PRICE;
            case STOP_LIMIT -> STOP_LIMIT;
            case STOP_MARKET -> STOP_MARKET;
            default -> throw new IllegalArgumentException("Không hỗ trợ OrderType: " + orderType);
        };
    }

    /**
     * Chuyển đổi sang OrderType
     */
    public OrderType toOrderType() {
        return switch (this) {
            case MARKET_PRICE -> OrderType.MARKET;
            case LIMIT_PRICE -> OrderType.LIMIT;
            case STOP_LIMIT -> OrderType.STOP_LIMIT;
            case STOP_MARKET -> OrderType.STOP_MARKET;
        };
    }
    
    /**
     * Lấy mô tả đầy đủ
     */
    public String getFullDescription() {
        return String.format("%s (%s) - %s", englishName, exchangeCode, vietnameseName);
    }
    
    /**
     * Kiểm tra tính hợp lệ với direction
     */
    public boolean isValidForDirection(OrderDirection direction) {
        // Tất cả spot order types đều hỗ trợ cả BUY và SELL
        return direction == OrderDirection.BUY || direction == OrderDirection.SELL;
    }

    /**
     * Lấy priority cho matching (số càng nhỏ càng ưu tiên)
     */
    public int getMatchingPriority() {
        return switch (this) {
            case MARKET_PRICE, STOP_MARKET -> 1; // Ưu tiên cao nhất
            case LIMIT_PRICE -> 2; // Ưu tiên trung bình
            case STOP_LIMIT -> 3; // Ưu tiên thấp (chờ trigger)
        };
    }

    
    @Override
    public String toString() {
        return exchangeCode;
    }
}
