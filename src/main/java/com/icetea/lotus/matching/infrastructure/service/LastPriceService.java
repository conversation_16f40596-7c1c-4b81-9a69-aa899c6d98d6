package com.icetea.lotus.matching.infrastructure.service;

import com.icetea.lotus.matching.domain.valueobject.Money;
import com.icetea.lotus.matching.infrastructure.constants.CommonConstance;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.concurrent.ConcurrentSkipListMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Last Price Service - simplified implementation for matching-engine module
 * Migrated from future-core
 */
@Slf4j
@Service
public class LastPriceService {

    // Last price cache - ConcurrentSkipListMap cho better performance
    private final ConcurrentSkipListMap<String, Money> spotLastPriceCache = new ConcurrentSkipListMap<>();

    private final ConcurrentSkipListMap<String, Money> futureLastPriceCache = new ConcurrentSkipListMap<>();
    // Performance counters
    private final AtomicLong priceUpdates = new AtomicLong(0);
    private final AtomicLong priceQueries = new AtomicLong(0);
    private final AtomicLong cacheHits = new AtomicLong(0);
    private final AtomicLong cacheMisses = new AtomicLong(0);

    /**
     * <PERSON><PERSON><PERSON><PERSON> sử dụng bởi MatchingEngineStopOrderManager để initialize stop orders
     *
     * @param symbolStr Symbol string (ví dụ: BTC/USDT)
     * @return Last price as BigDecimal hoặc null nếu không tìm thấy
     */
    public BigDecimal getLastPrice(String symbolStr, String type) {
        if (symbolStr == null || symbolStr.trim().isEmpty()) {
            log.warn("Cannot get last price for null or empty symbol string");
            return null;
        }

        try {
            priceQueries.incrementAndGet();
            Money lastPrice = Money.ZERO;
            if (CommonConstance.SPOT.equals(type)) {
                lastPrice = spotLastPriceCache.get(symbolStr);
            } else if (CommonConstance.FUTURE.equals(type)) {
                lastPrice = futureLastPriceCache.get(symbolStr);
            }
            if (lastPrice != null) {
                cacheHits.incrementAndGet();
                log.info("Retrieved last price for {}: {}", symbolStr, lastPrice.getValue());
                return lastPrice.getValue();
            } else {
                cacheMisses.incrementAndGet();
                log.info("No last price found for {}", symbolStr);
                return null;
            }

        } catch (Exception e) {
            log.error("Error getting last price for {}: {}", symbolStr, e.getMessage(), e);
            return null;
        }
    }

    /**
     * Được sử dụng để sync từ ExchangeCompatibilityService cache
     *
     * @param symbolStr Symbol string (ví dụ: BTC/USDT)
     * @param price     Price as BigDecimal
     */
    public void updateLastPrice(String symbolStr, BigDecimal price, String type) {
        if (symbolStr == null || symbolStr.trim().isEmpty() || price == null) {
            log.warn("Cannot update last price with null or empty symbol/price");
            return;
        }

        try {
            // Convert BigDecimal to Money
            Money moneyPrice = Money.of(price);

            // Update cache
            if (CommonConstance.SPOT.equals(type)) {
                spotLastPriceCache.put(symbolStr, moneyPrice);
            } else if (CommonConstance.FUTURE.equals(type)) {
                futureLastPriceCache.put(symbolStr, moneyPrice);
            }
            priceUpdates.incrementAndGet();

            log.info("Updated last price for {}: {}", symbolStr, price);

        } catch (Exception e) {
            log.error("Error updating last price for {}: {}", symbolStr, e.getMessage(), e);
        }
    }


    /**
     * Get performance statistics
     *
     * @return Performance stats
     */
    public LastPriceStats getStats() {
        return LastPriceStats.builder()
                .totalPriceUpdates(priceUpdates.get())
                .totalPriceQueries(priceQueries.get())
                .cacheHits(cacheHits.get())
                .cacheMisses(cacheMisses.get())
                .cacheHitRate(calculateCacheHitRate())
                .symbolsWithPrices(spotLastPriceCache.size())
                .build();
    }


    /**
     * Calculate cache hit rate
     *
     * @return Cache hit rate (0.0 to 1.0)
     */
    private double calculateCacheHitRate() {
        long total = cacheHits.get() + cacheMisses.get();
        if (total == 0) {
            return 0.0;
        }
        return (double) cacheHits.get() / total;
    }

    /**
     * Last price statistics
     */
    @Builder
    @Data
    public static class LastPriceStats {
        private long totalPriceUpdates;
        private long totalPriceQueries;
        private long cacheHits;
        private long cacheMisses;
        private double cacheHitRate;
        private int symbolsWithPrices;
    }

    @Override
    public String toString() {
        return String.format("LastPriceService{symbols=%d, updates=%d, queries=%d, hitRate=%.2f%%}",
                spotLastPriceCache.size(), priceUpdates.get(), priceQueries.get(), calculateCacheHitRate() * 100);
    }
}
