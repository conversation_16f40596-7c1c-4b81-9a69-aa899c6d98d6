package com.icetea.lotus.matching.infrastructure.persistence.redis.service;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.icetea.lotus.matching.infrastructure.engine.DistributedOrderBookSnapshot;
import com.icetea.lotus.matching.infrastructure.persistence.redis.document.RedisSnapshotDocument;
import com.icetea.lotus.matching.infrastructure.persistence.redis.util.StopOrderConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.ALL_ORDERS;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.BUY_ORDERS;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.GET_AMOUNT;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.SELL_ORDERS;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.STOP_ORDERS;

/**
 * Redis-based Order Book Snapshot Service cho Matching Engine
 * Thay thế cho MongoDB-based MatchingEngineSnapshotService
 *
 * <AUTHOR> nguyen
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RedisSnapshotService {

    @Qualifier("snapshotRedisTemplate")
    private final RedisTemplate<String, Object> redisTemplate;

    private final RedisSnapshotCleanupService cleanupService;

    private final AtomicLong versionCounter = new AtomicLong(System.currentTimeMillis());

    // Configuration
    @Value("${matching-engine.snapshot.ttl.hours:24}")
    private int snapshotTtlHours;

    @Value("${matching-engine.snapshot.max.versions:100}")
    private int maxVersionsPerSymbol;

    @Value("${matching-engine.snapshot.compression.enabled:true}")
    private boolean compressionEnabled;

    @Value("${matching-engine.snapshot.cleanup.grace.period.minutes:5}")
    private int gracePeriodMinutes;

    // Performance metrics
    private final AtomicLong saveCount = new AtomicLong(0);
    private final AtomicLong loadCount = new AtomicLong(0);
    private final AtomicLong totalSaveTime = new AtomicLong(0);
    private final AtomicLong totalLoadTime = new AtomicLong(0);

    // Helper: find first available method by name without throwing NoSuchMethodException
    private static Method findMethod(Class<?> clazz, String... names) {
        Method[] methods = clazz.getMethods();
        for (String name : names) {
            for (Method m : methods) {
                if (m.getName().equals(name)) {
                    return m;
                }
            }
        }
        return null;
    }

    /**
     * Save order book snapshot to Redis
     *
     * @param symbol      Trading symbol
     * @param tradingType SPOT or FUTURES
     * @param snapshot    Order book snapshot data
     * @return Snapshot ID
     */
    public String saveSnapshot(String symbol, String tradingType, Object snapshot) {
        long startTime = System.nanoTime();

        try {
            log.info("Starting snapshot save for symbol: {}, tradingType: {}", symbol, tradingType);

            // Validate inputs
            Objects.requireNonNull(snapshot, "Snapshot cannot be null");
            Objects.requireNonNull(symbol, "Symbol cannot be null");
            Objects.requireNonNull(tradingType, "Trading type cannot be null");

            // Generate version and document
            long version = versionCounter.incrementAndGet();
            log.info("Converting snapshot to document for symbol: {}, tradingType: {}, version: {}",
                    symbol, tradingType, version);
            RedisSnapshotDocument document = convertToDocument(snapshot, symbol, tradingType, version);
            if (document == null) {
                return null;
            }
            // Save to Redis
            String redisKey = RedisSnapshotDocument.generateRedisKey(symbol, tradingType, version);
            String latestKey = RedisSnapshotDocument.generateLatestKey(symbol, tradingType);
            String versionsKey = RedisSnapshotDocument.generateVersionsKey(symbol, tradingType);

            // Save snapshot data with TTL
            redisTemplate.opsForValue().set(redisKey, document, snapshotTtlHours, TimeUnit.HOURS);

            // Update latest snapshot pointer
            redisTemplate.opsForValue().set(latestKey, version, snapshotTtlHours, TimeUnit.HOURS);

            // Add version to sorted set (score = version for ordering)
            redisTemplate.opsForZSet().add(versionsKey, version, version);
            redisTemplate.expire(versionsKey, snapshotTtlHours, TimeUnit.HOURS);

            // Cleanup old versions asynchronously using enhanced cleanup service
            cleanupService.performAggressiveCleanup(symbol, tradingType);

            // Update statistics
            saveCount.incrementAndGet();
            totalSaveTime.addAndGet(System.nanoTime() - startTime);
            log.info("Saved snapshot for symbol: {}, tradingType: {}, version: {}, size: {} bytes",
                    symbol, tradingType, version,
                    document.getMetadata() != null ? document.getMetadata().getSnapshotSizeBytes() : 0);
            return document.getId();

        } catch (Exception e) {
            log.error("Failed to save snapshot for symbol: {}, tradingType: {}", symbol, tradingType, e);
        }
        return symbol;
    }

    /**
     * Load latest snapshot from Redis
     *
     * @param symbol      Trading symbol
     * @param tradingType SPOT or FUTURES
     * @return Optional snapshot document
     */
    public Optional<RedisSnapshotDocument> loadLatestSnapshot(String symbol, String tradingType) {
        long startTime = System.nanoTime();

        try {
            log.info("Loading latest snapshot for symbol: {}, tradingType: {}", symbol, tradingType);

            String latestKey = RedisSnapshotDocument.generateLatestKey(symbol, tradingType);
            Object versionObj = redisTemplate.opsForValue().get(latestKey);

            log.info("versionObj: {}", versionObj);

            if (versionObj == null) {
                log.info("No latest snapshot found for symbol: {}, tradingType: {}", symbol, tradingType);
                return Optional.empty();
            }

            Long version = Long.valueOf(versionObj.toString());
            String redisKey = RedisSnapshotDocument.generateRedisKey(symbol, tradingType, version);

            Object snapshotObj = redisTemplate.opsForValue().get(redisKey);

            log.info("snapshotObj: {}", snapshotObj);


            if (snapshotObj == null) {
                log.warn("Latest snapshot version {} not found for symbol: {}, tradingType: {}",
                        version, symbol, tradingType);
                return Optional.empty();
            }

            RedisSnapshotDocument document = convertToRedisSnapshotDocument(snapshotObj);

            // Update statistics
            loadCount.incrementAndGet();
            totalLoadTime.addAndGet(System.nanoTime() - startTime);

            log.info("Loaded latest snapshot for symbol: {}, tradingType: {}, version: {}",
                    symbol, tradingType, version);
            return Optional.of(document);

        } catch (Exception e) {
            log.error("Failed to load latest snapshot for symbol: {}, tradingType: {}", symbol, tradingType, e);
            return Optional.empty();
        }
    }

    /**
     * Convert snapshot object to Redis document
     */
    private RedisSnapshotDocument convertToDocument(Object snapshot, String symbol, String tradingType, Long version) {
        try {
            // Extract data from snapshot object (similar to MongoDB version)
            Map<String, Object> snapshotData = extractSnapshotData(snapshot);

            @SuppressWarnings("unchecked")
            Map<String, List<RedisSnapshotDocument.OrderDocument>> buyOrders =
                    (Map<String, List<RedisSnapshotDocument.OrderDocument>>) snapshotData.get(BUY_ORDERS);

            @SuppressWarnings("unchecked")
            Map<String, List<RedisSnapshotDocument.OrderDocument>> sellOrders =
                    (Map<String, List<RedisSnapshotDocument.OrderDocument>>) snapshotData.get(SELL_ORDERS);

            @SuppressWarnings("unchecked")
            List<RedisSnapshotDocument.OrderDocument> allOrders =
                    (List<RedisSnapshotDocument.OrderDocument>) snapshotData.get(ALL_ORDERS);

            @SuppressWarnings("unchecked")
            List<RedisSnapshotDocument.OrderDocument> stopOrders =
                    (List<RedisSnapshotDocument.OrderDocument>) snapshotData.get(STOP_ORDERS);

            // Calculate metadata
            int totalBuyOrders = buyOrders.values().stream().mapToInt(List::size).sum();
            int totalSellOrders = sellOrders.values().stream().mapToInt(List::size).sum();

            RedisSnapshotDocument.SnapshotMetadata metadata =
                    RedisSnapshotDocument.SnapshotMetadata.builder()
                            .totalOrders(allOrders.size())
                            .buyOrdersCount(totalBuyOrders)
                            .sellOrdersCount(totalSellOrders)
                            .stopOrdersCount(stopOrders.size())
                            .priceLevelsBuy(buyOrders.size())
                            .priceLevelsSell(sellOrders.size())
                            .snapshotSizeBytes(estimateDocumentSize(allOrders, stopOrders))
                            .compressionRatio(1.0)
                            .creationTimeMicros(System.nanoTime() / 1000)
                            .sourceNode(getNodeId())
                            .checksum(calculateChecksum(allOrders.size(), stopOrders.size()))
                            .tradingType(tradingType)
                            .engineType(tradingType.equals("SPOT") ? "EXCHANGE" : "FUTURE_CORE")
                            .build();

            log.info("Converted snapshot - Total orders: {}, Buy levels: {}, Sell levels: {}",
                    allOrders.size(), buyOrders.size(), sellOrders.size());

            return RedisSnapshotDocument.builder()
                    .id(RedisSnapshotDocument.generateId(symbol, tradingType, version))
                    .symbol(symbol)
                    .tradingType(tradingType)
                    .version(version)
                    .timestamp(Instant.now())
                    .buyOrders(buyOrders)
                    .sellOrders(sellOrders)
                    .allOrders(allOrders)
                    .stopOrders(stopOrders)
                    .metadata(metadata)
                    .createdAt(Instant.now())
                    .expiresAt(RedisSnapshotDocument.calculateExpiryTime(snapshotTtlHours))
                    .build();

        } catch (Exception e) {
            log.error("Failed to convert snapshot to document for symbol: {}, tradingType: {}", symbol, tradingType, e);
        }
        return null;
    }

    /**
     * Extract snapshot data from snapshot object
     * Based on ExchangeMatchingEngine.getOrderBook() structure
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> extractSnapshotData(Object snapshot) {
        Map<String, List<RedisSnapshotDocument.OrderDocument>> buyOrders = new HashMap<>();
        Map<String, List<RedisSnapshotDocument.OrderDocument>> sellOrders = new HashMap<>();
        List<RedisSnapshotDocument.OrderDocument> allOrders = new ArrayList<>();
        List<RedisSnapshotDocument.OrderDocument> stopOrders = new ArrayList<>();

        try {
            // Convert based on snapshot type
            if (snapshot instanceof Map<?, ?> rawMap) {
                // Handle Map-based order book (from ExchangeMatchingEngine.getOrderBook())
                Map<String, Object> snapshotMap = new HashMap<>();
                for (Map.Entry<?, ?> entry : rawMap.entrySet()) {
                    if (entry.getKey() instanceof String) {
                        snapshotMap.put((String) entry.getKey(), entry.getValue());
                    }
                }
                convertMapBasedOrderBook(snapshotMap, buyOrders, sellOrders, allOrders, stopOrders);


            } else if (snapshot != null) {
                // If it's a DistributedOrderBookSnapshot from matching-engine, use specialized converter
                if (snapshot instanceof DistributedOrderBookSnapshot) {
                    convertDistributedOrderBookSnapshot(snapshot, buyOrders, sellOrders, allOrders, stopOrders);
                } else {
                    // Fallback: extract using reflection
                    convertGenericSnapshot(snapshot, allOrders);
                }

            } else {
                log.warn("Snapshot is null, creating empty document");
            }

        } catch (Exception e) {
            log.error("Failed to extract snapshot data", e);
        }

        Map<String, Object> data = new HashMap<>();
        data.put("buyOrders", buyOrders);
        data.put("sellOrders", sellOrders);
        data.put("allOrders", allOrders);
        data.put("stopOrders", stopOrders);
        return data;
    }

    /**
     * Estimate document size in bytes
     */
    private long estimateDocumentSize(List<RedisSnapshotDocument.OrderDocument> allOrders,
                                      List<RedisSnapshotDocument.OrderDocument> stopOrders) {
        // Rough estimation: 500 bytes per order
        return (allOrders.size() + stopOrders.size()) * 500L;
    }

    /**
     * Calculate checksum for validation
     */
    private String calculateChecksum(int allOrdersSize, int stopOrdersSize) {
        return String.valueOf((allOrdersSize + stopOrdersSize) * 31 + System.currentTimeMillis() % 1000);
    }

    /**
     * Get current node ID
     */
    private String getNodeId() {
        return System.getProperty("matching-engine.pod-name", "unknown-node");
    }


    /**
     * Convert Map-based order book from ExchangeMatchingEngine
     */
    @SuppressWarnings("java:S3776")
    private void convertMapBasedOrderBook(Map<String, Object> snapshotMap,
                                          Map<String, List<RedisSnapshotDocument.OrderDocument>> buyOrders,
                                          Map<String, List<RedisSnapshotDocument.OrderDocument>> sellOrders,
                                          List<RedisSnapshotDocument.OrderDocument> allOrders,
                                          List<RedisSnapshotDocument.OrderDocument> stopOrders) {
        try {
            // Extract buy orders
            Object buyOrdersObj = snapshotMap.get("buyOrders");
            if (buyOrdersObj instanceof Map) {
                Map<String, Object> buyOrdersMap = (Map<String, Object>) buyOrdersObj;
                for (Map.Entry<String, Object> entry : buyOrdersMap.entrySet()) {
                    String priceLevel = convertOriginalPriceLevelToSafe(entry.getKey());
                    List<RedisSnapshotDocument.OrderDocument> orders = convertOrderList(entry.getValue());
                    if (!orders.isEmpty()) {
                        buyOrders.put(priceLevel, orders);
                        allOrders.addAll(orders);
                    }
                }
            }

            // Extract sell orders
            Object sellOrdersObj = snapshotMap.get("sellOrders");
            if (sellOrdersObj instanceof Map) {
                Map<String, Object> sellOrdersMap = (Map<String, Object>) sellOrdersObj;
                for (Map.Entry<String, Object> entry : sellOrdersMap.entrySet()) {
                    String priceLevel = convertOriginalPriceLevelToSafe(entry.getKey());
                    List<RedisSnapshotDocument.OrderDocument> orders = convertOrderList(entry.getValue());
                    if (!orders.isEmpty()) {
                        sellOrders.put(priceLevel, orders);
                        allOrders.addAll(orders);
                    }
                }
            }

            // Extract stop orders using specialized converter
            Object stopOrdersObj = snapshotMap.get("stopOrders");
            if (stopOrdersObj instanceof List) {
                List<RedisSnapshotDocument.OrderDocument> stopOrdersList = convertStopOrderList(stopOrdersObj);
                stopOrders.addAll(stopOrdersList);
            }

            // If allOrders is empty, try to extract from allOrders field
            if (allOrders.isEmpty()) {
                Object allOrdersObj = snapshotMap.get("allOrders");
                if (allOrdersObj instanceof List) {
                    List<RedisSnapshotDocument.OrderDocument> allOrdersList = convertOrderList(allOrdersObj);
                    allOrders.addAll(allOrdersList);
                }
            }

            log.info("Converted map-based order book: {} buy levels, {} sell levels, {} total orders, {} stop orders",
                    buyOrders.size(), sellOrders.size(), allOrders.size(), stopOrders.size());

        } catch (Exception e) {
            log.error("Error converting map-based order book", e);
        }
    }

    /**
     * Convert DistributedOrderBookSnapshot (placeholder for future implementation)
     */
    @SuppressWarnings("java:S3776")
    private void convertDistributedOrderBookSnapshot(Object snapshot,
                                                     Map<String, List<RedisSnapshotDocument.OrderDocument>> buyOrders,
                                                     Map<String, List<RedisSnapshotDocument.OrderDocument>> sellOrders,
                                                     List<RedisSnapshotDocument.OrderDocument> allOrders,
                                                     List<RedisSnapshotDocument.OrderDocument> stopOrders) {
        try {
            log.info("Converting DistributedOrderBookSnapshot to Redis format");

            // Use reflection to access DistributedOrderBookSnapshot methods
            Class<?> snapshotClass = snapshot.getClass();

            // Get buy orders
            Method getBuyOrdersMethod = snapshotClass.getMethod("getBuyOrders");
            Object buyOrdersList = getBuyOrdersMethod.invoke(snapshot);
            if (buyOrdersList instanceof List<?> orders) {
                for (Object order : orders) {
                    RedisSnapshotDocument.OrderDocument orderDoc = convertOrderToDocument(order);
                    if (orderDoc != null) {
                        allOrders.add(orderDoc);
                        // Group by price for buy orders
                        String priceKey = orderDoc.getPrice() != null ? orderDoc.getPrice().getAmount() : "0";
                        buyOrders.computeIfAbsent(priceKey, k -> new ArrayList<>()).add(orderDoc);
                    }
                }
                log.info("Converted {} buy orders from DistributedOrderBookSnapshot", orders.size());
            }

            // Get sell orders
            Method getSellOrdersMethod = snapshotClass.getMethod("getSellOrders");
            Object sellOrdersList = getSellOrdersMethod.invoke(snapshot);
            if (sellOrdersList instanceof List<?> orders) {
                for (Object order : orders) {
                    RedisSnapshotDocument.OrderDocument orderDoc = convertOrderToDocument(order);
                    if (orderDoc != null) {
                        allOrders.add(orderDoc);
                        // Group by price for sell orders
                        String priceKey = orderDoc.getPrice() != null ? orderDoc.getPrice().getAmount() : "0";
                        sellOrders.computeIfAbsent(priceKey, k -> new ArrayList<>()).add(orderDoc);
                    }
                }
                log.info("Converted {} sell orders from DistributedOrderBookSnapshot", orders.size());
            }

            // Get stop orders
            Method getStopOrdersMethod = snapshotClass.getMethod("getStopOrders");
            Object stopOrdersList = getStopOrdersMethod.invoke(snapshot);
            if (stopOrdersList instanceof List<?> orders) {
                for (Object order : orders) {
                    // Use specialized stop order converter
                    RedisSnapshotDocument.OrderDocument orderDoc = StopOrderConverter.fromObject(order);
                    if (orderDoc != null) {
                        stopOrders.add(orderDoc);
                    }
                }
                log.info("  Converted {} stop orders from DistributedOrderBookSnapshot using StopOrderConverter", orders.size());
            }

            log.info("Successfully converted DistributedOrderBookSnapshot - Buy: {}, Sell: {}, Stop: {}, Total: {}",
                    buyOrders.size(), sellOrders.size(), stopOrders.size(), allOrders.size());

        } catch (Exception e) {
            log.error("Failed to convert DistributedOrderBookSnapshot", e);
        }
    }

    /**
     * Convert Order object to RedisSnapshotDocument.OrderDocument
     */
    @SuppressWarnings("java:S3776")
    private RedisSnapshotDocument.OrderDocument convertOrderToDocument(Object order) {
        try {
            if (order == null) {
                return null;
            }

            Class<?> orderClass = order.getClass();
            RedisSnapshotDocument.OrderDocument.OrderDocumentBuilder builder =
                    RedisSnapshotDocument.OrderDocument.builder();

            // Extract order fields using reflection
            // Order ID
            Method getOrderIdMethod = orderClass.getMethod("getOrderId");
            Object orderIdObj = getOrderIdMethod.invoke(order);
            if (orderIdObj != null) {
                builder.orderId(orderIdObj.toString());
            }

            // Member ID
            Method getMemberIdMethod = orderClass.getMethod("getMemberId");
            Object memberIdObj = getMemberIdMethod.invoke(order);
            if (memberIdObj instanceof Long longValue) {
                builder.memberId(longValue);
            }

            // Direction
            Method getDirectionMethod = orderClass.getMethod("getDirection");
            Object directionObj = getDirectionMethod.invoke(order);
            if (directionObj != null) {
                builder.direction(directionObj.toString());
            }

            // Type
            Method getTypeMethod = orderClass.getMethod("getType");
            Object typeObj = getTypeMethod.invoke(order);
            if (typeObj != null) {
                builder.type(typeObj.toString());
            }

            // Status
            Method getStatusMethod = orderClass.getMethod("getStatus");
            Object statusObj = getStatusMethod.invoke(order);
            if (statusObj != null) {
                builder.status(statusObj.toString());
            }

            // Price
            Method getPriceMethod = orderClass.getMethod("getPrice");
            Object priceObj = getPriceMethod.invoke(order);
            if (priceObj != null) {
                RedisSnapshotDocument.MoneyDocument priceDoc = convertMoneyToDocument(priceObj);
                builder.price(priceDoc);
            }

            // Quantity/Size (prefer getSize if available) - no try/catch fallback
            Object quantityObj = null;
            Method qtyMethod = findMethod(orderClass, "getSize", "getQuantity");
            if (qtyMethod != null) {
                quantityObj = qtyMethod.invoke(order);
            }
            if (quantityObj != null) {
                RedisSnapshotDocument.MoneyDocument quantityDoc = convertMoneyToDocument(quantityObj);
                builder.quantity(quantityDoc);
            }

            // Filled Quantity/Size (prefer getFilledSize if available) - no try/catch fallback
            Object filledQuantityObj = null;
            Method filledMethod = findMethod(orderClass, "getFilledSize", "getFilledQuantity");
            if (filledMethod != null) {
                filledQuantityObj = filledMethod.invoke(order);
            }
            if (filledQuantityObj != null) {
                RedisSnapshotDocument.MoneyDocument filledQuantityDoc = convertMoneyToDocument(filledQuantityObj);
                builder.filledQuantity(filledQuantityDoc);
            }

            // Position ID (if available)
                Method getPositionIdMethod = orderClass.getMethod("getPositionId");
                Object positionIdObj = getPositionIdMethod.invoke(order);
                if (positionIdObj instanceof Number number) {
                    builder.positionId(number.longValue());
                } else if (positionIdObj != null) {
                    builder.positionId(Long.parseLong(positionIdObj.toString()));
                }

            // Created Time
            Method getCreatedTimeMethod = orderClass.getMethod("getCreatedTime");
            Object createdTimeObj = getCreatedTimeMethod.invoke(order);
            if (createdTimeObj instanceof Long longValue) {
                builder.createdTime(Instant.ofEpochMilli(longValue));
            } else if (createdTimeObj instanceof Instant instant) {
                builder.createdTime(instant);
            }

            return builder.build();

        } catch (Exception e) {
            log.error("Failed to convert order to document", e);
            return null;
        }
    }

    /**
     * Convert Money object to RedisSnapshotDocument.MoneyDocument
     */
    private RedisSnapshotDocument.MoneyDocument convertMoneyToDocument(Object money) {
        if (money == null) {
            return null;
        }


        Class<?> moneyClass = money.getClass();
        RedisSnapshotDocument.MoneyDocument.MoneyDocumentBuilder builder =
                RedisSnapshotDocument.MoneyDocument.builder();

        // Extract amount
        builder.amount(extractAmount(money, moneyClass));

        // Extract scale
        builder.scale(extractScale(money, moneyClass));

        // Extract currency
        builder.currency(extractCurrency(money, moneyClass));

        return builder.build();
    }

    private String extractAmount(Object money, Class<?> moneyClass) {
        try {
            Method getAmountMethod = moneyClass.getMethod(GET_AMOUNT);
            Object amountObj = getAmountMethod.invoke(money);
            return amountObj != null ? amountObj.toString() : money.toString();
        } catch (Exception e) {
            return money.toString();
        }
    }

    private int extractScale(Object money, Class<?> moneyClass) {
        try {
            Method getScaleMethod = moneyClass.getMethod("getScale");
            Object scaleObj = getScaleMethod.invoke(money);
            if (scaleObj instanceof Integer integer) {
                return integer;
            }
        } catch (Exception ignored) {
            log.info("Failed to extract scale from {}", money);
        }
        return 8; // Default
    }

    private String extractCurrency(Object money, Class<?> moneyClass) {
        try {
            Method getCurrencyMethod = moneyClass.getMethod("getCurrency");
            Object currencyObj = getCurrencyMethod.invoke(money);
            return currencyObj != null ? currencyObj.toString() : "USD";
        } catch (Exception ignored) {
            log.info("Failed to extract currency from {}", money);
        }
        return "USD"; // Default
    }


    /**
     * Convert object from Redis to RedisSnapshotDocument
     */
    private RedisSnapshotDocument convertToRedisSnapshotDocument(Object obj) {
        try {
            if (obj instanceof RedisSnapshotDocument object) {
                return object;
            }

            // If it's a Map (LinkedHashMap from Redis), convert using ObjectMapper
            if (obj instanceof Map) {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.registerModule(new JavaTimeModule());
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

                // Directly convert Map to RedisSnapshotDocument
                return objectMapper.convertValue(obj, RedisSnapshotDocument.class);
            }

            log.error("Cannot convert object of type {} to RedisSnapshotDocument", obj.getClass().getSimpleName());
            return null;

        } catch (Exception e) {
            log.error("Failed to convert object to RedisSnapshotDocument", e);
            return null;
        }
    }

    /**
     * Convert generic snapshot using reflection
     */
    private void convertGenericSnapshot(Object snapshot,
                                        List<RedisSnapshotDocument.OrderDocument> allOrders) {
        try {
            // Try common method names
            String[] methodNames = {"getAllOrders", "getOrders", "getOrderList"};
            for (String methodName : methodNames) {
                Method method = snapshot.getClass().getMethod(methodName);
                Object result = method.invoke(snapshot);
                if (result instanceof List) {
                    List<RedisSnapshotDocument.OrderDocument> ordersList = convertOrderList(result);
                    allOrders.addAll(ordersList);
                    log.info("Extracted {} orders using method {}", allOrders.size(), methodName);
                    break;
                }
            }
        } catch (Exception e) {
            log.warn("Error converting generic snapshot", e);
        }
    }

    /**
     * Convert stop order list to RedisSnapshotDocument.OrderDocument list
     * Uses specialized StopOrderConverter for better handling of stop order fields
     */
    @SuppressWarnings("unchecked")
    private List<RedisSnapshotDocument.OrderDocument> convertStopOrderList(Object orderListObj) {
        List<RedisSnapshotDocument.OrderDocument> result = new ArrayList<>();

        if (orderListObj instanceof List<?> orderList) {
            for (Object orderObj : orderList) {
                try {
                    // Use specialized stop order converter
                    RedisSnapshotDocument.OrderDocument orderDoc = StopOrderConverter.fromObject(orderObj);
                    if (orderDoc != null) {
                        result.add(orderDoc);
                    }
                } catch (Exception e) {
                    log.warn("Failed to convert stop order object: {}", orderObj, e);
                }
            }
        }

        log.info("  Converted {} stop orders to documents using StopOrderConverter", result.size());
        if (!result.isEmpty()) {
            log.info("Stop order conversion details: First stop order type={}, stopPrice={}",
                    result.get(0).getType(),
                    result.get(0).getStopPrice() != null ? result.get(0).getStopPrice().getAmount() : "null");
        }
        return result;
    }

    /**
     * Convert order list to RedisSnapshotDocument.OrderDocument list
     */
    @SuppressWarnings("unchecked")
    private List<RedisSnapshotDocument.OrderDocument> convertOrderList(Object orderListObj) {
        List<RedisSnapshotDocument.OrderDocument> result = new ArrayList<>();

        if (orderListObj instanceof List) {
            List<?> orderList = (List<?>) orderListObj;
            for (Object orderObj : orderList) {
                RedisSnapshotDocument.OrderDocument orderDoc = convertSingleOrder(orderObj);
                if (orderDoc != null) {
                    result.add(orderDoc);
                }
            }
        }

        return result;
    }

    /**
     * Convert single order object to RedisSnapshotDocument.OrderDocument
     */
    private RedisSnapshotDocument.OrderDocument convertSingleOrder(Object orderObj) {
        if (orderObj == null) {
            return null;
        }

        try {
            Class<?> orderClass = orderObj.getClass();

            // Extract basic fields using reflection
            String orderId = extractStringField(orderObj, orderClass, "orderId", "id", "getOrderId", "getId");
            Long memberId = extractLongField(orderObj, orderClass, "memberId", "getMemberId");
            Long positionId = extractLongField(orderObj, orderClass, "positionId", "getPositionId");
            String direction = extractStringField(orderObj, orderClass, "direction", "side", "getDirection", "getSide");
            String type = extractStringField(orderObj, orderClass, "type", "orderType", "getType", "getOrderType");
            String status = extractStringField(orderObj, orderClass, "status", "orderStatus", "getStatus", "getOrderStatus");

            // Extract price and quantity
            RedisSnapshotDocument.MoneyDocument price = extractMoneyField(orderObj, orderClass, "price", "getPrice");
            RedisSnapshotDocument.MoneyDocument quantity = extractMoneyField(orderObj, orderClass, "quantity", "amount", "getQuantity", GET_AMOUNT, "size");
            RedisSnapshotDocument.MoneyDocument filledQuantity = extractMoneyField(orderObj, orderClass, "filledSize", "tradedAmount", "getFilledSize", "getTradedAmount");

            // Check if we have minimum required fields
            if (orderId == null) {
                log.warn("Order ID is null, cannot create OrderDocument for order type: {}", orderClass.getSimpleName());
                return null;
            }

            return RedisSnapshotDocument.OrderDocument.builder()
                    .orderId(orderId)
                    .memberId(memberId)
                    .positionId(positionId)
                    .direction(direction)
                    .type(type)
                    .status(status)
                    .price(price)
                    .quantity(quantity)
                    .filledQuantity(filledQuantity)
                    .createdTime(Instant.now())
                    .updatedTime(Instant.now())
                    .build();

        } catch (Exception e) {
            log.error("Error converting single order of type {}: {}",
                    orderObj.getClass().getSimpleName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * Extract String field using reflection
     */
    @SuppressWarnings("java:S3011")
    private String extractStringField(Object obj, Class<?> clazz, String... fieldNames) {
        for (String fieldName : fieldNames) {
            try {
                // Try field access
                Field field = clazz.getDeclaredField(fieldName);
                field.setAccessible(true);
                Object value = field.get(obj);
                if (value != null) {
                    return value.toString();
                }
                // Try method access
                Method method = clazz.getMethod(fieldName);
                method.setAccessible(true);
                Object methodValue = method.invoke(obj);
                if (methodValue != null) {
                    return methodValue.toString();
                }

            } catch (Exception e) {
                // Continue to next field name
            }
        }
        return null;
    }

    /**
     * Extract Long field using reflection
     */
    @SuppressWarnings({"java:S3776", "java:S3011"})
    private Long extractLongField(Object obj, Class<?> clazz, String... fieldNames) {
        for (String fieldName : fieldNames) {
            try {
                // Try field access
                Field field = clazz.getDeclaredField(fieldName);
                field.setAccessible(true);
                Object value = field.get(obj);
                if (value != null) {
                    if (value instanceof Long longValue) {
                        return longValue;
                    } else if (value instanceof Number numberValue) {
                        return numberValue.longValue();
                    } else {
                        return Long.parseLong(value.toString());
                    }
                }
                // Try method access
                Method method = clazz.getMethod(fieldName);
                method.setAccessible(true);
                Object methodValue = method.invoke(obj);
                if (methodValue != null) {
                    if (methodValue instanceof Long longValue) {
                        return longValue;
                    } else if (methodValue instanceof Number numberValue) {
                        return numberValue.longValue();
                    } else {
                        return Long.parseLong(methodValue.toString());
                    }
                }

            } catch (Exception e) {
                // Continue to next field name
            }
        }
        return null;
    }

    /**
     * Extract Money field using reflection
     */
    @SuppressWarnings("java:S3011")
    private RedisSnapshotDocument.MoneyDocument extractMoneyField(Object obj, Class<?> clazz, String... fieldNames) {
        for (String fieldName : fieldNames) {
            try {
                // Try field access
                Field field = clazz.getDeclaredField(fieldName);
                field.setAccessible(true);
                Object value = field.get(obj);
                if (value != null) {
                    return convertToMoneyDocument(value);
                }
                // Try method access
                Method method = clazz.getMethod(fieldName);
                method.setAccessible(true);
                Object methodValue = method.invoke(obj);
                if (methodValue != null) {
                    return convertToMoneyDocument(methodValue);
                }
            } catch (Exception e) {
                // Continue to next field name
            }
        }
        return null;
    }

    /**
     * Convert value to MoneyDocument
     */
    private RedisSnapshotDocument.MoneyDocument convertToMoneyDocument(Object value) {
        if (value == null) {
            return null;
        }

        // If it's already a string or number, create simple MoneyDocument
        if (value instanceof String || value instanceof Number) {
            return RedisSnapshotDocument.MoneyDocument.builder()
                    .amount(value.toString())
                    .currency("USD")
                    .scale(8)
                    .build();
        }

        // Try to extract from Money-like object using reflection
        try {
            Class<?> valueClass = value.getClass();
            String amount = extractStringField(value, valueClass, "amount", "value", GET_AMOUNT, "getValue");
            String currency = extractStringField(value, valueClass, "currency", "symbol", "getCurrency", "getSymbol");

            return RedisSnapshotDocument.MoneyDocument.builder()
                    .amount(amount != null ? amount : value.toString())
                    .currency(currency != null ? currency : "USD")
                    .scale(8)
                    .build();
        } catch (Exception e) {
            log.info("Error converting value to MoneyDocument, using toString(): {}", e.getMessage());
            return RedisSnapshotDocument.MoneyDocument.builder()
                    .amount(value.toString())
                    .currency("USD")
                    .scale(8)
                    .build();
        }
    }

    /**
     * Convert original price level to safe format (dots to underscores)
     */
    private String convertOriginalPriceLevelToSafe(String originalPriceLevel) {
        if (originalPriceLevel == null) {
            return null;
        }
        return originalPriceLevel.replace(".", "_");
    }
}
