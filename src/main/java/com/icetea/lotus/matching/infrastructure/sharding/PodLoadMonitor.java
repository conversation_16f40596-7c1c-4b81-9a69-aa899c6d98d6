package com.icetea.lotus.matching.infrastructure.sharding;

import com.icetea.lotus.matching.infrastructure.sharding.model.PodLoadInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.OperatingSystemMXBean;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Monitor load của tất cả pods trong cluster
 * Migrated từ future-core
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PodLoadMonitor {

    private final RedissonClient redissonClient;
    private final DecimalFormat df = new DecimalFormat("#.##");
    
    @Value("${matching-engine.pod-name:${HOSTNAME:matching-engine-pod}}")
    private String podName;
    
    // Constants
    private static final String POD_LOAD_MAP = "matching-engine:pod-load-map";
    private static final String POD_HEARTBEAT_MAP = "matching-engine:pod-heartbeat-map";
    
    // Local metrics tracking
    private final AtomicInteger orderCount = new AtomicInteger(0);
    private final AtomicLong totalLatency = new AtomicLong(0);
    private final AtomicInteger activeConnections = new AtomicInteger(0);
    private final AtomicLong totalOrdersProcessed = new AtomicLong(0);
    
    // Cache for pod load info
    private final Map<String, PodLoadInfo> loadInfoCache = new ConcurrentHashMap<>();
    
    /**
     * Cập nhật load info của pod hiện tại
     */
    @Scheduled(fixedRate = 5000) // Every 5 seconds
    public void updateCurrentPodLoad() {
        try {
            PodLoadInfo loadInfo = collectCurrentPodMetrics();
            
            // Store in Redis
            RMap<String, PodLoadInfo> podLoadMap = redissonClient.getMap(POD_LOAD_MAP);
            podLoadMap.put(podName, loadInfo);
            
            // Update heartbeat
            RMap<String, LocalDateTime> heartbeatMap = redissonClient.getMap(POD_HEARTBEAT_MAP);
            heartbeatMap.put(podName, LocalDateTime.now());
            
            // Update local cache
            loadInfoCache.put(podName, loadInfo);
            
            log.info("Pod Load Metrics - Pod: {}, CPU: {}%, Memory: {}%, Overall Load: {}",
                     podName,
                     df.format(loadInfo.getCpuUsage() * 100),
                     df.format(loadInfo.getMemoryUsage() * 100),
                     df.format(loadInfo.getOverallLoad()));
            
        } catch (Exception e) {
            log.error("Error updating pod load info", e);
        }
    }
    
    /**
     * Thu thập metrics của pod hiện tại
     */
    private PodLoadInfo collectCurrentPodMetrics() {
        OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        
        // CPU usage
        double cpuUsage = 0.0;
        try {
            if (osBean instanceof com.sun.management.OperatingSystemMXBean operatingSystemMXBean) {
                cpuUsage = operatingSystemMXBean.getProcessCpuLoad();
                if (cpuUsage < 0) {
                    cpuUsage = 0.0; // Not available
                }
            }
        } catch (Exception e) {
            log.info("Could not get CPU usage: {}", e.getMessage());
            cpuUsage = 0.0;
        }
        
        // Memory usage
        long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
        long maxMemory = memoryBean.getHeapMemoryUsage().getMax();
        double memoryUsage = maxMemory > 0 ? (double) usedMemory / maxMemory : 0.0;
        
        // Order processing metrics
        int currentOrderCount = orderCount.getAndSet(0);
        long currentTotalLatency = totalLatency.getAndSet(0);
        double avgLatency = currentOrderCount > 0 ? (double) currentTotalLatency / currentOrderCount : 0.0;
        
        PodLoadInfo loadInfo = PodLoadInfo.builder()
                .podName(podName)
                .cpuUsage(cpuUsage)
                .memoryUsage(memoryUsage)
                .networkUsage(0.0) // Network monitoring not implemented yet
                .diskUsage(0.0)
                .orderRate(currentOrderCount * 12) // Convert to per minute (5s interval * 12)
                .avgLatency(avgLatency)
                .activeConnections(activeConnections.get())
                .totalOrdersProcessed(totalOrdersProcessed.get())
                .healthy(true)
                .healthStatus("HEALTHY")
                .lastUpdated(LocalDateTime.now())
                .build();
        
        // Calculate overall load
        loadInfo.calculateOverallLoad();
        
        return loadInfo;
    }
    
    /**
     * Lấy load info của pod cụ thể
     */
    public PodLoadInfo getPodLoadInfo(String podName) {
        // Try cache first
        PodLoadInfo cached = loadInfoCache.get(podName);
        if (cached != null && cached.getLastUpdated().isAfter(LocalDateTime.now().minusSeconds(30))) {
            return cached;
        }
        
        // Get from Redis
        try {
            RMap<String, PodLoadInfo> podLoadMap = redissonClient.getMap(POD_LOAD_MAP);
            PodLoadInfo loadInfo = podLoadMap.get(podName);
            
            if (loadInfo != null) {
                loadInfoCache.put(podName, loadInfo);
            }
            
            return loadInfo;
        } catch (Exception e) {
            log.error("Error getting pod load info for {}", podName, e);
            return null;
        }
    }
    
    /**
     * Lấy load info của tất cả pods
     */
    public Map<String, PodLoadInfo> getAllPodLoadInfo() {
        try {
            RMap<String, PodLoadInfo> podLoadMap = redissonClient.getMap(POD_LOAD_MAP);
            Map<String, PodLoadInfo> allLoadInfo = podLoadMap.readAllMap();
            
            // Update cache
            loadInfoCache.putAll(allLoadInfo);
            
            return allLoadInfo;
        } catch (Exception e) {
            log.error("Error getting all pod load info", e);
            return loadInfoCache;
        }
    }
    
    /**
     * Lấy danh sách pods đang active
     */
    public Set<String> getActivePods() {
        try {
            RMap<String, LocalDateTime> heartbeatMap = redissonClient.getMap(POD_HEARTBEAT_MAP);
            Map<String, LocalDateTime> heartbeats = heartbeatMap.readAllMap();
            
            LocalDateTime threshold = LocalDateTime.now().minusSeconds(30);
            
            return heartbeats.entrySet().stream()
                    .filter(entry -> entry.getValue().isAfter(threshold))
                    .map(Map.Entry::getKey)
                    .collect(java.util.stream.Collectors.toSet());
                    
        } catch (Exception e) {
            log.error("Error getting active pods", e);
            return Set.of(podName); // Fallback to current pod
        }
    }
    
    /**
     * Record order processing metrics
     */
    public void recordOrderProcessed(long latencyMs) {
        orderCount.incrementAndGet();
        totalLatency.addAndGet(latencyMs);
        totalOrdersProcessed.incrementAndGet();
    }
    
    /**
     * Update active connections count
     */
    public void updateActiveConnections(int connections) {
        activeConnections.set(connections);
    }
    
    /**
     * Cleanup old pod data
     */
    @Scheduled(fixedRate = 60000) // Every minute
    public void cleanupOldPodData() {
        try {
            RMap<String, LocalDateTime> heartbeatMap = redissonClient.getMap(POD_HEARTBEAT_MAP);
            RMap<String, PodLoadInfo> podLoadMap = redissonClient.getMap(POD_LOAD_MAP);
            
            LocalDateTime threshold = LocalDateTime.now().minusMinutes(5);
            
            heartbeatMap.readAllMap().entrySet().stream()
                    .filter(entry -> entry.getValue().isBefore(threshold))
                    .forEach(entry -> {
                        String deadPod = entry.getKey();
                        heartbeatMap.remove(deadPod);
                        podLoadMap.remove(deadPod);
                        loadInfoCache.remove(deadPod);
                        log.info("Removed dead pod {} from monitoring", deadPod);
                    });
                    
        } catch (Exception e) {
            log.error("Error cleaning up old pod data", e);
        }
    }
}
