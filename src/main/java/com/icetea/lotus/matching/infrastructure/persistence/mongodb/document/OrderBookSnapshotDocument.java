package com.icetea.lotus.matching.infrastructure.persistence.mongodb.document;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * MongoDB Document cho Order Book Snapshot trong Matching Engine
 * Copy từ future-core với modifications cho cả spot và futures trading
 * 
 * <AUTHOR> nguyen
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "#{@mongoCollectionNameResolver.getOrderBookSnapshotsCollection()}")

public class OrderBookSnapshotDocument {

    @Id
    private String id;

    @Field("symbol")
    @Indexed
    private String symbol;

    @Field("trading_type")
    @Indexed
    private String tradingType; // "SPOT" or "FUTURES"

    @Field("version")
    private Long version;

    @Field("timestamp")
    @Indexed
    private Instant timestamp;

    @Field("buy_orders")
    private Map<String, List<OrderDocument>> buyOrders;

    @Field("sell_orders")
    private Map<String, List<OrderDocument>> sellOrders;

    @Field("all_orders")
    private List<OrderDocument> allOrders;

    @Field("stop_orders")
    private List<OrderDocument> stopOrders;

    @Field("metadata")
    private SnapshotMetadata metadata;

    @Field("created_at")
    private Instant createdAt;

    @Field("expires_at")
    private Instant expiresAt;

    /**
     * Nested document cho Order data - Copy từ future-core
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderDocument {
        @Field("order_id")
        private String orderId;

        @Field("member_id")
        private Long memberId;

        @Field("direction")
        private String direction; // BUY, SELL

        @Field("type")
        private String type; // LIMIT_PRICE, MARKET_PRICE

        @Field("status")
        private String status; // NEW, PARTIAL_FILLED, FILLED, CANCELED

        @Field("price")
        private MoneyDocument price;

        @Field("quantity")
        private MoneyDocument quantity;

        @Field("filled_quantity")
        private MoneyDocument filledQuantity;

        @Field("turnover")
        private String turnover;

        @Field("fee")
        private String fee;

        // Futures-specific fields
        @Field("leverage")
        private Integer leverage;

        @Field("stop_price")
        private MoneyDocument stopPrice;

        @Field("time_in_force")
        private String timeInForce;

        @Field("stp_mode")
        private String stpMode;

        @Field("created_at")
        private Instant createdAt;

        @Field("updated_at")
        private Instant updatedAt;
    }

    /**
     * Money document for price/quantity fields
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MoneyDocument {
        @Field("amount")
        private String amount;

        @Field("currency")
        private String currency;

        @Field("scale")
        private Integer scale;
    }

    /**
     * Snapshot metadata - Copy từ future-core
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SnapshotMetadata {
        @Field("total_orders")
        private Integer totalOrders;

        @Field("buy_orders_count")
        private Integer buyOrdersCount;

        @Field("sell_orders_count")
        private Integer sellOrdersCount;

        @Field("stop_orders_count")
        private Integer stopOrdersCount;

        @Field("price_levels_buy")
        private Integer priceLevelsBuy;

        @Field("price_levels_sell")
        private Integer priceLevelsSell;

        @Field("snapshot_size_bytes")
        private Long snapshotSizeBytes;

        @Field("compression_ratio")
        private Double compressionRatio;

        @Field("creation_time_micros")
        private Long creationTimeMicros;

        @Field("source_node")
        private String sourceNode;

        @Field("checksum")
        private String checksum;

        @Field("trading_type")
        private String tradingType; // "SPOT" or "FUTURES"

        @Field("engine_type")
        private String engineType; // "EXCHANGE" or "FUTURE_CORE"
    }

    /**
     * Generate unique ID cho snapshot
     */
    public static String generateId(String symbol, String tradingType, Long version) {
        return String.format("%s_%s_%d_%d", symbol, tradingType, version, System.currentTimeMillis());
    }

    /**
     * Calculate TTL cho snapshot (default 24 hours)
     */
    public static Instant calculateExpiryTime(int ttlHours) {
        return Instant.now().plusSeconds(ttlHours * 3600L);
    }
}
