package com.icetea.lotus.matching.infrastructure.messaging.filter;

import com.icetea.lotus.matching.domain.valueobject.Symbol;
import com.icetea.lotus.matching.infrastructure.sharding.DistributedMatchingEngineCoordinator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.listener.adapter.RecordFilterStrategy;
import org.springframework.stereotype.Component;

/**
 * A Kafka RecordFilterStrategy that filters messages based on symbol ownership.
 * This strategy ensures that a consumer only processes records for symbols
 * that are managed by its pod, as determined by the DistributedMatchingEngineCoordinator.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SymbolOwnershipFilterStrategy implements RecordFilterStrategy<String, String> {

    private final DistributedMatchingEngineCoordinator distributedCoordinator;

    /**
     * Filters incoming Kafka records.
     * IMPORTANT: Chỉ filter khi chắc chắn có pod khác sẽ xử lý message
     *
     * @param consumerRecord The consumer record to be checked.
     * @return {@code false} if the record should be processed, {@code true} if it should be discarded.
     */
    @Override
    public boolean filter(ConsumerRecord<String, String> consumerRecord) {
        String symbol = consumerRecord.key();

        // If the symbol is null or empty, we cannot determine ownership.
        // It's safer to not filter it and let the application logic handle it.
        if (symbol == null || symbol.isEmpty()) {
            log.warn("Received a record with a null or empty key on topic {}. Record will not be filtered.", consumerRecord.topic());
            return false;
        }

        try {
            // Check for ownership. The coordinator will tell us if this pod owns the symbol.
            boolean isOwned = distributedCoordinator.isSymbolOwnedByCurrentNode(Symbol.of(symbol));

            if (!isOwned) {
                // CRITICAL: Chỉ discard nếu chắc chắn có pod khác đang xử lý symbol này
                // Kiểm tra xem có pod nào khác đang sở hữu symbol không
                boolean hasOtherOwner = distributedCoordinator.hasSymbolOwner(Symbol.of(symbol));

                if (hasOtherOwner) {
                    log.info("Discarding record for symbol {} on topic {} as it is owned by another pod.", symbol, consumerRecord.topic());
                    return true; // Discard vì có pod khác xử lý
                } else {
                    log.warn("Symbol {} has no owner, processing locally to avoid message loss on topic {}", symbol, consumerRecord.topic());
                    return false; // Không discard để tránh mất message
                }
            }

            // Pod này sở hữu symbol, xử lý bình thường
            return false;

        } catch (Exception e) {
            // In case of any error during the ownership check (e.g., coordinator is unavailable),
            // it's safer to process the record to avoid message loss. The application logic
            // should have its own fallbacks.
            log.error("Error checking symbol ownership for '{}'. Defaulting to processing the record to avoid data loss.", symbol, e);
            return false;
        }
    }
}
