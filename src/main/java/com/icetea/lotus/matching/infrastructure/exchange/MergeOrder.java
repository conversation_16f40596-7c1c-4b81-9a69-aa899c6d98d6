package com.icetea.lotus.matching.infrastructure.exchange;

import com.icetea.lotus.matching.domain.entity.ExchangeOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * MergeOrder - Copy từ Exchange module
 * Quản lý danh sách orders tại cùng một price level
 * Implement exact same logic như Exchange.MergeOrder
 * 
 * <AUTHOR> nguyen
 */
public class MergeOrder {
    
    private static final Logger logger = LoggerFactory.getLogger(MergeOrder.class);
    
    // Danh sách orders tại price level này - FIFO order
    private final LinkedList<ExchangeOrder> orders = new LinkedList<>();
    
    // Lock để đảm bảo thread safety
    private final ReadWriteLock lock = new ReentrantReadWriteLock();
    
    /**
     * Thêm order vào cuối danh sách (FIFO)
     */
    public void addOrder(ExchangeOrder order) {
        lock.writeLock().lock();
        try {
            orders.addLast(order);
            logger.info("Added order {} to merge order, total orders: {}", 
                    order.getOrderId(), orders.size());
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * Thêm order vào đầu danh sách (priority)
     */
    public void addOrderFirst(ExchangeOrder order) {
        lock.writeLock().lock();
        try {
            orders.addFirst(order);
            logger.info("Added priority order {} to merge order, total orders: {}", 
                    order.getOrderId(), orders.size());
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * Xóa order khỏi danh sách
     */
    public boolean removeOrder(ExchangeOrder order) {
        lock.writeLock().lock();
        try {
            boolean removed = orders.remove(order);
            if (removed) {
                logger.info("Removed order {} from merge order, remaining orders: {}", 
                        order.getOrderId(), orders.size());
            }
            return removed;
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * Xóa order theo orderId
     */
    public boolean removeOrderById(String orderId) {
        lock.writeLock().lock();
        try {
            Iterator<ExchangeOrder> iterator = orders.iterator();
            while (iterator.hasNext()) {
                ExchangeOrder order = iterator.next();
                if (order.getOrderId().equals(orderId)) {
                    iterator.remove();
                    logger.info("Removed order {} from merge order, remaining orders: {}", 
                            orderId, orders.size());
                    return true;
                }
            }
            return false;
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * Lấy order đầu tiên (FIFO)
     */
    public ExchangeOrder getFirstOrder() {
        lock.readLock().lock();
        try {
            return orders.isEmpty() ? null : orders.getFirst();
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Lấy và xóa order đầu tiên
     */
    public ExchangeOrder pollFirstOrder() {
        lock.writeLock().lock();
        try {
            if (orders.isEmpty()) {
                return null;
            }
            ExchangeOrder order = orders.removeFirst();
            logger.info("Polled order {} from merge order, remaining orders: {}", 
                    order.getOrderId(), orders.size());
            return order;
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * Kiểm tra có orders không
     */
    public boolean isEmpty() {
        lock.readLock().lock();
        try {
            return orders.isEmpty();
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Lấy số lượng orders
     */
    public int size() {
        lock.readLock().lock();
        try {
            return orders.size();
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Lấy danh sách orders (copy để tránh concurrent modification)
     */
    public List<ExchangeOrder> getOrders() {
        lock.readLock().lock();
        try {
            return new LinkedList<>(orders);
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Iterator cho orders (thread-safe)
     */
    public Iterator<ExchangeOrder> iterator() {
        lock.readLock().lock();
        try {
            // Tạo copy để tránh concurrent modification
            List<ExchangeOrder> ordersCopy = new LinkedList<>(orders);
            return ordersCopy.iterator();
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Tìm order theo orderId
     */
    public ExchangeOrder findOrderById(String orderId) {
        lock.readLock().lock();
        try {
            for (ExchangeOrder order : orders) {
                if (order.getOrderId().equals(orderId)) {
                    return order;
                }
            }
            return null;
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Kiểm tra có chứa order không
     */
    public boolean containsOrder(ExchangeOrder order) {
        lock.readLock().lock();
        try {
            return orders.contains(order);
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Kiểm tra có chứa orderId không
     */
    public boolean containsOrderId(String orderId) {
        lock.readLock().lock();
        try {
            for (ExchangeOrder order : orders) {
                if (order.getOrderId().equals(orderId)) {
                    return true;
                }
            }
            return false;
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Clear tất cả orders
     */
    public void clear() {
        lock.writeLock().lock();
        try {
            int size = orders.size();
            orders.clear();
            logger.info("Cleared {} orders from merge order", size);
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * Lấy thông tin debug
     */
    public String getDebugInfo() {
        lock.readLock().lock();
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("MergeOrder[size=").append(orders.size()).append(", orders=[");
            for (int i = 0; i < orders.size(); i++) {
                if (i > 0) sb.append(", ");
                sb.append(orders.get(i).getOrderId());
            }
            sb.append("]]");
            return sb.toString();
        } finally {
            lock.readLock().unlock();
        }
    }
    
    @Override
    public String toString() {
        return getDebugInfo();
    }
}
