package com.icetea.lotus.matching.infrastructure.sharding;

import com.icetea.lotus.matching.infrastructure.sharding.model.PartitionConfig;
import com.icetea.lotus.matching.infrastructure.sharding.model.PartitionStrategy;
import com.icetea.lotus.matching.infrastructure.sharding.model.PodLoadInfo;
import com.icetea.lotus.matching.infrastructure.sharding.model.SymbolMetrics;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Partition-based Load Balancer cho multiple active instances
 * Chia mỗi symbol thành nhiều partitions dựa trên volume và price ranges
 * Migrated từ future-core
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PartitionBasedLoadBalancer {

    private final RedissonClient redissonClient;
    private final PodLoadMonitor podLoadMonitor;

    // Cache partition configurations
    private final Map<String, PartitionConfig> partitionConfigs = new ConcurrentHashMap<>();

    // Constants
    private static final String PARTITION_CONFIG_KEY = "matching-engine:partition-configs";
    private static final String PARTITION_ASSIGNMENTS_KEY = "matching-engine:partition-assignments";
    private static final int MAX_PARTITIONS = 8;
    private static final BigDecimal HIGH_VOLUME_THRESHOLD = new BigDecimal("1000000");
    private static final BigDecimal MEDIUM_VOLUME_THRESHOLD = new BigDecimal("100000");

    /**
     * Lấy tất cả partitions cho symbol
     */
    public List<String> getAllPartitions(String symbol) {
        PartitionConfig config = getPartitionConfig(symbol);
        if (config == null) {
            return List.of(symbol + ":default");
        }

        return config.getPartitions().stream()
                .map(p -> symbol + ":" + p.getPartitionId())
                .toList();
    }

    /**
     * Lấy partition configuration cho symbol
     */
    public PartitionConfig getPartitionConfig(String symbol) {
        // Try cache first
        PartitionConfig cached = partitionConfigs.get(symbol);
        if (cached != null) {
            return cached;
        }

        // Get from Redis
        try {
            RMap<String, PartitionConfig> configMap = redissonClient.getMap(PARTITION_CONFIG_KEY);
            PartitionConfig config = configMap.get(symbol);

            if (config != null) {
                partitionConfigs.put(symbol, config);
            }

            return config;
        } catch (Exception e) {
            log.error("Error getting partition config for symbol {}", symbol, e);
            return null;
        }
    }

    /**
     * Tạo partition configuration cho symbol dựa trên metrics
     */
    public PartitionConfig createPartitionConfig(String symbol, SymbolMetrics metrics) {
        try {
            PartitionStrategy strategy = determinePartitionStrategy(metrics);
            int partitionCount = calculateOptimalPartitionCount(metrics);

            List<PartitionConfig.PartitionInfo> partitions = createPartitions(strategy, partitionCount);

            PartitionConfig config = PartitionConfig.builder()
                    .symbol(symbol)
                    .partitionCount(partitionCount)
                    .strategy(strategy)
                    .partitions(partitions)
                    .lastUpdated(LocalDateTime.now())
                    .updatedBy("PartitionBasedLoadBalancer")
                    .volumeThreshold(HIGH_VOLUME_THRESHOLD)
                    .orderCountThreshold(1000)
                    .loadBalanceThreshold(0.8)
                    .autoRebalanceEnabled(true)
                    .rebalanceIntervalMinutes(5)
                    .build();

            // Store in Redis
            RMap<String, PartitionConfig> configMap = redissonClient.getMap(PARTITION_CONFIG_KEY);
            configMap.put(symbol, config);

            // Update cache
            partitionConfigs.put(symbol, config);

            log.info("Created partition config for symbol {}: strategy={}, partitions={}",
                    symbol, strategy, partitionCount);

            return config;

        } catch (Exception e) {
            log.error("Error creating partition config for symbol {}", symbol, e);
            return null;
        }
    }

    /**
     * Xác định partition strategy dựa trên metrics
     */
    private PartitionStrategy determinePartitionStrategy(SymbolMetrics metrics) {
        if (metrics.isHighVolume() && metrics.isHighFrequency()) {
            return PartitionStrategy.HYBRID;
        } else if (metrics.isHighVolume()) {
            return PartitionStrategy.VOLUME_BASED;
        } else if (metrics.isHighFrequency()) {
            return PartitionStrategy.HASH_BASED;
        } else if (metrics.getPriceVolatility().compareTo(new BigDecimal("0.1")) > 0) {
            return PartitionStrategy.PRICE_RANGE;
        } else {
            return PartitionStrategy.SEQUENTIAL;
        }
    }

    /**
     * Tính toán số partition tối ưu
     */
    private int calculateOptimalPartitionCount(SymbolMetrics metrics) {
        double loadScore = metrics.getLoadScore();

        if (loadScore > 0.9) {
            return Math.min(MAX_PARTITIONS, 8);
        } else if (loadScore > 0.7) {
            return Math.min(MAX_PARTITIONS, 4);
        } else if (loadScore > 0.5) {
            return Math.min(MAX_PARTITIONS, 2);
        } else {
            return 1;
        }
    }

    /**
     * Tạo partitions cho symbol
     */
    private List<PartitionConfig.PartitionInfo> createPartitions(PartitionStrategy strategy, int count) {
        List<PartitionConfig.PartitionInfo> partitions = new ArrayList<>();

        for (int i = 0; i < count; i++) {
            String partitionId = "partition-" + i;

            PartitionConfig.PartitionRange range = createPartitionRange(strategy, i, count);
            PartitionConfig.PartitionMetrics metrics = PartitionConfig.PartitionMetrics.builder()
                    .orderCount(0)
                    .totalVolume(BigDecimal.ZERO)
                    .avgLatency(0.0)
                    .loadScore(0.0)
                    .lastUpdated(LocalDateTime.now())
                    .build();

            PartitionConfig.PartitionInfo partition = PartitionConfig.PartitionInfo.builder()
                    .partitionId(partitionId)
                    .assignedPod(null) // Will be assigned later
                    .range(range)
                    .metrics(metrics)
                    .active(true)
                    .lastAssigned(null)
                    .build();

            partitions.add(partition);
        }

        return partitions;
    }

    /**
     * Tạo partition range dựa trên strategy
     */
    private PartitionConfig.PartitionRange createPartitionRange(PartitionStrategy strategy, int index, int total) {
        switch (strategy) {
            case HASH_BASED:
                return PartitionConfig.PartitionRange.builder()
                        .hashRange(index + "/" + total)
                        .build();

            case PRICE_RANGE:
                // Price range partitioning - distribute symbols by price ranges
                return PartitionConfig.PartitionRange.builder()
                        .minPrice(BigDecimal.ZERO)
                        .maxPrice(new BigDecimal("999999999"))
                        .build();

            case VOLUME_BASED:
                // Volume-based partitioning - distribute symbols by trading volume
                return PartitionConfig.PartitionRange.builder()
                        .minVolume(BigDecimal.ZERO)
                        .maxVolume(new BigDecimal("999999999"))
                        .build();

            default:
                return PartitionConfig.PartitionRange.builder().build();
        }
    }

    /**
     * Assign partitions to pods dựa trên load
     */
    public void assignPartitionsToPods(String symbol) {
        try {
            PartitionConfig config = getPartitionConfig(symbol);
            if (config == null) {
                log.warn("No partition config found for symbol {}", symbol);
                return;
            }

            Set<String> activePods = podLoadMonitor.getActivePods();
            if (activePods.isEmpty()) {
                log.warn("No active pods found for partition assignment");
                return;
            }

            Map<String, PodLoadInfo> podLoads = podLoadMonitor.getAllPodLoadInfo();

            // Sort pods by load (ascending)
            List<String> sortedPods = activePods.stream()
                    .filter(podLoads::containsKey)
                    .sorted((p1, p2) -> {
                        double load1 = podLoads.get(p1).getOverallLoad();
                        double load2 = podLoads.get(p2).getOverallLoad();
                        return Double.compare(load1, load2);
                    })
                    .toList();

            // Assign partitions to pods in round-robin fashion
            RMap<String, String> assignmentMap = redissonClient.getMap(PARTITION_ASSIGNMENTS_KEY);

            for (int i = 0; i < config.getPartitions().size(); i++) {
                PartitionConfig.PartitionInfo partition = config.getPartitions().get(i);
                String podToAssign = sortedPods.get(i % sortedPods.size());

                String partitionKey = symbol + ":" + partition.getPartitionId();
                assignmentMap.put(partitionKey, podToAssign);

                partition.setAssignedPod(podToAssign);
                partition.setLastAssigned(LocalDateTime.now());

                log.info("Assigned partition {} to pod {}", partitionKey, podToAssign);
            }

            // Update config in Redis
            RMap<String, PartitionConfig> configMap = redissonClient.getMap(PARTITION_CONFIG_KEY);
            configMap.put(symbol, config);

            // Update cache
            partitionConfigs.put(symbol, config);

        } catch (Exception e) {
            log.error("Error assigning partitions for symbol {}", symbol, e);
        }
    }

    /**
     * Lấy pod owner của partition
     */
    public String getPartitionOwner(String partitionKey) {
        try {
            RMap<String, String> assignmentMap = redissonClient.getMap(PARTITION_ASSIGNMENTS_KEY);
            return assignmentMap.get(partitionKey);
        } catch (Exception e) {
            log.error("Error getting partition owner for {}", partitionKey, e);
            return null;
        }
    }

    /**
     * Lấy active pods
     */
    public Set<String> getActivePods() {
        return podLoadMonitor.getActivePods();
    }

    /**
     * Lấy load balancing stats
     */
    public LoadBalancingStats getLoadBalancingStats() {
        Map<String, PodLoadInfo> allLoads = podLoadMonitor.getAllPodLoadInfo();

        return LoadBalancingStats.builder()
                .totalPods(allLoads.size())
                .activePods(getActivePods().size())
                .avgLoad(allLoads.values().stream()
                        .mapToDouble(PodLoadInfo::getOverallLoad)
                        .average()
                        .orElse(0.0))
                .maxLoad(allLoads.values().stream()
                        .mapToDouble(PodLoadInfo::getOverallLoad)
                        .max()
                        .orElse(0.0))
                .minLoad(allLoads.values().stream()
                        .mapToDouble(PodLoadInfo::getOverallLoad)
                        .min()
                        .orElse(0.0))
                .lastUpdated(LocalDateTime.now())
                .build();
    }

    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class LoadBalancingStats {
        private int totalPods;
        private int activePods;
        private double avgLoad;
        private double maxLoad;
        private double minLoad;
        private LocalDateTime lastUpdated;
    }
}
