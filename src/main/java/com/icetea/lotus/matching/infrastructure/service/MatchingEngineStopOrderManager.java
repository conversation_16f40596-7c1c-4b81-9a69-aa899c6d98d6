package com.icetea.lotus.matching.infrastructure.service;

import com.icetea.lotus.matching.domain.entity.ExchangeOrder;
import com.icetea.lotus.matching.domain.entity.Order;
import com.icetea.lotus.matching.domain.entity.SimpleStopOrder;
import com.icetea.lotus.matching.domain.enums.OrderStatus;
import com.icetea.lotus.matching.domain.enums.OrderType;
import com.icetea.lotus.matching.domain.enums.SpotOrderDirection;
import com.icetea.lotus.matching.domain.enums.SpotOrderStatus;
import com.icetea.lotus.matching.domain.enums.SpotOrderType;
import com.icetea.lotus.matching.domain.enums.StopOrderStrategy;
import com.icetea.lotus.matching.domain.valueobject.Money;
import com.icetea.lotus.matching.domain.valueobject.Symbol;
import com.icetea.lotus.matching.infrastructure.constants.CommonConstance;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Stop Order Manager for Matching Engine
 * <p>
 * Quản lý stop orders và trigger chúng khi điều kiện thỏa mãn.
 * Sử dụng sign-based detection để trigger universal cho tất cả stop types.
 *
 * <AUTHOR> nguyen
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MatchingEngineStopOrderManager {

    // Stop orders by symbol
    private final Map<String, CopyOnWriteArrayList<SimpleStopOrder>> spotStopOrdersBySymbol = new ConcurrentHashMap<>();
    private final Map<String, CopyOnWriteArrayList<SimpleStopOrder>> futureStopOrdersBySymbol = new ConcurrentHashMap<>();

    // Stop orders by ID for quick lookup
    private final Map<String, SimpleStopOrder> spotStopOrdersById = new ConcurrentHashMap<>();
    private final Map<String, SimpleStopOrder> futureStopOrdersById = new ConcurrentHashMap<>();

    /**
     * Set last price service
     */
    @Setter
    private LastPriceService lastPriceService;

    /**
     * Thêm stop order với sign detection initialization và strategy
     *
     * @param stopOrder    Stop order cần thêm
     * @param currentPrice Giá hiện tại để initialize sign
     * @param strategy     Strategy cho stop order (null = TRADITIONAL)
     * @return true nếu thêm thành công
     */
    public boolean addStopOrder(SimpleStopOrder stopOrder, BigDecimal currentPrice, StopOrderStrategy strategy, String type) {
        if (stopOrder != null) {
            // Set strategy trước khi add
            stopOrder.setStrategy(strategy);
        }
        return addStopOrder(stopOrder, currentPrice, type);
    }

    /**
     * Thêm stop order với sign detection initialization (backward compatibility)
     *
     * @param stopOrder    Stop order cần thêm
     * @param currentPrice Giá hiện tại để initialize sign
     * @return true nếu thêm thành công
     */
    public boolean addStopOrder(SimpleStopOrder stopOrder, BigDecimal currentPrice, String type) {
        if (stopOrder == null || !stopOrder.isValid()) {
            log.warn("Invalid stop order: {}", stopOrder);
            return false;
        }
        StopOrderQueue stopOrderQueue = getStopOrderQueue(type);
        // Check duplicate
        if (stopOrderQueue.stopOrderById().containsKey(stopOrder.getOrderId().getValue())) {
            log.warn("Stop order already exists: {}", stopOrder.getOrderId().getValue());
            return false;
        }

        // Chỉ initialize stop order nếu có giá khả dụng
        if (!stopOrder.isSignInitialized()) {
            BigDecimal initPrice = getInitializationPriceForAdd(stopOrder.getSymbol().getValue(), currentPrice, type);
            if (initPrice != null) {
                stopOrder.initializeSignDetection(Money.of(initPrice));

                String source = currentPrice != null ? "current_trade" : "last_price_cache";
                log.info("Initialized stop order {} with price: {} for symbol: {} (source: {})",
                        stopOrder.getOrderId().getValue(), initPrice, stopOrder.getSymbol().getValue(), source);
            } else {
                log.info("Stop order {} for symbol: {} will be initialized when first trade occurs",
                        stopOrder.getOrderId().getValue(), stopOrder.getSymbol().getValue());
            }
        }

        //   FIX: Cho phép add stop order ngay cả khi chưa initialize sign detection
        // Stop order sẽ được initialize khi có trade đầu tiên

        // Activate stop order
        stopOrder.activate();

        // Add to maps
        String symbol = stopOrder.getSymbol().getValue();
        stopOrderQueue.stopOrdersBySymbol().computeIfAbsent(symbol, k -> new CopyOnWriteArrayList<>()).add(stopOrder);
        stopOrderQueue.stopOrderById().put(stopOrder.getOrderId().getValue(), stopOrder);

        log.info("  Added stop order: {} with sign: {}", stopOrder.getOrderId().getValue(), stopOrder.getSignDescription());

        return true;
    }

    private StopOrderQueue getStopOrderQueue(String type) {
        Map<String, SimpleStopOrder> stopOrderById = new ConcurrentHashMap<>();
        Map<String, CopyOnWriteArrayList<SimpleStopOrder>> stopOrdersBySymbol = new ConcurrentHashMap<>();
        if (CommonConstance.SPOT.equals(type)) {
            stopOrderById = spotStopOrdersById;
            stopOrdersBySymbol = spotStopOrdersBySymbol;
        } else if (CommonConstance.FUTURE.equals(type)) {
            stopOrderById = futureStopOrdersById;
            stopOrdersBySymbol = futureStopOrdersBySymbol;
        }
        return new StopOrderQueue(stopOrderById, stopOrdersBySymbol);
    }

    private record StopOrderQueue(Map<String, SimpleStopOrder> stopOrderById,
                                  Map<String, CopyOnWriteArrayList<SimpleStopOrder>> stopOrdersBySymbol) {
    }

    /**
     * Kiểm tra và trigger stop orders bằng sign-based detection
     *
     * @param symbol       Symbol cần kiểm tra
     * @param currentPrice Giá hiện tại
     * @return List các ExchangeOrders được trigger
     */
    @SuppressWarnings("java:S3776")
    public List<Object> checkAndTriggerStopOrders(String symbol, BigDecimal currentPrice, String type) {
        List<Object> triggeredOrders = new ArrayList<>();
        CopyOnWriteArrayList<SimpleStopOrder> stopOrders = getStopOrderQueue(type).stopOrdersBySymbol.get(symbol);
        if (stopOrders == null || stopOrders.isEmpty()) {
            return triggeredOrders;
        }

        // Kiểm tra từng stop order
        List<SimpleStopOrder> toRemove = new ArrayList<>();

        for (SimpleStopOrder stopOrder : stopOrders) {
            // Only check active stop orders
            if (!stopOrder.isActive()) {
                continue;
            }

            // thì initialize ngay với last price có sẵn hoặc current price từ trade hiện tại
            if (!stopOrder.isSignInitialized()) {
                BigDecimal initPrice = getInitializationPrice(symbol, currentPrice, type);
                log.info("Initializing stop order {} with price: {} for symbol: {} (source: {})",
                        stopOrder.getOrderId().getValue(), initPrice, symbol,
                        initPrice.equals(currentPrice) ? "current_trade" : "last_price_cache");
                stopOrder.initializeSignDetection(Money.of(initPrice));

            }

            // SIGN-BASED DETECTION - Universal cho tất cả stop types
            boolean triggered = stopOrder.isTriggeredBySignChange(Money.of(currentPrice));

            if (triggered) {
                if (CommonConstance.SPOT.equals(type)) {
                    // Trigger stop order và tạo ExchangeOrder
                    ExchangeOrder triggeredOrder = createExchangeOrderFromStopOrder(stopOrder);
                    triggeredOrders.add(triggeredOrder);
                    toRemove.add(stopOrder);

                    log.info("Stop order triggered: {} at price: {} -> regular order: {} with status: {}",
                            stopOrder.getOrderId().getValue(), currentPrice, triggeredOrder.getOrderId(),
                            triggeredOrder.getStatus());
                } else if (CommonConstance.FUTURE.equals(type)) {
                    Order triggeredOrder = createFutureOrderFromStopOrder(stopOrder);
                    triggeredOrders.add(triggeredOrder);
                    toRemove.add(stopOrder);

                    log.info("Stop future order triggered: {} at price: {} -> regular order: {} with status: {}",
                            stopOrder.getOrderId().getValue(), currentPrice, triggeredOrder.getOrderId(),
                            triggeredOrder.getStatus());

                }
            }
        }

        // Remove triggered/cancelled orders
        for (SimpleStopOrder stopOrder : toRemove) {
            removeStopOrder(stopOrder.getOrderId().getValue(), type);
        }

        if (!triggeredOrders.isEmpty()) {
            log.info("Triggered {} stop orders for symbol: {} at price: {}",
                    triggeredOrders.size(), symbol, currentPrice);
        }

        return triggeredOrders;
    }

    private Order createFutureOrderFromStopOrder(SimpleStopOrder stopOrder) {
        OrderType orderType;
        BigDecimal orderPrice = switch (stopOrder.getStopOrderType()) {
            case SELL_RALLY_MARKET, STOP_MARKET, BUY_DIP_MARKET -> {
                orderType = OrderType.MARKET;
                yield stopOrder.getExecutionPrice().getValue();
            }
            case STOP_LOSS ->  {
                orderType = OrderType.STOP_LOSS;
                yield stopOrder.getExecutionPrice().getValue();
            }
            case TAKE_PROFIT ->   {
                orderType = OrderType.TAKE_PROFIT;
                yield stopOrder.getExecutionPrice().getValue();
            }
            case STOP_LIMIT, BUY_DIP_LIMIT, SELL_RALLY_LIMIT -> {
                orderType = OrderType.LIMIT;
                yield stopOrder.getExecutionPrice().getValue();
            }
            default -> throw new IllegalStateException("Unknown order type: " + stopOrder.getStopOrderType());
        };

        return futureOrderBuilder(stopOrder, orderType, orderPrice);
    }

    private static Order futureOrderBuilder(SimpleStopOrder stopOrder, OrderType orderType, BigDecimal orderPrice) {
        Order order = new Order();
        order.setOrderId(stopOrder.getOrderId());
        order.setSymbol(Symbol.of(stopOrder.getSymbol().getValue()));
        order.setType(orderType);
        order.setPrice(Money.of(orderPrice));
        order.setStatus(OrderStatus.TRIGGERED);
        order.setStopPrice(stopOrder.getStopPrice());
        order.setDirection(stopOrder.getDirection());
        order.setSize(stopOrder.getQuantity());
        order.setFilledSize(Money.ZERO);
        order.setMemberId(stopOrder.getMember().getId());
        order.setFee(stopOrder.getFee());
        order.setBaseSymbol(stopOrder.getSymbol().getQuoteCurrency());
        order.setLeverage(stopOrder.getLeverage());
        order.setPositionId(stopOrder.getPositionId());
        order.setTimestamp(Instant.ofEpochSecond(stopOrder.getTimestamp()));
        return order;
    }


    /**
     * Get stop orders for symbol (for snapshot)
     */
    public List<SimpleStopOrder> getStopOrdersForSymbol(String symbol, String type) {
        CopyOnWriteArrayList<SimpleStopOrder> stopOrders = getStopOrderQueue(type).stopOrdersBySymbol.get(symbol);
        if (stopOrders == null || stopOrders.isEmpty()) {
            return new ArrayList<>();
        }

        // Return copy to avoid concurrent modification
        return new ArrayList<>(stopOrders);
    }

    /**
     * Lấy giá để initialize stop order khi add (có thể không có current price)
     * Ưu tiên: last price từ cache/MongoDB > current price từ trade hiện tại
     * Trả về null nếu không có giá nào để stop order không được initialize
     */
    private BigDecimal getInitializationPriceForAdd(String symbol, BigDecimal currentPrice, String type) {
        try {
            // Thử lấy last price từ cache hoặc MongoDB
            if (lastPriceService != null) {
                BigDecimal lastPrice = lastPriceService.getLastPrice(symbol, type);
                if (lastPrice != null && lastPrice.compareTo(BigDecimal.ZERO) > 0) {
                    log.info("Using cached last price for symbol: {} = {}", symbol, lastPrice);
                    return lastPrice;
                }
            }

            // Nếu có current price từ trade hiện tại
            if (currentPrice != null && currentPrice.compareTo(BigDecimal.ZERO) > 0) {
                log.info("Using current trade price for symbol: {} = {}", symbol, currentPrice);
                return currentPrice;
            }

            // Không có giá nào - trả về null để stop order không được initialize
            log.info("No price available for symbol: {}, stop order will not be initialized until first trade", symbol);
            return null;

        } catch (Exception e) {
            log.warn("Error getting initialization price for symbol: {}: {}", symbol, e.getMessage());
            return null;
        }
    }

    /**
     * Lấy giá để initialize stop order khi có trade
     */
    private BigDecimal getInitializationPrice(String symbol, BigDecimal currentPrice, String type) {
        try {
            // Thử lấy last price từ cache hoặc MongoDB
            if (lastPriceService != null) {
                BigDecimal lastPrice = lastPriceService.getLastPrice(symbol, type);
                if (lastPrice != null && lastPrice.compareTo(BigDecimal.ZERO) > 0) {
                    log.info("Using cached last price for symbol: {} = {} (instead of current price: {})",
                            symbol, lastPrice, currentPrice);
                    return lastPrice;
                }
            }

            log.info("No cached last price found for symbol: {}, using current trade price: {}",
                    symbol, currentPrice);
            return currentPrice;

        } catch (Exception e) {
            log.warn("Error getting initialization price for symbol: {}, fallback to current price: {}",
                    symbol, currentPrice, e);
            return currentPrice;
        }
    }

    /**
     * Tạo ExchangeOrder từ triggered SimpleStopOrder
     * CHUYỂN TYPE THÀNH LIMIT_PRICE/MARKET_PRICE sau khi trigger
     *
     * @param stopOrder Stop order được trigger
     * @return ExchangeOrder để submit vào matching engine
     */
    private ExchangeOrder createExchangeOrderFromStopOrder(SimpleStopOrder stopOrder) {
        // Determine order type and price - CHUYỂN THÀNH REGULAR TYPE
        SpotOrderType orderType;
        BigDecimal orderPrice = switch (stopOrder.getStopOrderType()) {
            case SELL_RALLY_MARKET, STOP_MARKET, BUY_DIP_MARKET -> {
                //   CHUYỂN TYPE THÀNH MARKET_PRICE sau khi trigger
                orderType = SpotOrderType.MARKET_PRICE;
                yield null;
            }
            case STOP_LIMIT, STOP_LOSS, TAKE_PROFIT, BUY_DIP_LIMIT, SELL_RALLY_LIMIT -> {
                //   CHUYỂN TYPE THÀNH LIMIT_PRICE sau khi trigger
                orderType = SpotOrderType.LIMIT_PRICE;
                yield stopOrder.getExecutionPrice() != null ?
                        stopOrder.getExecutionPrice().getValue() :
                        stopOrder.getTriggerPrice().getValue();
            }
            default -> throw new IllegalStateException("Unknown stop order type: " + stopOrder.getStopOrderType());
        };

        // Convert direction
        SpotOrderDirection direction = convertDirection(stopOrder.getDirection());

        // Create ExchangeOrder
        ExchangeOrder exchangeOrder = new ExchangeOrder();
        exchangeOrder.setOrderId(stopOrder.getOrderId().getValue());
        exchangeOrder.setSymbol(stopOrder.getSymbol().getValue());
        exchangeOrder.setDirection(direction);
        exchangeOrder.setType(orderType);
        exchangeOrder.setPrice(orderPrice);
        exchangeOrder.setStopPrice(stopOrder.getTriggerPrice().getValue()); //   Giữ lại stop price
        exchangeOrder.setAmount(stopOrder.getQuantity().getValue());
        exchangeOrder.setTradedAmount(BigDecimal.ZERO);
        exchangeOrder.setTurnover(BigDecimal.ZERO);
        exchangeOrder.setStatus(SpotOrderStatus.TRADING);
        exchangeOrder.setTime(stopOrder.getTimestamp());
        //   FIX: Handle null Member gracefully
        if (stopOrder.getMember() != null) {
            exchangeOrder.setMemberId(stopOrder.getMember().getId());
        } else {
            // For testing or cases where Member is not set, use a default value
            exchangeOrder.setMemberId(0L);
            log.warn("Stop order {} has null Member, using default memberId: 0", stopOrder.getOrderId().getValue());
        }

        //   Parse và set coinSymbol và baseSymbol từ symbol
        parseCoinAndBaseSymbol(exchangeOrder, stopOrder.getSymbol().getValue());

        //   SET TRIGGER TIME VÀ TRIGGERED FLAG
        exchangeOrder.setTriggerTime(System.currentTimeMillis());
        exchangeOrder.setTriggered("true");

        return exchangeOrder;
    }

    /**
     * Convert OrderDirection to SpotOrderDirection
     */
    private SpotOrderDirection convertDirection(com.icetea.lotus.matching.domain.enums.OrderDirection direction) {
        return switch (direction) {
            case BUY -> SpotOrderDirection.BUY;
            case SELL -> SpotOrderDirection.SELL;
        };
    }

    /**
     * Parse coinSymbol và baseSymbol từ symbol và set vào order
     * Format: BTC/USDT -> coinSymbol=BTC, baseSymbol=USDT
     */
    private void parseCoinAndBaseSymbol(ExchangeOrder order, String symbol) {
        if (symbol != null && symbol.contains("/")) {
            String[] parts = symbol.split("/");
            if (parts.length == 2) {
                order.setCoinSymbol(parts[0].trim()); // BTC
                order.setBaseSymbol(parts[1].trim()); // USDT
            }
        }
    }

    /**
     * Remove stop order by ID
     *
     * @param orderId Order ID to remove
     */
    private void removeStopOrder(String orderId, String type) {
        StopOrderQueue stopOrders = getStopOrderQueue(type);
        SimpleStopOrder stopOrder = stopOrders.stopOrderById.remove(orderId);
        if (stopOrder == null) {
            return;
        }

        String symbol = stopOrder.getSymbol().getValue();
        CopyOnWriteArrayList<SimpleStopOrder> symbolOrders = stopOrders.stopOrdersBySymbol.get(symbol);
        if (symbolOrders != null) {
            symbolOrders.remove(stopOrder);

            // Clean up empty lists
            if (symbolOrders.isEmpty()) {
                stopOrders.stopOrdersBySymbol.remove(symbol);
            }
        }

        log.info("Removed stop order: {}", orderId);
    }

    /**
     * Cancel stop order by ID
     *
     * @param orderId Order ID to cancel
     * @return true if cancelled successfully
     */
    public boolean cancelStopOrder(String orderId, String type) {
        StopOrderQueue stopOrders = getStopOrderQueue(type);
        SimpleStopOrder stopOrder = stopOrders.stopOrderById.get(orderId);
        if (stopOrder == null) {
            log.warn("Stop order not found in queue for cancellation: {}", orderId);
        } else {
            removeStopOrder(orderId, type);
        }
        return true;
    }


    /**
     * Clear all stop orders (for testing)
     */
    public void clear() {
        spotStopOrdersBySymbol.clear();
        spotStopOrdersById.clear();
        log.info("Cleared all stop orders");
    }
}
