package com.icetea.lotus.matching.infrastructure.futurecore;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.matching.domain.entity.Member;
import com.icetea.lotus.matching.domain.entity.Order;
import com.icetea.lotus.matching.domain.entity.SimpleStopOrder;
import com.icetea.lotus.matching.domain.entity.Trade;
import com.icetea.lotus.matching.domain.enums.OrderDirection;
import com.icetea.lotus.matching.domain.enums.OrderStatus;
import com.icetea.lotus.matching.domain.enums.OrderType;
import com.icetea.lotus.matching.domain.enums.StopOrderStatus;
import com.icetea.lotus.matching.domain.enums.StopOrderStrategy;
import com.icetea.lotus.matching.domain.enums.StopOrderType;
import com.icetea.lotus.matching.domain.enums.TradeStatus;
import com.icetea.lotus.matching.domain.service.StopOrderStrategyDetector;
import com.icetea.lotus.matching.domain.valueobject.Money;
import com.icetea.lotus.matching.domain.valueobject.OrderId;
import com.icetea.lotus.matching.domain.valueobject.Symbol;
import com.icetea.lotus.matching.infrastructure.constants.CommonConstance;
import com.icetea.lotus.matching.infrastructure.messaging.dto.FutureTradePlateMessage;
import com.icetea.lotus.matching.infrastructure.messaging.producer.FutureCoreKafkaProducer;
import com.icetea.lotus.matching.infrastructure.persistence.redis.document.RedisSnapshotDocument;
import com.icetea.lotus.matching.infrastructure.persistence.redis.service.RedisSnapshotService;
import com.icetea.lotus.matching.infrastructure.persistence.redis.util.StopOrderConverter;
import com.icetea.lotus.matching.infrastructure.service.LastPriceService;
import com.icetea.lotus.matching.infrastructure.service.MatchingEngineStopOrderManager;
import com.icetea.lotus.matching.infrastructure.sharding.SymbolShardingManager;
import com.icetea.lotus.matching.infrastructure.stp.SelfTradePreventionService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.FUTURE;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.ORDERID;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.PRICE;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.SYMBOL;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.TIMESTAMP;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.TYPE;

/**
 * Future-Core Compatibility Service - REFACTORED
 * <p>
 * CORE RESPONSIBILITY: Matching Engine Operations Only
 * - Order matching algorithms (FIFO, Pro-Rata, Hybrid, TWAP)
 * - Trade generation
 * - Order book management
 * - Performance metrics
 * <p>
 * POST-TRADE PROCESSING: Handled by separate services
 * - Position management → PositionService
 * - Balance updates → WalletService
 * - Fee calculation → FeeService
 * - Settlement → SettlementService
 *
 * <AUTHOR> nguyen
 */
@RequiredArgsConstructor
@Service
public class FutureCoreCompatibilityService {

    private static final Logger logger = LoggerFactory.getLogger(FutureCoreCompatibilityService.class);

    // Map symbol -> FutureCoreMatchingEngine instance
    private final Map<String, FutureCoreMatchingEngine> engineMap = new ConcurrentHashMap<>();

    private final RedisSnapshotService snapshotService;

    private final SymbolShardingManager shardingManager;

    private final KafkaTemplate<String, String> kafkaTemplate;

    private final ObjectMapper objectMapper;

    private final FutureCoreKafkaProducer futureCoreKafkaProducer;

    private final FutureCoreMatchingEngine futureCoreMatchingEngine;

    private final SelfTradePreventionService stpService;

    private final MongoTemplate mongoTemplate;

    private final LastPriceService lastPriceService;

    private final ApplicationEventPublisher applicationEventPublisher;
    private final MatchingEngineStopOrderManager matchingEngineStopOrderManager;

    /**
     * Get or create matching engine for symbol
     */
    private FutureCoreMatchingEngine getOrCreateEngine(String symbol) {
        return engineMap.computeIfAbsent(symbol, s -> {
            FutureCoreMatchingEngine engine = new FutureCoreMatchingEngine(futureCoreKafkaProducer, stpService, mongoTemplate, lastPriceService, applicationEventPublisher, matchingEngineStopOrderManager);
            engine.setSymbol(s);
            logger.info("Created new FutureCoreMatchingEngine for symbol: {}", s);
            return engine;
        });
    }


    /**
     * Shutdown all engines
     */
    public void shutdown() {
        logger.info("Shutting down FutureCoreCompatibilityService with {} engines", engineMap.size());

        for (Map.Entry<String, FutureCoreMatchingEngine> entry : engineMap.entrySet()) {
            String symbol = entry.getKey();
            try {
                logger.info("Shutdown engine for symbol: {}", symbol);
            } catch (Exception e) {
                logger.error("Error shutting down engine for symbol: {}", symbol, e);
            }
        }

        engineMap.clear();
        logger.info("FutureCoreCompatibilityService shutdown completed");
    }

    // Missing methods for messaging compatibility


    /**
     * Process contract order for messaging - INTERNAL USE ONLY
     * This method assumes sharding validation has already been done by OrderRoutingConsumer
     */
    public FutureCoreTradeResult processContractOrderInternal(Object order, String commandType) {
        try {
            logger.info("Processing contract order: {}", order);

            // Convert contract order to domain Order
            Order domainOrder = convertContractOrderToDomain(order);
            if (domainOrder == null) {
                logger.warn("Failed to convert contract order to domain order");
                return FutureCoreTradeResult.error("INVALID_ORDER", "Invalid contract order format");
            }

            // Process through Future-Core matching engine directly
            String symbol = domainOrder.getSymbol().getValue();

            // Get or create matching engine for symbol
            FutureCoreMatchingEngine engine = getOrCreateEngine(symbol);

            // Handle for stop order
            if (Arrays.asList(OrderType.STOP_MARKET, OrderType.STOP_LIMIT, OrderType.STOP_LOSS, OrderType.TAKE_PROFIT).contains(domainOrder.getType())
                    && OrderStatus.NEW.equals(domainOrder.getStatus())) {
                return this.handleStopOrder(domainOrder, engine);
            }

            // Process through Future-Core logic với CAS operations
            List<Trade> trades = engine.processOrder(domainOrder, commandType);

            // Publish orderbook updates (use the same engine instance that processed the order)
            FutureTradePlateMessage.OrderBookSnapshot tradePlate = engine.createOrderBookSnapshot(symbol);
            // Get performance metrics
            PerformanceMetrics metrics = engine.getPerformanceMetrics();

            // Create result
            FutureCoreTradeResult result = FutureCoreTradeResult.builder()
                    .success(true)
                    .orderId(domainOrder.getOrderId().getValue())
                    .symbol(symbol)
                    .trades(trades)
                    .tradesCount(trades.size())
                    .algorithm(engine.getMatchingAlgorithm().name())
                    .performanceMetrics(metrics)
                    .message("Contract order processed successfully")
                    .completedOrder(order)
                    .tradePlate(tradePlate)
                    .timestamp(System.currentTimeMillis())
                    .build();

            logger.info("Successfully processed contract order: {} with {} trades",
                    domainOrder.getOrderId().getValue(), result.getTradesCount());

            return result;

        } catch (Exception e) {
            logger.error("Failed to process contract order", e);
            return FutureCoreTradeResult.error("CONTRACT_ORDER_ERROR",
                    "Contract order processing failed: " + e.getMessage());
        }
    }


    /**
     * Process contract order cancel for messaging - INTERNAL USE ONLY
     * This method assumes sharding validation has already been done by OrderRoutingConsumer
     */
    public FutureCoreTradeResult processContractOrderCancelInternal(Object cancelRequest) {
        try {
            logger.info("Processing contract order cancel: {}", cancelRequest);

            // Extract order ID and symbol from cancel request
            String orderId = extractOrderIdFromCancelRequest(cancelRequest);
            String symbol = extractSymbolFromCancelRequest(cancelRequest);
            OrderType type = extractTypeFromCancelRequest(cancelRequest);
            OrderStatus status = extractStatusFromCancelRequest(cancelRequest);
            Boolean isSTP = extractIsSTPFromCancelRequest(cancelRequest);

            if (orderId == null || symbol == null) {
                logger.warn("Invalid cancel request - missing orderId or symbol");
                return FutureCoreTradeResult.builder()
                        .success(false)
                        .status(TradeStatus.FAILED)
                        .orderId(orderId)
                        .symbol(symbol)
                        .cancelResult(cancelRequest)
                        .message("Missing orderId or symbol in cancel request")
                        .timestamp(System.currentTimeMillis())
                        .errorCode("INVALID_CANCEL_REQUEST")
                        .build();
            }

            // Get matching engine for symbol
            FutureCoreMatchingEngine engine = engineMap.get(symbol);
            if (engine == null) {
                logger.warn("No matching engine found for symbol: {}", symbol);
                return FutureCoreTradeResult.builder()
                        .success(false)
                        .status(TradeStatus.FAILED)
                        .orderId(orderId)
                        .symbol(symbol)
                        .cancelResult(cancelRequest)
                        .message("No matching engine for symbol: " + symbol)
                        .timestamp(System.currentTimeMillis())
                        .errorCode("SYMBOL_NOT_FOUND")
                        .build();
            }

            // Cancel order in matching engine
            boolean cancelled = engine.cancelOrder(orderId, type, status);
            // Build orderbook snapshot from the same engine instance
            FutureTradePlateMessage.OrderBookSnapshot tradePlate = engine.createOrderBookSnapshot(symbol);
            if (cancelled) {
                if (Boolean.TRUE.equals(isSTP)) {
                    logger.info("Successfully cancelled STP contract order: {} for symbol: {}", orderId, symbol);
                    return FutureCoreTradeResult.builder()
                            .success(true)
                            .orderId(orderId)
                            .status(TradeStatus.SUCCESS)
                            .errorCode(CommonConstance.SUCCESS)
                            .symbol(symbol)
                            .cancelResult(cancelRequest)
                            .isSTP(true)
                            .tradePlate(tradePlate)
                            .message("Contract order cancelled successfully")
                            .timestamp(System.currentTimeMillis())
                            .build();
                }

                logger.info("Successfully cancelled contract order: {} for symbol: {}", orderId, symbol);
                return FutureCoreTradeResult.builder()
                        .success(true)
                        .orderId(orderId)
                        .status(TradeStatus.SUCCESS)
                        .errorCode(CommonConstance.SUCCESS)
                        .symbol(symbol)
                        .cancelResult(cancelRequest)
                        .tradePlate(tradePlate)
                        .message("Contract order cancelled successfully")
                        .timestamp(System.currentTimeMillis())
                        .build();
            } else {
                logger.warn("Order not found in local pod for cancellation: {} for symbol: {}", orderId, symbol);

                // Special case: MARKET IOC orders are never added to order book. Treat as cancelled successfully.
                if (type != null && Arrays.asList(OrderType.STOP_MARKET, OrderType.STOP_LOSS, OrderType.TAKE_PROFIT, OrderType.MARKET).contains(type)) {
                    logger.info("Treating MARKET/IOC order {} as cancelled without order book entry for symbol: {}", orderId, symbol);
                    return FutureCoreTradeResult.builder()
                            .success(true)
                            .orderId(orderId)
                            .status(TradeStatus.SUCCESS)
                            .errorCode(CommonConstance.SUCCESS)
                            .symbol(symbol)
                            .cancelResult(cancelRequest)
                            .tradePlate(tradePlate)
                            .message("Market IOC order cancelled (not in order book)")
                            .timestamp(System.currentTimeMillis())
                            .build();
                }


                // Try cross-pod cancel if order not found locally
                return tryBroadcastCancelRequest(cancelRequest, orderId, symbol);
            }

        } catch (Exception e) {
            logger.error("Failed to process contract order cancel", e);
            return FutureCoreTradeResult.builder()
                    .success(false)
                    .status(TradeStatus.ERROR)
                    .message("Missing orderId or symbol in cancel request")
                    .timestamp(System.currentTimeMillis())
                    .cancelResult(cancelRequest)
                    .errorCode("CONTRACT_CANCEL_ERROR")
                    .build();
        }
    }

    private Boolean extractIsSTPFromCancelRequest(Object cancelRequest) {
        if (!(cancelRequest instanceof Map<?, ?> map)) {
            return false;
        }

        Object value = map.get("isSTP");
        if (value == null) {
            return false;
        }

        return Boolean.parseBoolean(value.toString());
    }

    private OrderStatus extractStatusFromCancelRequest(Object cancelRequest) {
        try {
            if (cancelRequest instanceof Map) {
                return OrderStatus.valueOf((String) ((Map<?, ?>) cancelRequest).get(CommonConstance.STATUS));
            }
            return null;
        } catch (Exception ex) {
            logger.error("Failed to extract status from cancel request", ex);
            return null;
        }
    }

    /**
     * Try to broadcast cancel request to all pods when order not found locally
     * This handles the case where cancel request is sent to wrong pod
     */
    private FutureCoreTradeResult tryBroadcastCancelRequest(Object cancelRequest, String orderId, String symbol) {
        try {
            logger.info("Broadcasting cancel request for order: {} symbol: {} to all pods", orderId, symbol);

            // Create broadcast cancel message
            Map<String, Object> broadcastMessage = new java.util.HashMap<>();
            broadcastMessage.put(TYPE, "BROADCAST_CANCEL");
            broadcastMessage.put(ORDERID, orderId);
            broadcastMessage.put(SYMBOL, symbol);
            broadcastMessage.put("originalRequest", cancelRequest);
            broadcastMessage.put("sourcePod", shardingManager.getPodName());
            broadcastMessage.put(TIMESTAMP, System.currentTimeMillis());
            broadcastMessage.put("reason", "Order not found in local pod, broadcasting to all pods");

            String messageJson = objectMapper.writeValueAsString(broadcastMessage);

            // Send to broadcast cancel topic - all pods will receive this
            kafkaTemplate.send("contract-order-cancel-broadcast", symbol, messageJson);

            logger.info("Broadcasted cancel request for order: {} to all pods via contract-order-cancel-broadcast topic", orderId);

            // Return pending result - actual cancellation will be handled by the pod that has the order
            return FutureCoreTradeResult.builder()
                    .success(true)
                    .status(TradeStatus.SUCCESS)
                    .orderId(orderId)
                    .symbol(symbol)
                    .message("Cancel request broadcasted to all pods - order not found locally")
                    .timestamp(System.currentTimeMillis())
                    .build();

        } catch (Exception e) {
            logger.error("Failed to broadcast cancel request for order: {} symbol: {}", orderId, symbol, e);
            return FutureCoreTradeResult.error("BROADCAST_CANCEL_FAILED",
                    "Failed to broadcast cancel request: " + e.getMessage());
        }
    }

    /**
     * Update funding rate
     */
    public void updateFundingRate(String symbol, BigDecimal rate) {
        try {
            logger.info("Updating funding rate for symbol {} to {}", symbol, rate);

            // Get matching engine for symbol
            FutureCoreMatchingEngine engine = engineMap.get(symbol);
            if (engine == null) {
                logger.warn("No matching engine found for symbol: {} when updating funding rate", symbol);
                return;
            }

            // Update funding rate in engine (if supported)
            // For now, just log the update as funding rate is typically handled by separate service
            logger.info("Funding rate updated for symbol: {} to rate: {}", symbol, rate);

        } catch (Exception e) {
            logger.error("Failed to update funding rate for symbol {}", symbol);
            throw e;
        }
    }

    /**
     * Convert contract order object to domain Order
     */
    private Order convertContractOrderToDomain(Object contractOrder) {
        try {
            if (contractOrder == null) {
                return null;
            }

            // Handle different contract order formats
            if (contractOrder instanceof Order order) {
                return order;
            }

            // Handle Map format (from JSON)
            if (contractOrder instanceof Map) {
                Map<String, Object> orderMap = (Map<String, Object>) contractOrder;
                return convertMapToOrder(orderMap);
            }

            logger.warn("Unsupported contract order format: {}", contractOrder.getClass());
            return null;

        } catch (Exception e) {
            logger.error("Failed to convert contract order to domain order", e);
            return null;
        }
    }

    /**
     * Convert Map to Order entity
     */
    private Order convertMapToOrder(Map<String, Object> orderMap) {
        try {
            // Extract required fields
            String orderId = String.valueOf(orderMap.get(ORDERID));
            String symbol = String.valueOf(orderMap.get(SYMBOL));
            Long memberId = ((Number) orderMap.get("memberId")).longValue();
            String direction = String.valueOf(orderMap.get("direction"));
            String type = String.valueOf(orderMap.get(TYPE));
            String price = String.valueOf(orderMap.get("price"));
            String quantity = String.valueOf(orderMap.get("size"));
            OrderStatus status = OrderStatus.valueOf(String.valueOf(orderMap.get(CommonConstance.STATUS)));
            String fee = String.valueOf(orderMap.get("fee"));
            String leverage = String.valueOf(orderMap.get("leverage"));
            String triggerPrice = String.valueOf(orderMap.get("triggerPrice"));
            String baseSymbol = String.valueOf(orderMap.get("baseSymbol"));
            Object posIdObj = orderMap.get("positionId");
            Long positionId = null;
            if (posIdObj instanceof Number number) {
                positionId = number.longValue();
            }

            if (orderId == null || symbol == null || direction == null || type == null || price == null || quantity == null) {
                logger.warn("Missing required fields in contract order map");
                return null;
            }

            return Order.builder()
                    .orderId(OrderId.of(orderId))
                    .symbol(Symbol.of(symbol))
                    .memberId(memberId)
                    .direction(OrderDirection.valueOf(direction.toUpperCase()))
                    .type(OrderType.valueOf(type.toUpperCase()))
                    .price(Money.of(price))
                    .size(Money.of(quantity))
                    .filledSize(Money.of("0"))
                    .status(status)
                    .fee(Money.of(fee))
                    .leverage(new BigDecimal(leverage))
                    .stopPrice(triggerPrice.equalsIgnoreCase("null") ? Money.ZERO : Money.of(triggerPrice))
                    .baseSymbol(baseSymbol)
                    .positionId(positionId)
                    .build();

        } catch (Exception e) {
            logger.error("Failed to convert map to order", e);
            return null;
        }
    }


    /**
     * Extract order ID from cancel request
     */
    private String extractOrderIdFromCancelRequest(Object cancelRequest) {
        try {
            if (cancelRequest instanceof Map) {
                return String.valueOf(((Map<?, ?>) cancelRequest).get("orderId"));
            }
            if (cancelRequest instanceof String string) {
                // Assume it's the order ID itself
                return string;
            }
            return null;
        } catch (Exception e) {
            logger.error("Failed to extract order ID from cancel request", e);
            return null;
        }
    }


    /**
     * Extract type from cancel request
     */
    private OrderType extractTypeFromCancelRequest(Object cancelRequest) {
        try {
            if (cancelRequest instanceof Map) {
                return OrderType.valueOf((String) ((Map<?, ?>) cancelRequest).get("type"));
            }
            return null;
        } catch (Exception e) {
            logger.error("Failed to extract type from cancel request", e);
            return null;
        }
    }

    /**
     * Extract symbol from cancel request
     */
    private String extractSymbolFromCancelRequest(Object cancelRequest) {
        try {
            if (cancelRequest instanceof Map) {
                return String.valueOf(((Map<?, ?>) cancelRequest).get(SYMBOL));
            }
            return null;
        } catch (Exception e) {
            logger.error("Failed to extract symbol from cancel request", e);
            return null;
        }
    }

    /**
     * Restore order book from database - Copy từ Exchange CoinTraderEvent pattern
     */
    @SuppressWarnings("java:S3776")
    public boolean restoreOrderBookFromDatabase(String symbol) {
        logger.info("Restoring futures order book from database for symbol: {}", symbol);

        try {
            // Try to load snapshot from Redis first
            Optional<RedisSnapshotDocument> snapshot = snapshotService.loadLatestSnapshot(symbol, FUTURE);

            if (snapshot.isPresent()) {
                logger.info("Found Redis snapshot for symbol: {}, restoring from snapshot", symbol);

                // Get or create matching engine for symbol
                FutureCoreMatchingEngine engine = getOrCreateEngine(symbol);

                // Convert Redis snapshot to format expected by engine
                Object snapshotData = convertRedisSnapshotToEngineFormat(snapshot.get());

                // Restore snapshot to engine
                boolean restored = engine.restoreFromSnapshot(snapshotData);

                if (!restored) {
                    logger.warn("Failed to restore snapshot to engine for symbol: {}", symbol);
                    // Continue to database fallback
                } else {
                    logger.info("Successfully restored futures stop order from snapshot for symbol: {}", symbol);

                    // Rebuild stop-order manager queue from Redis snapshot (authoritative source)
                    int restoredStops = 0;
                    RedisSnapshotDocument redisDoc = snapshot.get();
                    if (redisDoc.getStopOrders() != null && !redisDoc.getStopOrders().isEmpty()) {
                        for (RedisSnapshotDocument.OrderDocument orderDocument : redisDoc.getStopOrders()) {
                            SimpleStopOrder stopOrder = StopOrderConverter.fromDocument(orderDocument);
                            if (stopOrder != null) {
                                // Set symbol (not present in order document)
                                stopOrder.setSymbol(Symbol.of(symbol));

                                // Ensure strategy present as Spot does: detect when missing
                                if (stopOrder.getStrategy() == null) {
                                    BigDecimal lastPrice = lastPriceService.getLastPrice(symbol, FUTURE);
                                    StopOrderStrategy detected = StopOrderStrategyDetector.detectOptimalStrategy(
                                            stopOrder.getStopOrderType(), stopOrder.getDirection(), stopOrder.getTriggerPrice(), Money.of(lastPrice));
                                    stopOrder.setStrategy(detected);
                                }

                                matchingEngineStopOrderManager.addStopOrder(stopOrder, null, FUTURE);
                                restoredStops++;
                            }
                        }
                    }
                    logger.info("Restored {} stop orders into manager for symbol: {}", restoredStops, symbol);
                    // Publish trade plate after successful restore
                    FutureTradePlateMessage.OrderBookSnapshot tradePlate = engine.createOrderBookSnapshot(symbol);
                    FutureTradePlateMessage message = FutureTradePlateMessage.createTradePlateMessage(symbol, tradePlate);
                    futureCoreKafkaProducer.publishContractTradePlate(symbol, message);
                    return true;
                }
            }

            logger.info("No pending futures orders found for symbol: {}", symbol);
            return false;

        } catch (Exception e) {
            logger.error("Error restoring futures order book for symbol: {}", symbol, e);
            return false;
        }
    }

    /**
     * Initialize matching engine for symbol - Copy từ Exchange CoinTraderEvent pattern
     */
    public void initializeMatchingEngine(String symbol) {
        logger.info("Initializing futures matching engine for symbol: {}", symbol);

        try {
            // Get or create matching engine for symbol
            FutureCoreMatchingEngine engine = getOrCreateEngine(symbol);

            // Set engine as active and ready
            engine.setActive(true);

            logger.info("Futures matching engine initialized and ready for symbol: {}", symbol);

        } catch (Exception e) {
            logger.error("Error initializing futures matching engine for symbol: {}", symbol, e);
        }
    }

    /**
     * Get performance metrics for symbol
     */
    public PerformanceMetrics getPerformanceMetrics(String symbol) {
        FutureCoreMatchingEngine engine = engineMap.get(symbol);
        return engine != null ? engine.getPerformanceMetrics() :
                PerformanceMetrics.builder().build();
    }

    /**
     * Get order book snapshot for symbol
     */
    public Object getOrderBookSnapshot(String symbol) {
        FutureCoreMatchingEngine engine = engineMap.get(symbol);
        return engine != null ? engine.getOrderBookSnapshot() : null;
    }

    /**
     * Create order book snapshot for symbol - returns FutureTradePlateMessage.OrderBookSnapshot
     */
    public FutureTradePlateMessage.OrderBookSnapshot createOrderBookSnapshot(String symbol) {
        FutureCoreMatchingEngine engine = engineMap.get(symbol);
        if (engine != null) {
            return engine.createOrderBookSnapshot(symbol);
        } else {
            // Return empty snapshot if engine not found
            return FutureTradePlateMessage.OrderBookSnapshot.builder()
                    .symbol(symbol)
                    .asks(Collections.emptyList())
                    .bids(Collections.emptyList())
                    .build();
        }
    }

    /**
     * Reset performance metrics for symbol
     */
    public void resetPerformanceMetrics(String symbol) {
        FutureCoreMatchingEngine engine = engineMap.get(symbol);
        if (engine != null) {
            engine.resetPerformanceMetrics();
            logger.info("Reset performance metrics for symbol: {}", symbol);
        }
    }

    /**
     * Get order book for symbol
     */
    public Object getOrderBook(String symbol) {
        logger.info("Getting futures order book for symbol: {}", symbol);

        try {
            FutureCoreMatchingEngine engine = engineMap.get(symbol);
            if (engine != null) {
                // Get actual order book from engine
                return engine.getOrderBook();
            } else {
                // Return empty order book if engine not found
                return Map.of(
                        SYMBOL, symbol,
                        "bids", List.of(),
                        "asks", List.of(),
                        TIMESTAMP, System.currentTimeMillis()
                );
            }
        } catch (Exception e) {
            logger.error("Error getting futures order book for symbol: {}", symbol, e);
            return Map.of(
                    SYMBOL, symbol,
                    "error", e.getMessage(),
                    TIMESTAMP, System.currentTimeMillis()
            );
        }
    }

    // ===== REDIS SNAPSHOT METHODS =====

    /**
     * Save order book snapshot to Redis
     */
    public void saveOrderBookSnapshot(String symbol) {
        try {
            logger.info("Saving futures order book snapshot for symbol: {}", symbol);

            FutureCoreMatchingEngine engine = engineMap.get(symbol);
            if (engine == null) {
                logger.warn("No engine found for symbol: {}, cannot save snapshot", symbol);
                return;
            }

            // Get current order book snapshot from engine (for normal orders)
            Object orderBookSnapshot = engine.getOrderBookSnapshotForPersistence();

            // Build a Map snapshot that ALWAYS sources stop orders from MatchingEngineStopOrderManager
            Map<String, Object> snapshotMap = new HashMap<>();

            // Extract all normal orders from engine snapshot via reflection to ensure compatibility
            if (orderBookSnapshot != null) {
                var snapshotClass = orderBookSnapshot.getClass();
                var getAllOrders = snapshotClass.getMethod("getAllOrders");
                Object allOrdersObj = getAllOrders.invoke(orderBookSnapshot);
                if (allOrdersObj instanceof List<?> list && !list.isEmpty()) {
                    snapshotMap.put(CommonConstance.ALL_ORDERS, list);
                } else {
                    snapshotMap.put(CommonConstance.ALL_ORDERS, List.of());
                }
            } else {
                snapshotMap.put(CommonConstance.ALL_ORDERS, List.of());
            }

            // Inject stop orders from the central manager as requested
            List<SimpleStopOrder> futureStopOrders = matchingEngineStopOrderManager.getStopOrdersForSymbol(symbol, CommonConstance.FUTURE);
            snapshotMap.put(CommonConstance.STOP_ORDERS, futureStopOrders != null ? futureStopOrders : List.of());

            // Save snapshot to Redis using map-based converter (will convert orders + stop-orders properly)
            String snapshotId = snapshotService.saveSnapshot(symbol, FUTURE, snapshotMap);

            logger.info("Saved futures order book snapshot for symbol: {}, snapshotId: {} (orders={}, stopOrders={})",
                    symbol,
                    snapshotId,
                    ((List<?>) snapshotMap.getOrDefault(CommonConstance.ALL_ORDERS, List.of())).size(),
                    ((List<?>) snapshotMap.getOrDefault(CommonConstance.STOP_ORDERS, List.of())).size());

        } catch (Exception e) {
            logger.error("Error saving futures order book snapshot for symbol: {}", symbol, e);
        }
    }

    /**
     * Save snapshots for all active symbols
     */
    public void saveAllSnapshots() {
        try {
            logger.info("Saving futures snapshots for all {} active symbols", engineMap.size());

            for (String symbol : engineMap.keySet()) {
                saveOrderBookSnapshot(symbol);
            }

            logger.info("Completed saving futures snapshots for all active symbols");

        } catch (Exception e) {
            logger.error("Error saving futures snapshots for all symbols", e);
        }
    }

    /**
     * Convert Redis snapshot document to format expected by FutureCoreMatchingEngine
     */
    private Object convertRedisSnapshotToEngineFormat(RedisSnapshotDocument redisSnapshot) {
        try {
            Map<String, Object> engineSnapshot = new HashMap<>();
            String symbol = redisSnapshot.getSymbol();
            engineSnapshot.put(SYMBOL, symbol);
            engineSnapshot.put("tradingType", redisSnapshot.getTradingType());
            engineSnapshot.put("version", redisSnapshot.getVersion());
            engineSnapshot.put(TIMESTAMP, redisSnapshot.getTimestamp());

            // Pass through price-level maps (engine currently ignores, but keep for completeness)
            engineSnapshot.put("buyOrders", redisSnapshot.getBuyOrders());
            engineSnapshot.put("sellOrders", redisSnapshot.getSellOrders());

            // Convert order documents (snake_case) -> engine map (camelCase) expected by convertMapToOrder()
            List<Map<String, Object>> allOrders = new java.util.ArrayList<>();
            if (redisSnapshot.getAllOrders() != null) {
                for (RedisSnapshotDocument.OrderDocument od : redisSnapshot.getAllOrders()) {
                    Map<String, Object> m = convertOrderDocToEngineMap(od, symbol);
                    if (m != null) allOrders.add(m);
                }
            }
            engineSnapshot.put(CommonConstance.ALL_ORDERS, allOrders);

            List<Map<String, Object>> stopOrders = new java.util.ArrayList<>();
            if (redisSnapshot.getStopOrders() != null) {
                for (RedisSnapshotDocument.OrderDocument od : redisSnapshot.getStopOrders()) {
                    Map<String, Object> m = convertOrderDocToEngineMap(od, symbol);
                    if (m != null) stopOrders.add(m);
                }
            }
            engineSnapshot.put(CommonConstance.STOP_ORDERS, stopOrders);

            engineSnapshot.put("metadata", redisSnapshot.getMetadata());

            logger.info("Converted Redis snapshot to engine format for symbol: {} (orders={}, stopOrders={})",
                    symbol, allOrders.size(), stopOrders.size());
            return engineSnapshot;

        } catch (Exception e) {
            logger.error("Error converting Redis snapshot to engine format", e);
            return null;
        }
    }

    /**
     * Convert a Redis OrderDocument (snake_case) to engine-compatible map (camelCase)
     */
    @SuppressWarnings("java:S3776")
    private Map<String, Object> convertOrderDocToEngineMap(RedisSnapshotDocument.OrderDocument od, String symbol) {
        try {
            if (od == null) return Map.of();

            Map<String, Object> m = new HashMap<>();
            m.put("orderId", od.getOrderId());
            m.put("memberId", od.getMemberId());
            m.put("symbol", symbol);
            m.put("direction", od.getDirection() != null ? od.getDirection().toUpperCase() : null);
            if (od.getPositionId() != null) m.put("positionId", od.getPositionId());

            // Normalize type to domain enum values
            String rawType = od.getType() != null ? od.getType().toUpperCase() : null;
            if ("LIMIT_PRICE".equals(rawType)) rawType = "LIMIT";
            if ("MARKET_PRICE".equals(rawType)) rawType = "MARKET";
            // Map StopOrderType variants that don't exist in OrderType to closest equivalents
            if ("BUY_DIP_MARKET".equals(rawType) || "SELL_RALLY_MARKET".equals(rawType)) rawType = "STOP_MARKET";
            if ("BUY_DIP_LIMIT".equals(rawType) || "SELL_RALLY_LIMIT".equals(rawType)) rawType = "STOP_LIMIT";
            m.put("type", rawType);

            // Normalize status differences (e.g., PARTIAL_FILLED -> PARTIALLY_FILLED)
            String rawStatus = od.getStatus() != null ? od.getStatus().toUpperCase() : null;
            if ("PARTIAL_FILLED".equals(rawStatus)) rawStatus = "PARTIALLY_FILLED";
            // Some stop-order statuses (e.g., ACTIVE) are not valid OrderStatus; default to NEW if invalid
            if (rawStatus != null) {
                OrderStatus.valueOf(rawStatus);
            }
            m.put(CommonConstance.STATUS, rawStatus);

            // Money fields
            if (od.getPrice() != null) m.put("price", convertMoneyDocToMap(od.getPrice()));

            // quantity may be null in stored snapshot; fall back gracefully
            Map<String, Object> qty = od.getQuantity() != null ? convertMoneyDocToMap(od.getQuantity()) : null;
            if (qty == null) {
                // Use filledQuantity if available, else zero - ensures non-null for engine converter
                Map<String, Object> filled = od.getFilledQuantity() != null ? convertMoneyDocToMap(od.getFilledQuantity()) : null;
                qty = filled != null ? filled : convertMoneyDocToMap(new RedisSnapshotDocument.MoneyDocument("0", 8, "USD"));
            }
            // Provide both keys for compatibility: engine reads 'quantity', entity uses 'size'
            m.put("quantity", qty);
            m.put("size", qty);

            if (od.getFilledQuantity() != null) m.put("filledSize", convertMoneyDocToMap(od.getFilledQuantity()));

            // createdAt expects epoch millis
            Long createdAtMillis = od.getCreatedTime() != null ? od.getCreatedTime().toEpochMilli() : System.currentTimeMillis();
            m.put("createdAt", createdAtMillis);

            // Optional: stopPrice for stop orders
            if (od.getStopPrice() != null) m.put("stopPrice", convertMoneyDocToMap(od.getStopPrice()));

            return m;
        } catch (Exception ex) {
            logger.warn("Failed to convert OrderDocument to engine map: {}", ex.getMessage(), ex);
            return Map.of();
        }
    }

    private Map<String, Object> convertMoneyDocToMap(RedisSnapshotDocument.MoneyDocument md) {
        Map<String, Object> money = new HashMap<>();
        money.put("amount", md.getAmount());
        money.put("scale", md.getScale() != null ? md.getScale() : 8);
        money.put("currency", md.getCurrency() != null ? md.getCurrency() : "USD");
        return money;
    }


    private FutureCoreTradeResult handleStopOrder(Order order, FutureCoreMatchingEngine engine) {
        try {
            logger.info("Handling stop order: {}", order);
            // Convert Order to SimpleStopOrder
            SimpleStopOrder stopOrder = convertToSimpleStopOrder(order);
            if (stopOrder == null) {
                logger.error("Failed to convert order to stop order: {}", order);
            }
            this.matchingEngineStopOrderManager.addStopOrder(stopOrder, lastPriceService.getLastPrice(order.getSymbol().getValue(), FUTURE), FUTURE);

            return FutureCoreTradeResult.builder()
                    .success(true)
                    .orderId(order.getOrderId().getValue())
                    .symbol(order.getSymbol().getValue())
                    .trades(List.of())
                    .tradesCount(0)
                    .algorithm(engine.getMatchingAlgorithm().name())
                    .performanceMetrics(engine.getPerformanceMetrics())
                    .message("Contract order processed successfully")
                    .completedOrder(order)
                    .tradePlate(null)
                    .timestamp(System.currentTimeMillis())
                    .build();
        } catch (Exception e) {
            logger.error("Error handling stop order: {}", order, e);
        }
        return null;
    }

    private SimpleStopOrder convertToSimpleStopOrder(Order order) {
        try {
            StopOrderType stopOrderType;
            switch (order.getType()) {
                case STOP_MARKET -> stopOrderType = StopOrderType.STOP_MARKET;
                case STOP_LIMIT -> stopOrderType = StopOrderType.STOP_LIMIT;
                case STOP_LOSS -> stopOrderType = StopOrderType.STOP_LOSS;
                case TAKE_PROFIT -> stopOrderType = StopOrderType.TAKE_PROFIT;
                default -> stopOrderType = StopOrderType.BUY_DIP_MARKET;
            }
            // Detect optimal strategy
            StopOrderStrategy strategy = StopOrderStrategyDetector.detectOptimalStrategy(
                    stopOrderType, order.getDirection(), order.getStopPrice(), Money.of(lastPriceService.getLastPrice(order.getSymbol().getValue(), FUTURE)));

            return SimpleStopOrder.builder()
                    .orderId(order.getOrderId())
                    .member(Member.builder().id(order.getMemberId()).build())
                    .symbol(order.getSymbol())
                    .direction(order.getDirection())
                    .stopOrderType(stopOrderType)
                    .strategy(strategy)
                    .quantity(order.getSize())
                    .executionPrice(order.getPrice() != null ? order.getPrice() : null)
                    .triggerPrice(order.getStopPrice())
                    .status(StopOrderStatus.NEW)
                    .fee(order.getFee())
                    .leverage(order.getLeverage())
                    .positionId(order.getPositionId())
                    .timestamp(System.currentTimeMillis())
                    .build();

        } catch (Exception e) {
            logger.error("Error converting Order to SimpleStopOrder: {} - {}",
                    order.getOrderId(), e.getMessage(), e);
            return null;
        }
    }

    public void restoreLastTradePriceFromDatabase(String symbol) {
        try {
            logger.info("Restoring last trade price from database for symbol: {}", symbol);

            BigDecimal lastPrice = queryLastPrice(symbol);

            if (lastPrice != null) {
                this.lastPriceService.updateLastPrice(symbol, lastPrice, FUTURE);
                logger.info("Last trade price updated for symbol: {}", symbol);
            } else {
                logger.info("Last trade price not found for symbol: {}", symbol);
            }
        } catch (Exception e) {
            logger.error("Error restoring last trade price from database: {}", symbol, e);
        }
    }

    private BigDecimal queryLastPrice(String symbol) {
        try {
            String collectionName = "future_last_price";

            Query query = new Query(Criteria.where("symbol").is(symbol));
            Map<String, Object> lastPriceDoc = mongoTemplate.findOne(query, Map.class, collectionName);

            if (lastPriceDoc != null) {
                Object lastPrice = lastPriceDoc.get(PRICE);
                if (lastPrice != null) {
                    BigDecimal lastPriceValue = new BigDecimal(lastPrice.toString());
                    logger.info("Last price value = {}", lastPriceValue);
                    return lastPriceValue;
                }
            }

            logger.info("Last price doc is null");
            return null;
        } catch (Exception e) {
            logger.error("Error querying last price from database", e);
            return null;
        }
    }
}