package com.icetea.lotus.matching.infrastructure.persistence.mongodb.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Resolver để lấy collection names từ config thay vì hardcode
 * <PERSON><PERSON><PERSON>i quyết vấn đề collection name không được sử dụng từ application.yml
 * 
 * <AUTHOR> nguyen
 */
@Slf4j
@Component("mongoCollectionNameResolver")
public class MongoCollectionNameResolver {

    @Value("${matching-engine.mongodb.collections.order-book-snapshots:order_book_snapshots}")
    private String orderBookSnapshotsCollection;

    @Value("${matching-engine.mongodb.collections.trade-history:trade_history}")
    private String tradeHistoryCollection;

    @Value("${matching-engine.mongodb.collections.performance-metrics:performance_metrics}")
    private String performanceMetricsCollection;

    @Value("${matching-engine.mongodb.collections.system-events:system_events}")
    private String systemEventsCollection;

    /**
     * Lấy collection name cho order book snapshots
     */
    public String getOrderBookSnapshotsCollection() {
        log.info("Using order book snapshots collection: {}", orderBookSnapshotsCollection);
        return orderBookSnapshotsCollection;
    }

    /**
     * Lấy collection name cho trade history
     */
    public String getTradeHistoryCollection() {
        log.info("Using trade history collection: {}", tradeHistoryCollection);
        return tradeHistoryCollection;
    }

    /**
     * Lấy collection name cho performance metrics
     */
    public String getPerformanceMetricsCollection() {
        log.info("Using performance metrics collection: {}", performanceMetricsCollection);
        return performanceMetricsCollection;
    }

    /**
     * Lấy collection name cho system events
     */
    public String getSystemEventsCollection() {
        log.info("Using system events collection: {}", systemEventsCollection);
        return systemEventsCollection;
    }
}
