package com.icetea.lotus.matching.infrastructure.exchange;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.matching.domain.enums.SpotOrderDirection;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Converter để chuyển đổi TradePlate từ matching engine format sang market module format
 * 
 * <AUTHOR> nguyen
 */
@Component
public class TradePlateConverter {
    
    private static final Logger logger = LoggerFactory.getLogger(TradePlateConverter.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final int MAX_PUBLISH_DEPTH = 100; // hard cap per side when publishing

    /**
     * Chuyển đổi TradePlate từ matching engine format sang JSON format mà market module mong đợi
     *
     * @param tradePlate TradePlate từ matching engine
     * @return JSON string tương thích với market module
     */
    public String convertToMarketFormat(TradePlate tradePlate) {
        if (tradePlate == null) {
            logger.warn("Cannot convert null trade plate");
            return "{}";
        }
        
        try {
            // Tạo Map với format mà market module mong đợi
            Map<String, Object> marketTradePlate = new HashMap<>();
            
            // Basic fields
            marketTradePlate.put("symbol", tradePlate.getSymbol());
            marketTradePlate.put("direction", convertDirection(tradePlate.getDirection()));
            // Cap the published maxDepth at 100 regardless of internal configuration
            marketTradePlate.put("maxDepth", Math.min(tradePlate.getMaxDepth(), MAX_PUBLISH_DEPTH));
            
            // Price statistics
            marketTradePlate.put("maxAmount", tradePlate.getMaxAmount().doubleValue());
            marketTradePlate.put("minAmount", tradePlate.getMinAmount().doubleValue());
            marketTradePlate.put("highestPrice", tradePlate.getHighestPrice().doubleValue());
            marketTradePlate.put("lowestPrice", tradePlate.getLowestPrice().doubleValue());
            
            // Items
            List<Map<String, Object>> convertedItems = convertItems(tradePlate.getItems());
            marketTradePlate.put("items", convertedItems);
            
            String json = objectMapper.writeValueAsString(marketTradePlate);
            
            logger.info("Converted trade plate to market format: symbol={}, direction={}, items={}", 
                    tradePlate.getSymbol(), tradePlate.getDirection(), convertedItems.size());
            
            return json;
            
        } catch (JsonProcessingException e) {
            logger.error("Error converting trade plate to market format: {}", e.getMessage(), e);
            return "{}";
        }
    }
    
    /**
     * Chuyển đổi SpotOrderDirection sang String format mà market module mong đợi
     */
    private String convertDirection(SpotOrderDirection direction) {
        if (direction == null) {
            return "BUY"; // Default
        }
        
        return switch (direction) {
            case BUY -> "BUY";
            case SELL -> "SELL";
        };
    }
    
    /**
     * Chuyển đổi TradePlateItems sang format mà market module mong đợi
     */
    private List<Map<String, Object>> convertItems(List<TradePlateItem> items) {
        List<Map<String, Object>> convertedItems = new ArrayList<>();
        if (items == null || items.isEmpty()) {
            return convertedItems;
        }
        // Create a snapshot and enforce DESC order for performance and consistency across layers
        List<TradePlateItem> snapshot = new ArrayList<>(items);
        snapshot.sort((a, b) -> {
            if (a == null && b == null) return 0;
            if (a == null) return 1;
            if (b == null) return -1;
            return b.getPrice().compareTo(a.getPrice()); // DESC by price for both sides
        });
        int limit = Math.min(snapshot.size(), MAX_PUBLISH_DEPTH);
        for (int i = 0; i < limit; i++) {
            TradePlateItem item = snapshot.get(i);
            if (item != null) {
                Map<String, Object> convertedItem = new HashMap<>();
                convertedItem.put("orderId", item.getOrderId());
                convertedItem.put("price", item.getPrice().doubleValue());
                convertedItem.put("amount", item.getAmount().doubleValue());
                convertedItems.add(convertedItem);
            }
        }
        return convertedItems;
    }
    
    /**
     * Tạo empty trade plate JSON cho market module
     */
    public String createEmptyTradePlateJson(String symbol, SpotOrderDirection direction) {
        Map<String, Object> emptyTradePlate = new HashMap<>();
        emptyTradePlate.put("symbol", symbol);
        emptyTradePlate.put("direction", convertDirection(direction));
        emptyTradePlate.put("maxDepth", 100);
        emptyTradePlate.put("maxAmount", 0.0);
        emptyTradePlate.put("minAmount", 0.0);
        emptyTradePlate.put("highestPrice", 0.0);
        emptyTradePlate.put("lowestPrice", 0.0);
        emptyTradePlate.put("items", new ArrayList<>());
        
        try {
            return objectMapper.writeValueAsString(emptyTradePlate);
        } catch (JsonProcessingException e) {
            logger.error("Error creating empty trade plate JSON: {}", e.getMessage(), e);
            return "{}";
        }
    }
    
    /**
     * Validate trade plate data trước khi convert
     */
    public boolean isValidTradePlate(TradePlate tradePlate) {
        if (tradePlate == null) {
            return false;
        }
        
        if (tradePlate.getSymbol() == null || tradePlate.getSymbol().trim().isEmpty()) {
            logger.warn("Trade plate has invalid symbol: {}", tradePlate.getSymbol());
            return false;
        }
        
        if (tradePlate.getDirection() == null) {
            logger.warn("Trade plate has null direction for symbol: {}", tradePlate.getSymbol());
            return false;
        }
        
        return true;
    }
    
    /**
     * Log trade plate conversion details for debugging
     */
    public void logConversionDetails(TradePlate tradePlate, String convertedJson) {
        if (logger.isDebugEnabled()) {
            logger.info("=== Trade Plate Conversion Details ===");
            logger.info("Original - Symbol: {}, Direction: {}, Items: {}", 
                    tradePlate.getSymbol(), tradePlate.getDirection(), 
                    tradePlate.getItems() != null ? tradePlate.getItems().size() : 0);
            logger.info("Converted JSON length: {}", convertedJson.length());
            logger.info("Converted JSON preview: {}", 
                    convertedJson.length() > 200 ? convertedJson.substring(0, 200) + "..." : convertedJson);
        }
    }
    

}
