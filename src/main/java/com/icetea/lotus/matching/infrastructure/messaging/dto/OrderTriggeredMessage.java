package com.icetea.lotus.matching.infrastructure.messaging.dto;

import com.icetea.lotus.matching.domain.enums.OrderDirection;
import com.icetea.lotus.matching.domain.enums.OrderStatus;
import com.icetea.lotus.matching.domain.enums.OrderType;
import lombok.Builder;
import lombok.Data;
import lombok.Setter;

import java.math.BigDecimal;

@Builder
@Setter
@Data
public class OrderTriggeredMessage {
    private String orderId;
    private Long memberId;
    private String symbol;
    private BigDecimal price;
    private BigDecimal size;
    private BigDecimal filledSize;
    private BigDecimal triggerPrice;
    private OrderDirection direction;
    private OrderType type;
    private OrderStatus status;
}
