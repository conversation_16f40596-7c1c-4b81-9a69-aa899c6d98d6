package com.icetea.lotus.matching.infrastructure.engine;

import com.icetea.lotus.matching.domain.entity.Order;
import com.icetea.lotus.matching.domain.enums.OrderDirection;
import com.icetea.lotus.matching.domain.valueobject.Money;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentSkipListMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.StampedLock;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Đại diện cho một phân đoạn của sổ lệnh, chứa các lệnh trong một phạm vi giá cụ thể
 * Sử dụng ConcurrentSkipListMap để đảm bảo FIFO và hiệu suất tối ưu với early exit
 * Thiết kế key bao gồm timestamp để đảm bảo thứ tự FIFO
 * 
 * <AUTHOR> nguyen
 */
@Slf4j
public class OrderBookSegment {

    // Phạm vi giá của phân đoạn này
    @Getter
    private final PriceRange range;

    // Sử dụng ConcurrentSkipListMap cho BUY orders (giá cao nhất trước - reverse order)
    private final ConcurrentSkipListMap<BigDecimal, ConcurrentSkipListMap<TimestampedOrderKey, Order>> buyOrders;

    // Sử dụng ConcurrentSkipListMap cho SELL orders (giá thấp nhất trước - natural order)
    private final ConcurrentSkipListMap<BigDecimal, ConcurrentSkipListMap<TimestampedOrderKey, Order>> sellOrders;

    // OPTIMIZED: ConcurrentSkipListMap cho consistent performance và better cache locality
    private final ConcurrentSkipListMap<String, Order> allOrders;

    // Counter để tạo timestamp unique
    private final AtomicLong timestampCounter = new AtomicLong(0);

    // OPTIMIZATION: StampedLock for better read performance
    private final StampedLock lock = new StampedLock();

    // OPTIMIZATION: Cached best prices for faster access
    private final AtomicReference<BigDecimal> cachedBestBuyPrice = new AtomicReference<>();
    private final AtomicReference<BigDecimal> cachedBestSellPrice = new AtomicReference<>();
    private volatile long lastCacheUpdate = 0;
    
    /**
     * Khởi tạo OrderBookSegment với phạm vi giá cụ thể
     * @param range Phạm vi giá
     */
    public OrderBookSegment(PriceRange range) {
        this.range = range;
        // BUY orders: giá cao nhất trước (reverse order)
        this.buyOrders = new ConcurrentSkipListMap<>(Collections.reverseOrder());
        // SELL orders: giá thấp nhất trước (natural order)
        this.sellOrders = new ConcurrentSkipListMap<>();
        // OPTIMIZED: ConcurrentSkipListMap cho consistent O(log n) performance
        this.allOrders = new ConcurrentSkipListMap<>();
    }
    
    /**
     * Thêm lệnh vào phân đoạn
     * @param order Lệnh cần thêm
     */
    public void addOrder(Order order) {
        if (!range.contains(order.getPrice())) {
            log.warn("Lệnh có giá {} không nằm trong phạm vi {} của phân đoạn",
                    order.getPrice(), range);
            return;
        }

        // Thêm lệnh vào map allOrders
        allOrders.put(order.getOrderId().getValue(), order);

        // Tạo timestamped key để đảm bảo FIFO
        TimestampedOrderKey orderKey = new TimestampedOrderKey(
                order.getOrderId().getValue(),
                timestampCounter.incrementAndGet());

        BigDecimal price = order.getPrice().getValue();

        // Thêm lệnh vào ConcurrentSkipListMap theo hướng
        if (order.getDirection() == OrderDirection.BUY) {
            buyOrders.computeIfAbsent(price, k -> new ConcurrentSkipListMap<>())
                     .put(orderKey, order);
        } else {
            sellOrders.computeIfAbsent(price, k -> new ConcurrentSkipListMap<>())
                      .put(orderKey, order);
        }

        log.info("Đã thêm lệnh {} vào phân đoạn với giá {} và timestamp {}",
                order.getOrderId().getValue(), price, orderKey.getTimestamp());

        // OPTIMIZATION: Update cached best prices
        updateCachedBestPrices();
    }

    /**
     * Thêm lệnh vào phân đoạn với return value
     * @param order Lệnh cần thêm
     * @return true nếu thành công, false nếu không
     */
    public boolean addOrderWithResult(Order order) {
        try {
            addOrder(order);
            return true;
        } catch (Exception e) {
            log.error("Failed to add order {} to segment: {}", order.getOrderId(), e.getMessage());
            return false;
        }
    }
    
    /**
     * Xóa lệnh khỏi phân đoạn
     * @param order Lệnh cần xóa
     * @return true nếu xóa thành công, false nếu không
     */
    public boolean removeOrder(Order order) {
        // Xóa lệnh khỏi map allOrders
        Order removedOrder = allOrders.remove(order.getOrderId().getValue());
        if (removedOrder == null) {
            return false;
        }

        BigDecimal price = order.getPrice().getValue();
        String orderId = order.getOrderId().getValue();

        // Xóa lệnh khỏi ConcurrentSkipListMap theo hướng
        boolean removed = false;
        if (order.getDirection() == OrderDirection.BUY) {
            ConcurrentSkipListMap<TimestampedOrderKey, Order> priceLevel = buyOrders.get(price);
            if (priceLevel != null) {
                // Tìm và xóa order theo orderId
                removed = priceLevel.entrySet().removeIf(entry ->
                    entry.getKey().hasSameOrderId(orderId));

                // Nếu price level rỗng, xóa luôn
                if (priceLevel.isEmpty()) {
                    buyOrders.remove(price);
                }
            }
        } else {
            ConcurrentSkipListMap<TimestampedOrderKey, Order> priceLevel = sellOrders.get(price);
            if (priceLevel != null) {
                // Tìm và xóa order theo orderId
                removed = priceLevel.entrySet().removeIf(entry ->
                    entry.getKey().hasSameOrderId(orderId));

                // Nếu price level rỗng, xóa luôn
                if (priceLevel.isEmpty()) {
                    sellOrders.remove(price);
                }
            }
        }

        log.info("Đã xóa lệnh {} khỏi phân đoạn với giá {}, kết quả: {}",
                orderId, price, removed);

        // OPTIMIZATION: Update cached best prices after removal
        if (removed) {
            updateCachedBestPrices();
        }

        return removed;
    }
    
    /**
     * Lấy danh sách lệnh ở một mức giá
     * @param price Giá
     * @param direction Hướng lệnh
     * @return Danh sách lệnh theo thứ tự FIFO
     */
    public List<Order> getOrdersAtPrice(Money price, OrderDirection direction) {
        if (!range.contains(price)) {
            return Collections.emptyList();
        }

        BigDecimal priceValue = price.getValue();
        List<Order> result = new ArrayList<>();

        // Lấy danh sách lệnh từ ConcurrentSkipListMap theo hướng
        if (direction == OrderDirection.BUY) {
            ConcurrentSkipListMap<TimestampedOrderKey, Order> priceLevel = buyOrders.get(priceValue);
            if (priceLevel != null) {
                // ConcurrentSkipListMap đã đảm bảo thứ tự FIFO theo timestamp
                result.addAll(priceLevel.values());
            }
        } else {
            ConcurrentSkipListMap<TimestampedOrderKey, Order> priceLevel = sellOrders.get(priceValue);
            if (priceLevel != null) {
                // ConcurrentSkipListMap đã đảm bảo thứ tự FIFO theo timestamp
                result.addAll(priceLevel.values());
            }
        }

        return result;
    }
    
    /**
     * Lấy lệnh mua tốt nhất (giá cao nhất)
     * @return Entry chứa giá và danh sách lệnh theo thứ tự FIFO, hoặc null nếu không có lệnh mua
     */
    public Map.Entry<Money, List<Order>> getBestBuyEntry() {
        // ConcurrentSkipListMap với reverse order: giá cao nhất sẽ là firstEntry
        Map.Entry<BigDecimal, ConcurrentSkipListMap<TimestampedOrderKey, Order>> firstEntry = buyOrders.firstEntry();
        if (firstEntry == null || firstEntry.getValue().isEmpty()) {
            return null;
        }

        Money price = Money.of(firstEntry.getKey());
        List<Order> orders = new ArrayList<>(firstEntry.getValue().values());

        return Map.entry(price, orders);
    }

    /**
     * Lấy lệnh bán tốt nhất (giá thấp nhất)
     * @return Entry chứa giá và danh sách lệnh theo thứ tự FIFO, hoặc null nếu không có lệnh bán
     */
    public Map.Entry<Money, List<Order>> getBestSellEntry() {
        // ConcurrentSkipListMap với natural order: giá thấp nhất sẽ là firstEntry
        Map.Entry<BigDecimal, ConcurrentSkipListMap<TimestampedOrderKey, Order>> firstEntry = sellOrders.firstEntry();
        if (firstEntry == null || firstEntry.getValue().isEmpty()) {
            return null;
        }

        Money price = Money.of(firstEntry.getKey());
        List<Order> orders = new ArrayList<>(firstEntry.getValue().values());

        return Map.entry(price, orders);
    }
    
    /**
     * Kiểm tra xem phân đoạn có rỗng không
     * @return true nếu phân đoạn rỗng, false nếu không
     */
    public boolean isEmpty() {
        return allOrders.isEmpty();
    }
    
    /**
     * Lấy số lượng lệnh trong phân đoạn
     * @return Số lượng lệnh
     */
    public int size() {
        return allOrders.size();
    }
    
    /**
     * Lấy tất cả các lệnh trong phân đoạn với optimized iteration
     * @return Danh sách tất cả các lệnh
     */
    public List<Order> getAllOrders() {
        // OPTIMIZED: Pre-size ArrayList và use optimized iteration
        List<Order> result = new ArrayList<>(allOrders.size());
        result.addAll(allOrders.values());
        return result;
    }

    /**
     * Batch add orders for better performance
     * @param orders List of orders to add
     */
    public void addOrdersBatch(List<Order> orders) {
        if (orders == null || orders.isEmpty()) {
            return;
        }

        for (Order order : orders) {
            if (!range.contains(order.getPrice())) {
                log.warn("Lệnh có giá {} không nằm trong phạm vi {} của phân đoạn",
                        order.getPrice(), range);
                continue;
            }

            // Batch operations
            allOrders.put(order.getOrderId().getValue(), order);

            TimestampedOrderKey orderKey = new TimestampedOrderKey(
                    order.getOrderId().getValue(),
                    timestampCounter.incrementAndGet());

            BigDecimal price = order.getPrice().getValue();

            if (order.getDirection() == OrderDirection.BUY) {
                buyOrders.computeIfAbsent(price, k -> new ConcurrentSkipListMap<>())
                         .put(orderKey, order);
            } else {
                sellOrders.computeIfAbsent(price, k -> new ConcurrentSkipListMap<>())
                          .put(orderKey, order);
            }
        }

        log.info("Batch added {} orders to segment", orders.size());
    }

    /**
     * Lấy iterator cho BUY orders với early exit capability
     * @return Iterator cho BUY orders theo thứ tự giá cao nhất trước
     */
    public Iterator<Map.Entry<BigDecimal, ConcurrentSkipListMap<TimestampedOrderKey, Order>>> getBuyOrdersIterator() {
        return buyOrders.entrySet().iterator();
    }

    /**
     * Lấy iterator cho SELL orders với early exit capability
     * @return Iterator cho SELL orders theo thứ tự giá thấp nhất trước
     */
    public Iterator<Map.Entry<BigDecimal, ConcurrentSkipListMap<TimestampedOrderKey, Order>>> getSellOrdersIterator() {
        return sellOrders.entrySet().iterator();
    }

    // ===== OPTIMIZATION METHODS =====

    /**
     * OPTIMIZATION: Update cached best prices using StampedLock for better performance
     */
    private void updateCachedBestPrices() {
        long stamp = lock.writeLock();
        try {
            // Update best buy price (highest price)
            if (!buyOrders.isEmpty()) {
                BigDecimal bestBuyPrice = buyOrders.firstKey();
                cachedBestBuyPrice.set(bestBuyPrice);
            } else {
                cachedBestBuyPrice.set(null);
            }

            // Update best sell price (lowest price)
            if (!sellOrders.isEmpty()) {
                BigDecimal bestSellPrice = sellOrders.firstKey();
                cachedBestSellPrice.set(bestSellPrice);
            } else {
                cachedBestSellPrice.set(null);
            }

            lastCacheUpdate = System.currentTimeMillis();

        } finally {
            lock.unlockWrite(stamp);
        }
    }

    /**
     * OPTIMIZATION: Get best buy price with optimistic read
     * @return Best buy price or null if no buy orders
     */
    public BigDecimal getBestBuyPriceOptimized() {
        long stamp = lock.tryOptimisticRead();
        BigDecimal cachedPrice = cachedBestBuyPrice.get();

        if (!lock.validate(stamp)) {
            // Fallback to read lock if optimistic read failed
            stamp = lock.readLock();
            try {
                cachedPrice = cachedBestBuyPrice.get();
            } finally {
                lock.unlockRead(stamp);
            }
        }

        return cachedPrice;
    }

    /**
     * OPTIMIZATION: Get best sell price with optimistic read
     * @return Best sell price or null if no sell orders
     */
    public BigDecimal getBestSellPriceOptimized() {
        long stamp = lock.tryOptimisticRead();
        BigDecimal cachedPrice = cachedBestSellPrice.get();

        if (!lock.validate(stamp)) {
            // Fallback to read lock if optimistic read failed
            stamp = lock.readLock();
            try {
                cachedPrice = cachedBestSellPrice.get();
            } finally {
                lock.unlockRead(stamp);
            }
        }

        return cachedPrice;
    }

    /**
     * OPTIMIZATION: Check if cache is fresh (within 1ms)
     * @return true if cache is fresh
     */
    public boolean isCacheFresh() {
        return (System.currentTimeMillis() - lastCacheUpdate) < 1;
    }
}
