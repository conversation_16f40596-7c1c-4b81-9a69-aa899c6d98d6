package com.icetea.lotus.matching.infrastructure.engine;

import com.icetea.lotus.matching.domain.entity.Order;
import com.icetea.lotus.matching.domain.enums.OrderDirection;
import com.icetea.lotus.matching.domain.enums.OrderType;
import com.icetea.lotus.matching.domain.valueobject.Money;
import com.icetea.lotus.matching.domain.valueobject.OrderId;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeMap;
import java.util.concurrent.ConcurrentSkipListMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.StampedLock;

/**
 * Distributed Order Book from future-core
 * <p>
 * <PERSON><PERSON> lệnh phân tán, chia thành các phân đoạn theo phạm vi giá
 * - Optimized performance với ConcurrentSkipListMap
 * - Advanced caching mechanisms
 * - Segmented architecture cho scalability
 * - Price-time priority ordering
 *
 * <AUTHOR> nguyen
 */
@Slf4j
public class DistributedOrderBook {

    // Kích thước phân đoạn mặc định
    private static final BigDecimal DEFAULT_SEGMENT_SIZE = new BigDecimal("100");

    // Kích thước phân đoạn
    @Getter
    private final BigDecimal segmentSize;

    // OPTIMIZED: ConcurrentSkipListMap cho ordered access và better performance
    private final ConcurrentSkipListMap<PriceRange, OrderBookSegment> segments;

    // OPTIMIZED: ConcurrentSkipListMap cho better cache locality và ordered iteration
    private final ConcurrentSkipListMap<String, Order> allOrders;

    // OPTIMIZED: ConcurrentSkipListMap cho consistent performance
    private final ConcurrentSkipListMap<String, PriceRange> orderToPriceRange;

    // Optimizations: Cached data structures

    private volatile long lastModificationTime = System.currentTimeMillis();
    private final AtomicReference<TreeMap<Money, List<Order>>> cachedBuyOrders = new AtomicReference<>();
    private final AtomicReference<TreeMap<Money, List<Order>>> cachedSellOrders = new AtomicReference<>();

    private final StampedLock optimizedLock = new StampedLock(); // OPTIMIZATION: Better read performance
    private static final long CACHE_VALIDITY_MS = 100; // Cache valid for 100ms

    /**
     * Khởi tạo DistributedOrderBook với kích thước phân đoạn mặc định
     */
    public DistributedOrderBook() {
        this(DEFAULT_SEGMENT_SIZE);
    }

    /**
     * Khởi tạo DistributedOrderBook với kích thước phân đoạn cụ thể
     * OPTIMIZED: Sử dụng ConcurrentSkipListMap cho better performance
     *
     * @param segmentSize Kích thước phân đoạn
     */
    public DistributedOrderBook(BigDecimal segmentSize) {
        this.segmentSize = segmentSize;
        // OPTIMIZED: ConcurrentSkipListMap provides O(log n) operations with better cache locality
        this.segments = new ConcurrentSkipListMap<>();
        this.allOrders = new ConcurrentSkipListMap<>();
        this.orderToPriceRange = new ConcurrentSkipListMap<>();
    }

    /**
     * Thêm lệnh vào sổ lệnh với optimizations
     *
     * @param order Lệnh cần thêm
     */
    public void addOrder(Order order) {
        String orderIdStr = order.getOrderId().getValue();

        // Nếu là lệnh thị trường, chỉ lưu vào map allOrders
        if (Arrays.asList(OrderType.STOP_MARKET, OrderType.STOP_LOSS, OrderType.TAKE_PROFIT, OrderType.MARKET).contains(order.getType())) {
            allOrders.put(orderIdStr, order);
            log.info("Market order {} added to allOrders only (IOC behavior)", orderIdStr);
            return;
        }

        // Optimized: Pre-calculate range once
        PriceRange range = PriceRange.forPrice(order.getPrice(), segmentSize);

        // Optimized: Batch operations to reduce contention
        OrderBookSegment segment = segments.computeIfAbsent(range, OrderBookSegment::new);
        segment.addOrder(order);

        // Optimized: Batch map updates
        allOrders.put(orderIdStr, order);
        orderToPriceRange.put(orderIdStr, range);
        log.info("Limit order {} added to segments and allOrders", orderIdStr);
    }

    /**
     * Batch add orders for better performance
     *
     * @param orders List of orders to add
     */
    public void addOrdersBatch(List<Order> orders) {
        if (orders == null || orders.isEmpty()) {
            return;
        }

        long stamp = optimizedLock.writeLock();
        try {
            for (Order order : orders) {
                addOrderInternal(order);
            }

            log.info("Added {} orders in batch", orders.size());
        } finally {
            optimizedLock.unlockWrite(stamp);
        }
    }

    /**
     * Xóa lệnh khỏi sổ lệnh với optimizations
     *
     * @param orderId ID lệnh cần xóa
     * @return Lệnh đã xóa, hoặc null nếu không tìm thấy
     */
    public Order removeOrder(OrderId orderId) {
        String orderIdStr = orderId.getValue();

        log.info("DEBUG: DistributedOrderBook.removeOrder - Bắt đầu xóa lệnh {}, total orders: {}",
                orderIdStr, allOrders.size());

        // Optimized: Single map operation
        Order order = allOrders.remove(orderIdStr);
        if (order == null) {
            log.info("DEBUG: DistributedOrderBook.removeOrder - Lệnh {} không tìm thấy trong allOrders", orderIdStr);
            return null;
        }

        log.info("DEBUG: DistributedOrderBook.removeOrder - Tìm thấy lệnh {}, type: {}, price: {}",
                orderIdStr, order.getType(), order.getPrice());

        // Nếu là lệnh thị trường, không cần xử lý phân đoạn
        if (Arrays.asList(OrderType.STOP_MARKET, OrderType.STOP_LOSS, OrderType.TAKE_PROFIT, OrderType.MARKET).contains(order.getType())) {
            log.info("DEBUG: DistributedOrderBook.removeOrder - Lệnh {} là market order, bỏ qua segment processing", orderIdStr);
            return order;
        }

        // Optimized: Batch removal operations
        PriceRange range = orderToPriceRange.remove(orderIdStr);
        if (range == null) {
            log.warn("DEBUG: DistributedOrderBook.removeOrder - Không tìm thấy phạm vi giá cho lệnh {}", orderIdStr);

            return order;
        }

        log.info("DEBUG: DistributedOrderBook.removeOrder - Tìm thấy price range {} cho lệnh {}", range, orderIdStr);

        OrderBookSegment segment = segments.get(range);
        if (segment == null) {
            log.warn("DEBUG: DistributedOrderBook.removeOrder - Không tìm thấy phân đoạn cho phạm vi giá {}", range);

            return order;
        }

        log.info("DEBUG: DistributedOrderBook.removeOrder - Tìm thấy segment cho range {}, segment size: {}",
                range, segment.size());

        // Remove from segment
        boolean segmentRemoved = segment.removeOrder(order);
        log.info("DEBUG: DistributedOrderBook.removeOrder - Kết quả xóa khỏi segment: {}, segment size sau khi xóa: {}",
                segmentRemoved, segment.size());

        // Optimized: Clean up empty segments
        if (segment.isEmpty()) {
            segments.remove(range);
            log.info("DEBUG: DistributedOrderBook.removeOrder - Đã xóa segment rỗng cho range {}", range);
        }


        log.info("DEBUG: DistributedOrderBook.removeOrder - Hoàn thành xóa lệnh {}, total orders: {}",
                orderIdStr, allOrders.size());
        return order;
    }

    /**
     * Lấy lệnh theo ID
     *
     * @param orderId ID lệnh
     * @return Optional chứa lệnh, hoặc empty nếu không tìm thấy
     */
    public Optional<Order> getOrder(OrderId orderId) {
        return Optional.ofNullable(allOrders.get(orderId.getValue()));
    }

    /**
     * Lấy danh sách lệnh ở một mức giá
     *
     * @param price     Giá
     * @param direction Hướng lệnh
     * @return Danh sách lệnh
     */
    public List<Order> getOrdersAtPrice(Money price, OrderDirection direction) {
        // Tính toán phạm vi giá cho mức giá này
        PriceRange range = PriceRange.forPrice(price, segmentSize);

        // Lấy phân đoạn cho phạm vi giá này
        OrderBookSegment segment = segments.get(range);
        if (segment == null) {
            return Collections.emptyList();
        }

        // Lấy danh sách lệnh ở mức giá này từ phân đoạn
        return segment.getOrdersAtPrice(price, direction);
    }

    /**
     * Lấy lệnh mua tốt nhất (giá cao nhất)
     *
     * @return Entry chứa giá và danh sách lệnh, hoặc null nếu không có lệnh mua
     */
    public Map.Entry<Money, List<Order>> getBestBuyEntry() {
        return segments.values().stream()
                .map(OrderBookSegment::getBestBuyEntry)
                .filter(Objects::nonNull)
                .max(Map.Entry.comparingByKey())
                .map(entry -> {
                    List<Order> orderList = new ArrayList<>(entry.getValue());
                    return Map.entry(entry.getKey(), orderList);
                })
                .orElse(null);
    }

    /**
     * Lấy lệnh bán tốt nhất (giá thấp nhất)
     *
     * @return Entry chứa giá và danh sách lệnh, hoặc null nếu không có lệnh bán
     */
    public Map.Entry<Money, List<Order>> getBestSellEntry() {
        return segments.values().stream()
                .map(OrderBookSegment::getBestSellEntry)
                .filter(Objects::nonNull)
                .min(Map.Entry.comparingByKey())
                .map(entry -> {
                    List<Order> orderList = new ArrayList<>(entry.getValue());
                    return Map.entry(entry.getKey(), orderList);
                })
                .orElse(null);
    }

    /**
     * Lấy số lượng lệnh trong sổ lệnh
     *
     * @return Số lượng lệnh
     */
    public int size() {
        return allOrders.size();
    }

    /**
     * Kiểm tra xem sổ lệnh có rỗng không
     *
     * @return true nếu sổ lệnh rỗng, false nếu không
     */
    public boolean isEmpty() {
        return allOrders.isEmpty();
    }

    /**
     * Lấy tất cả các lệnh
     *
     * @return Danh sách tất cả các lệnh
     */
    public List<Order> getAllOrdersList() {
        return new ArrayList<>(allOrders.values());
    }

    /**
     * Remove order by string ID for compatibility
     *
     * @param orderId String order ID
     * @return true if removed
     */
    public boolean removeOrder(String orderId) {
        try {
            OrderId orderIdObj = OrderId.of(orderId);
            Order removedOrder = removeOrder(orderIdObj);
            return removedOrder != null;
        } catch (Exception e) {
            log.error("Failed to remove order {}: {}", orderId, e.getMessage());
            return false;
        }
    }

    /**
     * Get snapshot of order book
     *
     * @return DistributedOrderBookSnapshot
     */
    public DistributedOrderBookSnapshot getSnapshot() {
        DistributedOrderBookSnapshot snapshot = new DistributedOrderBookSnapshot(segmentSize);

        // Copy all orders to snapshot
        List<Order> allOrdersList = getAllOrdersList();
        for (Order order : allOrdersList) {
            snapshot.addOrder(order);
        }

        return snapshot;
    }

    /**
     * Get buy price levels - copy từ Future-Core
     */
    public List<Money> getBuyPriceLevels() {
        TreeMap<Money, List<Order>> buyOrders = cachedBuyOrders.get();
        if (buyOrders == null || isCacheExpired()) {
            buyOrders = buildBuyOrdersMap();
            cachedBuyOrders.set(buyOrders);
        }
        return new ArrayList<>(buyOrders.descendingKeySet()); // Highest price first
    }

    /**
     * Get sell price levels - copy từ Future-Core
     */
    public List<Money> getSellPriceLevels() {
        TreeMap<Money, List<Order>> sellOrders = cachedSellOrders.get();
        if (sellOrders == null || isCacheExpired()) {
            sellOrders = buildSellOrdersMap();
            cachedSellOrders.set(sellOrders);
        }
        return new ArrayList<>(sellOrders.keySet()); // Lowest price first
    }

    /**
     * Get buy orders at specific price - copy từ Future-Core
     */
    public List<Order> getBuyOrdersAtPrice(Money price) {
        TreeMap<Money, List<Order>> buyOrders = cachedBuyOrders.get();
        if (buyOrders == null || isCacheExpired()) {
            buyOrders = buildBuyOrdersMap();
            cachedBuyOrders.set(buyOrders);
        }
        return buyOrders.getOrDefault(price, Collections.emptyList());
    }

    /**
     * Get sell orders at specific price - copy từ Future-Core
     */
    public List<Order> getSellOrdersAtPrice(Money price) {
        TreeMap<Money, List<Order>> sellOrders = cachedSellOrders.get();
        if (sellOrders == null || isCacheExpired()) {
            sellOrders = buildSellOrdersMap();
            cachedSellOrders.set(sellOrders);
        }
        return sellOrders.getOrDefault(price, Collections.emptyList());
    }

    /**
     * Build buy orders map
     */
    private TreeMap<Money, List<Order>> buildBuyOrdersMap() {
        TreeMap<Money, List<Order>> buyOrders = new TreeMap<>(Collections.reverseOrder());

        for (OrderBookSegment segment : segments.values()) {
            Iterator<Map.Entry<BigDecimal, ConcurrentSkipListMap<TimestampedOrderKey, Order>>> buyIterator =
                    segment.getBuyOrdersIterator();

            while (buyIterator.hasNext()) {
                Map.Entry<BigDecimal, ConcurrentSkipListMap<TimestampedOrderKey, Order>> priceEntry = buyIterator.next();
                Money price = Money.of(priceEntry.getKey());
                List<Order> ordersAtPrice = new ArrayList<>(priceEntry.getValue().values());

                if (!ordersAtPrice.isEmpty()) {
                    buyOrders.put(price, ordersAtPrice);
                }
            }
        }

        log.info("Built buy orders map with {} price levels from segments", buyOrders.size());
        return buyOrders;
    }

    /**
     * Build sell orders map
     */
    private TreeMap<Money, List<Order>> buildSellOrdersMap() {
        TreeMap<Money, List<Order>> sellOrders = new TreeMap<>();

        for (OrderBookSegment segment : segments.values()) {
            Iterator<Map.Entry<BigDecimal, ConcurrentSkipListMap<TimestampedOrderKey, Order>>> sellIterator =
                    segment.getSellOrdersIterator();

            while (sellIterator.hasNext()) {
                Map.Entry<BigDecimal, ConcurrentSkipListMap<TimestampedOrderKey, Order>> priceEntry = sellIterator.next();
                Money price = Money.of(priceEntry.getKey());
                List<Order> ordersAtPrice = new ArrayList<>(priceEntry.getValue().values());

                if (!ordersAtPrice.isEmpty()) {
                    sellOrders.put(price, ordersAtPrice);
                }
            }
        }

        log.info("Built sell orders map with {} price levels from segments", sellOrders.size());
        return sellOrders;
    }

    /**
     * Check if cache is expired
     */
    private boolean isCacheExpired() {
        return System.currentTimeMillis() - lastModificationTime > CACHE_VALIDITY_MS;
    }

    /**
     * Add buy order - copy từ Future-Core
     */
    public void addBuyOrder(Order order) {
        if (order.getDirection() != OrderDirection.BUY) {
            throw new IllegalArgumentException("Order must be a buy order");
        }
        addOrder(order);
    }

    /**
     * Add sell order - copy từ Future-Core
     */
    public void addSellOrder(Order order) {
        if (order.getDirection() != OrderDirection.SELL) {
            throw new IllegalArgumentException("Order must be a sell order");
        }
        addOrder(order);
    }

    /**
     * Get order book depth - copy từ Future-Core
     */
    public int getDepth() {
        return Math.max(getBuyPriceLevels().size(), getSellPriceLevels().size());
    }

    /**
     * Get total volume for direction - copy từ Future-Core
     */
    public BigDecimal getTotalVolume(OrderDirection direction) {
        return allOrders.values().stream()
                .filter(order -> order.getDirection() == direction && order.getType() != OrderType.MARKET)
                .map(order -> order.getSize().getAmount())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * Clear all orders - copy từ Future-Core
     */
    public void clear() {
        allOrders.clear();
        segments.clear();
        orderToPriceRange.clear();

        log.info("Cleared all orders from distributed order book");
    }

    /**
     * Get order book summary - copy từ Future-Core
     */
    public String getSummary() {
        return String.format("DistributedOrderBook[orders=%d, segments=%d, buyLevels=%d, sellLevels=%d]",
                size(), segments.size(), getBuyPriceLevels().size(), getSellPriceLevels().size());
    }

    // ===== OPTIMIZATION METHODS WITH STAMPEDLOCK =====

    /**
     * OPTIMIZATION: Get best buy price with optimistic read
     *
     * @return Best buy price or null if no buy orders
     */
    public Money getBestBuyPriceOptimized() {
        long stamp = optimizedLock.tryOptimisticRead();
        Map.Entry<Money, List<Order>> bestEntry = getBestBuyEntry();

        if (!optimizedLock.validate(stamp)) {
            // Fallback to read lock if optimistic read failed
            stamp = optimizedLock.readLock();
            try {
                bestEntry = getBestBuyEntry();
            } finally {
                optimizedLock.unlockRead(stamp);
            }
        }

        return bestEntry != null ? bestEntry.getKey() : null;
    }

    /**
     * OPTIMIZATION: Get best sell price with optimistic read
     *
     * @return Best sell price or null if no sell orders
     */
    public Money getBestSellPriceOptimized() {
        long stamp = optimizedLock.tryOptimisticRead();
        Map.Entry<Money, List<Order>> bestEntry = getBestSellEntry();

        if (!optimizedLock.validate(stamp)) {
            // Fallback to read lock if optimistic read failed
            stamp = optimizedLock.readLock();
            try {
                bestEntry = getBestSellEntry();
            } finally {
                optimizedLock.unlockRead(stamp);
            }
        }

        return bestEntry != null ? bestEntry.getKey() : null;
    }

    /**
     * OPTIMIZATION: Get order count with optimistic read
     *
     * @return Number of orders
     */
    public int getSizeOptimized() {
        long stamp = optimizedLock.tryOptimisticRead();
        int size = allOrders.size();

        if (!optimizedLock.validate(stamp)) {
            // Fallback to read lock if optimistic read failed
            stamp = optimizedLock.readLock();
            try {
                size = allOrders.size();
            } finally {
                optimizedLock.unlockRead(stamp);
            }
        }

        return size;
    }

    /**
     * OPTIMIZATION: Check if order book is empty with optimistic read
     *
     * @return true if empty
     */
    public boolean isEmptyOptimized() {
        long stamp = optimizedLock.tryOptimisticRead();
        boolean empty = allOrders.isEmpty();

        if (!optimizedLock.validate(stamp)) {
            // Fallback to read lock if optimistic read failed
            stamp = optimizedLock.readLock();
            try {
                empty = allOrders.isEmpty();
            } finally {
                optimizedLock.unlockRead(stamp);
            }
        }

        return empty;
    }

    // ===== ITERATOR METHODS FOR PERFORMANCE OPTIMIZATION =====

    /**
     * Lấy iterator cho BUY orders với early exit capability
     * Tối ưu hóa theo pattern của Exchange module
     *
     * @return Iterator cho BUY orders theo thứ tự giá cao nhất trước
     */
    public Iterator<Map.Entry<Money, List<Order>>> getBuyOrdersIterator() {
        // Build buy orders map with optimistic read
        long stamp = optimizedLock.tryOptimisticRead();
        TreeMap<Money, List<Order>> buyOrders = buildBuyOrdersMap();

        if (!optimizedLock.validate(stamp)) {
            // Fallback to read lock if optimistic read failed
            stamp = optimizedLock.readLock();
            try {
                buyOrders = buildBuyOrdersMap();
            } finally {
                optimizedLock.unlockRead(stamp);
            }
        }

        return buyOrders.entrySet().iterator();
    }

    /**
     * Lấy iterator cho SELL orders với early exit capability
     * Tối ưu hóa theo pattern của Exchange module
     *
     * @return Iterator cho SELL orders theo thứ tự giá thấp nhất trước
     */
    public Iterator<Map.Entry<Money, List<Order>>> getSellOrdersIterator() {
        // Build sell orders map with optimistic read
        long stamp = optimizedLock.tryOptimisticRead();
        TreeMap<Money, List<Order>> sellOrders = buildSellOrdersMap();

        if (!optimizedLock.validate(stamp)) {
            // Fallback to read lock if optimistic read failed
            stamp = optimizedLock.readLock();
            try {
                sellOrders = buildSellOrdersMap();
            } finally {
                optimizedLock.unlockRead(stamp);
            }
        }

        return sellOrders.entrySet().iterator();
    }

    /**
     * Get buy orders as map for direct access
     *
     * @return Map of buy orders by price
     */
    public Map<Money, List<Order>> getBuyOrdersMap() {
        long stamp = optimizedLock.tryOptimisticRead();
        TreeMap<Money, List<Order>> buyOrders = buildBuyOrdersMap();

        if (!optimizedLock.validate(stamp)) {
            // Fallback to read lock if optimistic read failed
            stamp = optimizedLock.readLock();
            try {
                buyOrders = buildBuyOrdersMap();
            } finally {
                optimizedLock.unlockRead(stamp);
            }
        }

        return buyOrders;
    }

    /**
     * Get sell orders as map for direct access
     *
     * @return Map of sell orders by price
     */
    public Map<Money, List<Order>> getSellOrdersMap() {
        long stamp = optimizedLock.tryOptimisticRead();
        TreeMap<Money, List<Order>> sellOrders = buildSellOrdersMap();

        if (!optimizedLock.validate(stamp)) {
            // Fallback to read lock if optimistic read failed
            stamp = optimizedLock.readLock();
            try {
                sellOrders = buildSellOrdersMap();
            } finally {
                optimizedLock.unlockRead(stamp);
            }
        }

        return sellOrders;
    }

    /**
     * Get all buy orders as flat list
     *
     * @return List of all buy orders
     */
    public List<Order> getBuyOrders() {
        List<Order> buyOrders = new ArrayList<>();
        Map<Money, List<Order>> buyOrdersMap = getBuyOrdersMap();
        for (List<Order> orders : buyOrdersMap.values()) {
            buyOrders.addAll(orders);
        }
        return buyOrders;
    }

    /**
     * Get all sell orders as flat list
     *
     * @return List of all sell orders
     */
    public List<Order> getSellOrders() {
        List<Order> sellOrders = new ArrayList<>();
        Map<Money, List<Order>> sellOrdersMap = getSellOrdersMap();
        for (List<Order> orders : sellOrdersMap.values()) {
            sellOrders.addAll(orders);
        }
        return sellOrders;
    }

    /**
     * Remove order by ID - optimized version
     *
     * @param orderId Order ID to remove
     * @return true if order was found and removed
     */
    public boolean removeOrderById(String orderId) {
        OrderId orderIdObj = OrderId.of(orderId);
        Order removedOrder = removeOrder(orderIdObj);
        return removedOrder != null;
    }

    /**
     * Find order by ID - optimized version
     *
     * @param orderId Order ID to find
     * @return Order if found, null otherwise
     */
    public Order findOrderById(String orderId) {
        OrderId orderIdObj = OrderId.of(orderId);
        Optional<Order> orderOpt = getOrder(orderIdObj);
        return orderOpt.orElse(null);
    }

    /**
     * Internal method to add order without locking (assumes lock is already held)
     */
    private void addOrderInternal(Order order) {
        if (order == null || order.getOrderId() == null) {
            return;
        }

        String orderIdStr = order.getOrderId().getValue();

        // Add to all orders map
        allOrders.put(orderIdStr, order);

        if (Arrays.asList(OrderType.STOP_MARKET, OrderType.STOP_LOSS, OrderType.TAKE_PROFIT, OrderType.MARKET).contains(order.getType())) {
            log.info("Market order {} added to allOrders only (IOC behavior - internal)", orderIdStr);
            return;
        }

        // Determine price range and add to appropriate segment
        PriceRange priceRange = calculatePriceRange(order.getPrice());
        orderToPriceRange.put(orderIdStr, priceRange);

        OrderBookSegment segment = segments.computeIfAbsent(priceRange,
                k -> new OrderBookSegment(priceRange));
        segment.addOrder(order);

        log.info("Limit order {} added to segments (internal)", orderIdStr);
    }

    /**
     * Calculate price range for a given price
     *
     * @param price Price to calculate range for
     * @return PriceRange containing the price
     */
    private PriceRange calculatePriceRange(Money price) {
        return PriceRange.forPrice(price, segmentSize);
    }
}
