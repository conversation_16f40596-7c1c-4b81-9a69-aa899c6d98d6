package com.icetea.lotus.matching.infrastructure.persistence.mongodb.repository;

import com.icetea.lotus.matching.infrastructure.persistence.mongodb.document.OrderBookSnapshotDocument;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

/**
 * MongoDB Repository cho Order Book Snapshots trong Matching Engine
 * Copy từ future-core với modifications cho cả spot và futures
 * 
 * <AUTHOR> nguyen
 */
@Repository
public interface OrderBookSnapshotRepository extends MongoRepository<OrderBookSnapshotDocument, String> {

    /**
     * Find latest snapshot by symbol and trading type
     */
    @Query(value = "{'symbol': ?0, 'trading_type': ?1}", sort = "{'version': -1}")
    Optional<OrderBookSnapshotDocument> findLatestBySymbolAndTradingType(String symbol, String tradingType);

    /**
     * Find latest snapshot by symbol (any trading type)
     */
    @Query(value = "{'symbol': ?0}", sort = "{'version': -1}")
    Optional<OrderBookSnapshotDocument> findLatestBySymbol(String symbol);

    /**
     * Find snapshot by symbol, trading type and version
     */
    Optional<OrderBookSnapshotDocument> findBySymbolAndTradingTypeAndVersion(String symbol, String tradingType, Long version);

    /**
     * Find snapshots by symbol and trading type within time range
     */
    @Query("{'symbol': ?0, 'trading_type': ?1, 'timestamp': {'$gte': ?2, '$lte': ?3}}")
    List<OrderBookSnapshotDocument> findBySymbolAndTradingTypeAndTimestampBetween(
            String symbol, String tradingType, Instant startTime, Instant endTime);

    /**
     * Find snapshots by symbol within time range (any trading type)
     */
    @Query("{'symbol': ?0, 'timestamp': {'$gte': ?1, '$lte': ?2}}")
    List<OrderBookSnapshotDocument> findBySymbolAndTimestampBetween(
            String symbol, Instant startTime, Instant endTime);

    /**
     * Find all snapshots by trading type
     */
    @Query(value = "{'trading_type': ?0}", sort = "{'timestamp': -1}")
    List<OrderBookSnapshotDocument> findByTradingTypeOrderByTimestampDesc(String tradingType);

    /**
     * Count snapshots by symbol and trading type
     */
    long countBySymbolAndTradingType(String symbol, String tradingType);

    /**
     * Count snapshots by symbol (any trading type)
     */
    long countBySymbol(String symbol);

    /**
     * Delete old snapshots by symbol and trading type (keep only latest N)
     */
    @Query(value = "{'symbol': ?0, 'trading_type': ?1}", sort = "{'version': -1}")
    List<OrderBookSnapshotDocument> findBySymbolAndTradingTypeOrderByVersionDesc(String symbol, String tradingType);

    /**
     * Find snapshots by multiple symbols
     */
    @Query("{'symbol': {'$in': ?0}}")
    List<OrderBookSnapshotDocument> findBySymbolIn(List<String> symbols);

    /**
     * Find snapshots by multiple symbols and trading type
     */
    @Query("{'symbol': {'$in': ?0}, 'trading_type': ?1}")
    List<OrderBookSnapshotDocument> findBySymbolInAndTradingType(List<String> symbols, String tradingType);

    /**
     * Find expired snapshots for cleanup
     */
    @Query("{'expires_at': {'$lt': ?0}}")
    List<OrderBookSnapshotDocument> findExpiredSnapshots(Instant now);

    /**
     * Delete snapshots older than timestamp
     */
    void deleteByTimestampBefore(Instant timestamp);

    /**
     * Delete snapshots by symbol and trading type older than timestamp
     */
    void deleteBySymbolAndTradingTypeAndTimestampBefore(String symbol, String tradingType, Instant timestamp);

    /**
     * Find snapshots with large size (for optimization)
     */
    @Query("{'metadata.snapshot_size_bytes': {'$gt': ?0}}")
    List<OrderBookSnapshotDocument> findLargeSnapshots(long sizeThreshold);

    /**
     * Find snapshots with low compression ratio
     */
    @Query("{'metadata.compression_ratio': {'$lt': ?0}}")
    List<OrderBookSnapshotDocument> findPoorlyCompressedSnapshots(double compressionThreshold);

    /**
     * Find snapshots older than specified time for cleanup (with pagination)
     */
    @Query(value = "{'timestamp': {'$lt': ?0}}", sort = "{'timestamp': 1}")
    List<OrderBookSnapshotDocument> findByTimestampBeforeOrderByTimestampAsc(Instant cutoffTime, Pageable pageable);

    /**
     * Find distinct symbols
     */
    @Query(value = "{}", fields = "{'symbol': 1}")
    List<String> findDistinctSymbols();

    /**
     * Find distinct trading types
     */
    @Query(value = "{}", fields = "{'trading_type': 1}")
    List<String> findDistinctTradingTypes();

    /**
     * Statistics interface for aggregation results
     */
    interface SnapshotStatistics {
        String getId(); // symbol
        String getTradingType();
        Long getTotalSnapshots();
        Long getLatestVersion();
        Long getOldestVersion();
        Double getAvgOrdersCount();
        Long getTotalSizeBytes();
    }

    /**
     * Symbol summary interface for aggregation results
     */
    interface SymbolSnapshotSummary {
        String getId(); // symbol
        String getTradingType();
        Long getSnapshotCount();
        Long getLatestVersion();
        Instant getLatestTimestamp();
    }
}
