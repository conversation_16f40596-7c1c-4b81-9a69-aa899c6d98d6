package com.icetea.lotus.matching.infrastructure.engine;

import com.icetea.lotus.matching.domain.valueobject.Money;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * Đại diện cho một phạm vi giá trong sổ lệnh phân tán
 * Implements Comparable để có thể sử dụng trong ConcurrentSkipListMap
 * 
 * <AUTHOR> nguyen
 */
@Getter
@RequiredArgsConstructor
public class PriceRange implements Comparable<PriceRange> {

    // Giới hạn dưới của phạm vi giá (bao gồm)
    private final Money lowerBound;

    // Giới hạn trên của phạm vi giá (không bao gồm)
    private final Money upperBound;

    /**
     * Kiểm tra xem một giá có nằm trong phạm vi này không
     * @param price Giá cần kiểm tra
     * @return true nếu giá nằm trong phạm vi, false nếu không
     */
    public boolean contains(Money price) {
        return price.getValue().compareTo(lowerBound.getValue()) >= 0 &&
               price.getValue().compareTo(upperBound.getValue()) < 0;
    }

    /**
     * Tạo PriceRange cho một giá cụ thể dựa trên kích thước phân đoạn
     * @param price Giá cần tạo phạm vi
     * @param segmentSize Kích thước phân đoạn
     * @return PriceRange chứa giá
     */
    public static PriceRange forPrice(Money price, BigDecimal segmentSize) {
        // Xử lý trường hợp giá null (lệnh thị trường)
        if (price == null) {
            // Sử dụng phạm vi mặc định cho lệnh thị trường
            return new PriceRange(Money.of(BigDecimal.ZERO), Money.of(segmentSize));
        }

        BigDecimal value = price.getValue();
        BigDecimal lowerBound = value.divideToIntegralValue(segmentSize).multiply(segmentSize);
        BigDecimal upperBound = lowerBound.add(segmentSize);
        return new PriceRange(Money.of(lowerBound), Money.of(upperBound));
    }

    /**
     * Compare PriceRange objects for ordering in ConcurrentSkipListMap
     * Compares based on lower bound first, then upper bound
     */
    @Override
    public int compareTo(PriceRange other) {
        if (other == null) {
            return 1;
        }

        // Compare lower bounds first
        int lowerComparison = this.lowerBound.getValue().compareTo(other.lowerBound.getValue());
        if (lowerComparison != 0) {
            return lowerComparison;
        }

        // If lower bounds are equal, compare upper bounds
        return this.upperBound.getValue().compareTo(other.upperBound.getValue());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PriceRange that = (PriceRange) o;
        return Objects.equals(lowerBound, that.lowerBound) &&
               Objects.equals(upperBound, that.upperBound);
    }

    @Override
    public int hashCode() {
        return Objects.hash(lowerBound, upperBound);
    }

    @Override
    public String toString() {
        return "[" + lowerBound + " - " + upperBound + ")";
    }
}
