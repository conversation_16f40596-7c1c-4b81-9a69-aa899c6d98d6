package com.icetea.lotus.matching.infrastructure.futurecore;

import com.icetea.lotus.matching.domain.entity.Order;
import com.icetea.lotus.matching.domain.entity.Trade;
import com.icetea.lotus.matching.domain.enums.OrderDirection;
import com.icetea.lotus.matching.domain.enums.OrderStatus;
import com.icetea.lotus.matching.domain.enums.OrderType;
import com.icetea.lotus.matching.domain.valueobject.Money;
import com.icetea.lotus.matching.domain.valueobject.OrderId;
import com.icetea.lotus.matching.domain.valueobject.Symbol;
import com.icetea.lotus.matching.infrastructure.constants.CommandTypeConstance;
import com.icetea.lotus.matching.infrastructure.constants.CommonConstance;
import com.icetea.lotus.matching.infrastructure.engine.DistributedOrderBookSnapshot;
import com.icetea.lotus.matching.infrastructure.event.TradeExecutedEvent;
import com.icetea.lotus.matching.infrastructure.messaging.dto.FutureTradePlateMessage;
import com.icetea.lotus.matching.infrastructure.messaging.producer.FutureCoreKafkaProducer;
import com.icetea.lotus.matching.infrastructure.service.LastPriceService;
import com.icetea.lotus.matching.infrastructure.service.MatchingEngineStopOrderManager;
import com.icetea.lotus.matching.infrastructure.stp.SelfTradePreventionMode;
import com.icetea.lotus.matching.infrastructure.stp.SelfTradePreventionResult;
import com.icetea.lotus.matching.infrastructure.stp.SelfTradePreventionService;
import com.icetea.lotus.matching.infrastructure.util.SnowflakeIdGenerator;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.LockSupport;

import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.ALL_ORDERS;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.BUY_ORDERS;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.PRICE;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.SELL_ORDERS;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.STOP_ORDERS;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.SYMBOL;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.TIMESTAMP;

/**
 * Future-Core Matching Engine - Copy từ DistributedLockFreeMatchingEngine
 * Implement exact lock-free algorithms và performance optimizations
 * CAS-based operations với 12K+ TPS performance
 *
 * <AUTHOR> nguyen
 */
@Component
public class FutureCoreMatchingEngine {

    private static final Logger logger = LoggerFactory.getLogger(FutureCoreMatchingEngine.class);
    public static final String MARKET = "MARKET";
    public static final String LIMIT = "LIMIT";

    // Lock-free order book reference - copy từ Future-Core
    private final AtomicReference<DistributedOrderBookSnapshot> orderBookRef =
            new AtomicReference<>(new DistributedOrderBookSnapshot());

    // Performance metrics - copy từ Future-Core
    private final AtomicLong totalOrdersProcessed = new AtomicLong(0);
    private final AtomicLong totalTradesGenerated = new AtomicLong(0);
    private final AtomicLong casFailureCount = new AtomicLong(0);
    private final AtomicLong casSuccessCount = new AtomicLong(0);

    private final LastPriceService lastPriceService;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final MatchingEngineStopOrderManager matchingEngineStopOrderManager;
    // Algorithm configuration
    @Getter
    private volatile MatchingAlgorithm matchingAlgorithm = MatchingAlgorithm.FIFO;
    @Getter
    private Money lastPrice = Money.ZERO;

    // Getters and setters
    // Symbol for this engine instance
    @Getter
    @Setter
    private String symbol;

    // ==================== ISOLATED RESOURCES FOR FUTURES ENGINE ====================

    /**
     * ISOLATION: Dedicated thread-local pools for Futures engine only
     */
    private static final ThreadLocal<List<Trade>> FUTURES_TRADE_LIST_POOL = ThreadLocal.withInitial(() ->
            new ArrayList<>(150)); // Larger capacity for futures

    /**
     * CONCURRENCY OPTIMIZATION: Advanced CAS configuration
     */
    private static final int OPTIMIZED_MAX_CAS_RETRIES = 25; // Optimized retry count
    private static final long OPTIMIZED_INITIAL_BACKOFF_NANOS = 1L; // Start with 1ns

    // ==================== LOCK-FREE DATA STRUCTURES ====================

    /**
     * LOCK-FREE: Segmented order book for reduced contention
     */
    private static final int ORDER_BOOK_SEGMENTS = 16; // Power of 2 for fast modulo
    private static final int SEGMENT_MASK = ORDER_BOOK_SEGMENTS - 1;

    /**
     * LOCK-FREE: Per-segment atomic references for parallel processing
     */
    private final AtomicReference<DistributedOrderBookSnapshot>[] segmentedOrderBooks;
    private final SnowflakeIdGenerator snowflakeIdGenerator = new SnowflakeIdGenerator();

    private final FutureCoreKafkaProducer futureCoreKafkaProducer;
    private final SelfTradePreventionService stpService;
    private final MongoTemplate mongoTemplate;


    // ==================== CPU CACHE OPTIMIZATION ====================

    /**
     * LOCK-FREE: Initialize segmented order books
     */
    @SuppressWarnings("unchecked")
    public FutureCoreMatchingEngine(FutureCoreKafkaProducer futureCoreKafkaProducer, SelfTradePreventionService stpService, MongoTemplate mongoTemplate, LastPriceService lastPriceService, ApplicationEventPublisher applicationEventPublisher, MatchingEngineStopOrderManager matchingEngineStopOrderManager) {
        this.futureCoreKafkaProducer = futureCoreKafkaProducer;
        this.stpService = stpService;
        this.mongoTemplate = mongoTemplate;
        this.lastPriceService = lastPriceService;
        // Initialize segmented order books for lock-free operations
        segmentedOrderBooks = new AtomicReference[ORDER_BOOK_SEGMENTS];
        for (int i = 0; i < ORDER_BOOK_SEGMENTS; i++) {
            segmentedOrderBooks[i] = new AtomicReference<>(new DistributedOrderBookSnapshot());
        }
        this.applicationEventPublisher = applicationEventPublisher;
        this.matchingEngineStopOrderManager = matchingEngineStopOrderManager;
    }

    /**
     * Process order với ultra-high performance optimizations
     */
    public List<Trade> processOrder(Order order, String commandType) {
        if (order == null) {
            return Collections.emptyList();
        }

        totalOrdersProcessed.incrementAndGet();

        // Use hyper-optimized processing for 10,000+ orders
        return processMatchingOrder(order, commandType);
    }

    /**
     * Hyper-optimized processing for 10,000+ orders per second
     */
    public List<Trade> processMatchingOrder(Order order, String commandType) {
        if (order == null) {
            return Collections.emptyList();
        }

        totalOrdersProcessed.incrementAndGet();

        // Use adaptive segmented processing with CPU affinity
        int segmentIndex = selectOptimalSegment(order);
        AtomicReference<DistributedOrderBookSnapshot> segmentRef = segmentedOrderBooks[segmentIndex];

        // Use pre-allocated trade list pool with zero-copy
        List<Trade> trades = FUTURES_TRADE_LIST_POOL.get();
        trades.clear();

        // Process with hyper-optimized algorithms
        try {
            return processOrderInSegment(order, segmentRef, trades, commandType);
        } finally {
            FUTURES_TRADE_LIST_POOL.remove();
        }
    }

    /**
     * Consistent segment selection for same symbol orders
     */
    private int selectOptimalSegment(Order order) {
        // Ensure ALL orders of the same symbol go to the SAME segment to allow cross-member matching
        // Using only symbol-based hashing guarantees that triggered orders from different members can match
        long symbolHash = order.getSymbol().getValue().hashCode();
        return (int) (symbolHash & SEGMENT_MASK);
    }

    /**
     * Compute segment index based solely on symbol value for restore paths
     */
    private int computeSegmentIndexBySymbol(String sym) {
        String s = (sym != null) ? sym : this.symbol;
        long symbolHash = (s != null) ? s.hashCode() : 0L;
        return (int) (symbolHash & SEGMENT_MASK);
    }


    /**
     * Hyper-optimized segment processing for 10,000+ orders per second
     */
    @SuppressWarnings({"java:S3776", "java:S2245"})
    private List<Trade> processOrderInSegment(Order order, AtomicReference<DistributedOrderBookSnapshot> segmentRef,
                                              List<Trade> trades, String commandType) {
        // Use adaptive retry strategy based on load
        int maxRetries = OPTIMIZED_MAX_CAS_RETRIES * 2; // Double retries for extreme load
        int retryCount = 0;
        long backoffNanos = OPTIMIZED_INITIAL_BACKOFF_NANOS / 2; // Faster initial backoff

        // Pre-check order type for optimization
        boolean isMarketOrder = Arrays.asList(OrderType.STOP_MARKET, OrderType.STOP_LOSS, OrderType.TAKE_PROFIT, OrderType.MARKET).contains(order.getType());

        // Lock-free processing with hyper-optimized CAS retry loop
        while (retryCount < maxRetries) {
            // Get snapshot with minimal overhead
            DistributedOrderBookSnapshot currentSnapshot = segmentRef.get();

            // Use zero-copy lightweight snapshot
            DistributedOrderBookSnapshot newSnapshot = currentSnapshot.createLightweightCopy();

            // Clear trades list for retry (reuse allocation)
            trades.clear();
            List<Order> orderCanceled = List.of();
            // Use hyper-optimized matching algorithm
            matchOrderFIFO(order, newSnapshot, trades, commandType, orderCanceled);

            // Update last price with minimal overhead
            int tradesSize = trades.size();
            if (!trades.isEmpty()) {
                lastPrice = Money.of(trades.get(tradesSize - 1).getPrice());
            }

            updateOrderStatusAfterMatching(order, newSnapshot, isMarketOrder, orderCanceled);

            // CAS update with hyper-optimized retry mechanism
            if (segmentRef.compareAndSet(currentSnapshot, newSnapshot)) {
                casSuccessCount.incrementAndGet();
                totalTradesGenerated.addAndGet(tradesSize);

                // Side-effects AFTER successful CAS commit
                if (tradesSize > 0) {
                    try {
                        // Publish per-trade events (individual)
                        for (int i = 0; i < tradesSize; i++) {
                            Trade t = trades.get(i);
                            this.publishTradeExecuteEvent(symbol, t);
                        }

                        // Update and persist last price using the last trade
                        Money committedLastPrice = Money.of(trades.get(tradesSize - 1).getPrice());
                        this.updateLastPrice(committedLastPrice);
                        this.saveLastPriceToMongoDB(order.getSymbol().getValue(), committedLastPrice);
                    } catch (Exception se) {
                        logger.warn("Post-commit side-effects failed for order {}: {}", order.getOrderId().getValue(), se.getMessage(), se);
                    }
                }

                return trades;
            } else {
                // CAS failed - use adaptive backoff strategy
                casFailureCount.incrementAndGet();
                retryCount++;

                // Hyper-optimized backoff strategy for minimal latency
                if (retryCount <= 12) {
                    // Yield CPU for next 7 attempts
                    Thread.yield();
                } else if (retryCount <= 20) {
                    // PRECISE: Use nano-precision backoff
                    LockSupport.parkNanos(backoffNanos);
                } else {
                    // HIGH CONTENTION: Exponential backoff with reduced jitter
                    long jitter = ThreadLocalRandom.current().nextLong(0, 0); // Reduced jitter
                    LockSupport.parkNanos(backoffNanos + jitter);
                }
            }
        }

        // Log only when max retries exceeded
        logger.warn("Max CAS retries ({}) exceeded for order {}, returning {} trades",
                maxRetries, order.getOrderId().getValue(), trades.size());
        return trades;
    }

    /**
     * FIFO matching algorithm - Hyper-optimized for 10,000+ orders per second
     */
    private void matchOrderFIFO(Order order, DistributedOrderBookSnapshot snapshot, List<Trade> trades, String commandType, List<Order> orderCanceled) {
        // Pre-cache all order properties to minimize method calls
        OrderType orderType = order.getType();
        OrderDirection direction = order.getDirection();

        // Use branch prediction friendly code structure
        if (Arrays.asList(OrderType.STOP_MARKET, OrderType.STOP_LOSS, OrderType.TAKE_PROFIT, OrderType.MARKET).contains(orderType)) {
            // Market orders - immediate execution path
            if (direction == OrderDirection.BUY) {
                matchMarketBuyOrder(order, snapshot, trades, commandType, orderCanceled);
            } else {
                matchMarketSellOrder(order, snapshot, trades, commandType, orderCanceled);
            }
        } else {
            // Limit orders - standard matching path
            if (direction == OrderDirection.BUY) {
                matchBuyOrder(order, snapshot, trades);
            } else {
                matchSellOrder(order, snapshot, trades);
            }
        }
    }

    /**
     * Add order to snapshot (for both processing and restoration)
     */
    private void addOrderToSnapshot(Order order, DistributedOrderBookSnapshot snapshot) {
        try {
            if (order == null || snapshot == null) {
                return;
            }

            // Add order to the appropriate side of the order book
            if (order.getDirection() == OrderDirection.BUY) {
                snapshot.addBuyOrder(order);
            } else {
                snapshot.addSellOrder(order);
            }

            logger.info("Added order {} to snapshot", order.getOrderId().getValue());

        } catch (Exception e) {
            logger.error("Error adding order to snapshot: {}", order.getOrderId().getValue(), e);
        }
    }

    /**
     * Update order status after matching - following spot logic pattern
     */
    private void updateOrderStatusAfterMatching(Order order, DistributedOrderBookSnapshot snapshot, boolean isMarketOrder, List<Order> orderCanceled) {
        try {
            if (order.getRemainingQuantity().isZero()) {
                // Order is fully filled
                order.setStatus(OrderStatus.FILLED);
                snapshot.removeOrder(order.getOrderId());
                logger.info("Order {} fully filled and removed from snapshot", order.getOrderId().getValue());
            } else if (order.getFilledSize().getValue().compareTo(BigDecimal.ZERO) > 0) {
                // Order is partially filled
                order.setStatus(OrderStatus.PARTIALLY_FILLED);

                if (isMarketOrder) {
                    // Market order IOC behavior: remove from snapshot even if partially filled
                    snapshot.removeOrder(order.getOrderId());
                    logger.info("Market order {} partially filled (IOC) - removed from snapshot", order.getOrderId().getValue());
                } else {
                    // Check if this is a taker order (incoming order) or maker order (already in snapshot)
                    Order existingOrder = snapshot.findOrderById(order.getOrderId().getValue());

                    if (existingOrder != null) {
                        // This is a maker order that was already in snapshot and got partially filled
                        // The order is already updated in memory during matching, no need to add again
                        logger.info("Limit maker order {} partially filled - already updated in snapshot", order.getOrderId().getValue());
                    } else {
                        // This is a taker order that got partially filled and needs to be added to snapshot
                        addOrderToSnapshot(order, snapshot);
                        logger.info("Limit taker order {} partially filled - added to snapshot for future matching", order.getOrderId().getValue());
                    }
                }
            } else {
                // Order is not filled at all
                if (isMarketOrder && !orderCanceled.contains(order)) {
                    futureCoreKafkaProducer.sendCancelOrderCommand(order);
                    logger.info("Market order {} not filled (IOC) - cancelled", order.getOrderId().getValue());
                } else {
                    // Limit order: add to snapshot for future matching
                    order.setStatus(OrderStatus.NEW);
                    addOrderToSnapshot(order, snapshot);
                    logger.info("Limit order {} not matched - added to snapshot", order.getOrderId().getValue());
                }
            }
        } catch (Exception e) {
            logger.error("Error updating order status after matching: {}", order.getOrderId().getValue(), e);
        }
    }

    /**
     * Get performance metrics
     */
    public PerformanceMetrics getPerformanceMetrics() {
        return PerformanceMetrics.builder()
                .totalOrdersProcessed(totalOrdersProcessed.get())
                .totalTradesGenerated(totalTradesGenerated.get())
                .casSuccessCount(casSuccessCount.get())
                .casFailureCount(casFailureCount.get())
                .casSuccessRate(calculateCasSuccessRate())
                .build();
    }

    /**
     * Calculate CAS success rate
     */
    private double calculateCasSuccessRate() {
        long total = casSuccessCount.get() + casFailureCount.get();
        if (total == 0) {
            return 1.0;
        }
        return (double) casSuccessCount.get() / total;
    }

    /**
     * Get order book snapshot aggregated from all segments
     */
    @SuppressWarnings("java:S3776")
    public DistributedOrderBookSnapshot getOrderBookSnapshot() {
        DistributedOrderBookSnapshot aggregatedSnapshot = new DistributedOrderBookSnapshot();

        // Collect orders from all segments
        for (int i = 0; i < ORDER_BOOK_SEGMENTS; i++) {
            DistributedOrderBookSnapshot segmentSnapshot = segmentedOrderBooks[i].get();
            if (segmentSnapshot != null) {
                List<Order> segmentOrders = segmentSnapshot.getAllOrders();
                if (segmentOrders != null && !segmentOrders.isEmpty()) {
                    for (Order order : segmentOrders) {
                        aggregatedSnapshot.addOrder(order);
                    }
                }

                // Also copy stop orders if any
                List<Order> stopOrders = segmentSnapshot.getStopOrders();
                if (stopOrders != null && !stopOrders.isEmpty()) {
                    for (Order stopOrder : stopOrders) {
                        aggregatedSnapshot.addStopOrder(stopOrder);
                    }
                }
            }
        }

        logger.info("Aggregated snapshot from {} segments with {} total orders",
                ORDER_BOOK_SEGMENTS, aggregatedSnapshot.size());

        return aggregatedSnapshot;
    }

    /**
     * Reset performance metrics
     */
    public void resetPerformanceMetrics() {
        totalOrdersProcessed.set(0);
        totalTradesGenerated.set(0);
        casSuccessCount.set(0);
        casFailureCount.set(0);
    }

    public void setMatchingAlgorithm(MatchingAlgorithm algorithm) {
        this.matchingAlgorithm = algorithm;
        logger.info("Matching algorithm changed to {} for symbol {}", algorithm, symbol);
    }

    /**
     * Update last price - for mark price updates
     */
    public void updateLastPrice(Money newPrice) {
        if (newPrice != null && newPrice.getAmount().compareTo(Money.ZERO.getAmount()) > 0) {
            this.lastPrice = newPrice;
            if (this.lastPriceService != null) {
                this.lastPriceService.updateLastPrice(symbol, newPrice.getValue(), CommonConstance.FUTURE);
            }
            logger.info("Updated last price for symbol {} to {}", symbol, newPrice);
        }
    }

    /**
     * Cancel order by order ID - Lock-free implementation with segmented search
     */
    public boolean cancelOrder(String orderId, OrderType orderType, OrderStatus orderStatus) {
        if (orderId == null || orderId.trim().isEmpty()) {
            logger.warn("Cannot cancel order with null or empty orderId");
            return false;
        }

        if (orderType != null && OrderStatus.NEW.equals(orderStatus)
                && Arrays.asList(OrderType.TAKE_PROFIT, OrderType.STOP_MARKET, OrderType.STOP_LOSS, OrderType.STOP_LIMIT).contains(orderType)) {
            return matchingEngineStopOrderManager.cancelStopOrder(orderId, CommonConstance.FUTURE);
        }

        logger.info("Attempting to cancel order: {} for symbol: {}", orderId, symbol);

        // Search across all segments for the order
        for (int i = 0; i < ORDER_BOOK_SEGMENTS; i++) {
            AtomicReference<DistributedOrderBookSnapshot> segmentRef = segmentedOrderBooks[i];

            // Lock-free cancellation với CAS retry loop for this segment
            while (true) {
                // Lấy snapshot hiện tại
                DistributedOrderBookSnapshot currentSnapshot = segmentRef.get();

                // Tạo bản sao để thực hiện thay đổi
                DistributedOrderBookSnapshot newSnapshot = currentSnapshot.createOptimizedCopy();

                // Tìm và remove order từ snapshot
                boolean orderFound = removeOrderFromSnapshot(orderId, newSnapshot);

                if (!orderFound) {
                    // Order not in this segment, try next segment
                    break;
                }

                // Thử cập nhật snapshot với CAS
                if (segmentRef.compareAndSet(currentSnapshot, newSnapshot)) {
                    casSuccessCount.incrementAndGet();
                    logger.info("Successfully cancelled order: {} for symbol: {} in segment: {}", orderId, symbol, i);
                    return true;
                } else {
                    casFailureCount.incrementAndGet();
                    logger.info("CAS failed for order cancellation, retrying: {} in segment: {}", orderId, i);
                    // Retry với snapshot mới
                }
            }
        }

        logger.info("Order not found in any segment: {}", orderId);
        return false;
    }

    /**
     * Remove order from snapshot by order ID - OPTIMIZED with iterators
     */
    private boolean removeOrderFromSnapshot(String orderId, DistributedOrderBookSnapshot snapshot) {
        try {
            // OPTIMIZATION: Use optimized removeOrderById method if available
            if (snapshot.removeOrderById(orderId)) {
                logger.info("Successfully removed order {} using optimized method", orderId);
                return true;
            }

            // Fallback: Remove from buy orders using iterator
            boolean foundInBuy = removeOrderFromBuyOrders(orderId, snapshot);

            // Remove from sell orders using iterator
            boolean foundInSell = removeOrderFromSellOrders(orderId, snapshot);

            return foundInBuy || foundInSell;

        } catch (Exception e) {
            logger.error("Error removing order from snapshot: {}", orderId, e);
            return false;
        }
    }

    /**
     * Remove order from buy orders using iterator for better performance
     */
    private boolean removeOrderFromBuyOrders(String orderId, DistributedOrderBookSnapshot snapshot) {
        Iterator<Map.Entry<Money, List<Order>>> buyIterator = snapshot.getBuyOrdersIterator();

        while (buyIterator.hasNext()) {
            Map.Entry<Money, List<Order>> entry = buyIterator.next();
            List<Order> ordersAtPrice = entry.getValue();

            // Tìm và remove order
            boolean removed = ordersAtPrice.removeIf(order ->
                    order.getOrderId().getValue().equals(orderId));

            if (removed) {
                // Nếu không còn order nào ở price level này, remove price level
                if (ordersAtPrice.isEmpty()) {
                    buyIterator.remove();
                }
                logger.info("Removed order {} from buy orders at price {}", orderId, entry.getKey());
                return true;
            }
        }
        return false;
    }

    /**
     * Remove order from sell orders using iterator for better performance
     */
    private boolean removeOrderFromSellOrders(String orderId, DistributedOrderBookSnapshot snapshot) {
        Iterator<Map.Entry<Money, List<Order>>> sellIterator = snapshot.getSellOrdersIterator();

        while (sellIterator.hasNext()) {
            Map.Entry<Money, List<Order>> entry = sellIterator.next();
            List<Order> ordersAtPrice = entry.getValue();

            // Tìm và remove order
            boolean removed = ordersAtPrice.removeIf(order ->
                    order.getOrderId().getValue().equals(orderId));

            if (removed) {
                // Nếu không còn order nào ở price level này, remove price level
                if (ordersAtPrice.isEmpty()) {
                    sellIterator.remove();
                }
                logger.info("Removed order {} from sell orders at price {}", orderId, entry.getKey());
                return true;
            }
        }
        return false;
    }

    /**
     * Get order book snapshot for MongoDB persistence - aggregated from all segments
     */
    public Object getOrderBookSnapshotForPersistence() {
        try {
            // Create aggregated snapshot from all segments
            DistributedOrderBookSnapshot aggregatedSnapshot = new DistributedOrderBookSnapshot();

            for (int i = 0; i < ORDER_BOOK_SEGMENTS; i++) {
                DistributedOrderBookSnapshot segmentSnapshot = segmentedOrderBooks[i].get();
                if (segmentSnapshot != null) {
                    List<Order> segmentOrders = segmentSnapshot.getAllOrders();
                    if (segmentOrders != null && !segmentOrders.isEmpty()) {
                        for (Order order : segmentOrders) {
                            aggregatedSnapshot.addOrder(order);
                        }
                    }
                }
            }

            return aggregatedSnapshot;
        } catch (Exception e) {
            logger.error("Error creating aggregated snapshot for persistence", e);
            return null;
        }
    }

    /**
     * Restore from snapshot - for MongoDB recovery
     */
    public boolean restoreFromSnapshot() {
        // No snapshot data provided - return false indicating no snapshot to restore
        logger.info("Restore from snapshot called for symbol: {} - no snapshot data provided", symbol);
        return false;
    }

    /**
     * Restore from snapshot object - for MongoDB recovery
     */
    public boolean restoreFromSnapshot(Object snapshotData) {
        try {
            logger.info("Restoring FutureCoreMatchingEngine from snapshot for symbol: {}", symbol);

            if (snapshotData == null) {
                logger.warn("Snapshot data is null for symbol: {}", symbol);
                return false;
            }

            if (snapshotData instanceof DistributedOrderBookSnapshot snapshot) {
                return restoreFromDistributedSnapshot(snapshot);
            }

            // Handle Map-based snapshot (from MongoDB)
            if (snapshotData instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> snapshotMap = (Map<String, Object>) snapshotData;
                return restoreFromMapSnapshot(snapshotMap);
            }

            logger.warn("Unsupported snapshot data type: {} for symbol: {}",
                    snapshotData.getClass().getSimpleName(), symbol);
            return false;

        } catch (Exception e) {
            logger.error("Error restoring from snapshot for symbol: {}", symbol, e);
            return false;
        }
    }

    /**
     * Set engine as active
     */
    public void setActive(boolean active) {
        logger.info("Setting engine active status to {} for symbol: {}", active, symbol);
        // For now, just log. In production, this could control order processing
    }

    /**
     * Get order book for monitoring/snapshot
     */
    public Object getOrderBook() {
        try {
            DistributedOrderBookSnapshot snapshot = getOrderBookSnapshot();
            if (snapshot == null || snapshot.isEmpty()) {
                return Map.of(
                        SYMBOL, symbol,
                        BUY_ORDERS, Map.of(),
                        SELL_ORDERS, Map.of(),
                        ALL_ORDERS, List.of(),
                        STOP_ORDERS, List.of(),
                        TIMESTAMP, System.currentTimeMillis()
                );
            }

            // Convert snapshot to Map for compatibility
            Map<String, Object> orderBook = new HashMap<>();
            orderBook.put(SYMBOL, symbol);
            orderBook.put(BUY_ORDERS, snapshot.getBuyOrdersMap() != null ? snapshot.getBuyOrdersMap() : Map.of());
            orderBook.put(SELL_ORDERS, snapshot.getSellOrdersMap() != null ? snapshot.getSellOrdersMap() : Map.of());
            orderBook.put(ALL_ORDERS, snapshot.getAllOrders() != null ? snapshot.getAllOrders() : List.of());
            orderBook.put(STOP_ORDERS, snapshot.getStopOrders() != null ? snapshot.getStopOrders() : List.of());
            orderBook.put(TIMESTAMP, System.currentTimeMillis());

            return orderBook;

        } catch (Exception e) {
            logger.error("Error getting order book for symbol: {}", symbol, e);
            return Map.of(
                    SYMBOL, symbol,
                    "error", e.getMessage(),
                    TIMESTAMP, System.currentTimeMillis()
            );
        }
    }

    /**
     * Matching Algorithm enum - copy từ Future-Core
     */


    /**
     * Restore from Map-based snapshot (from MongoDB)
     */
    private boolean restoreFromMapSnapshot(Map<String, Object> snapshotMap) {
        try {
            logger.info("Restoring from Map snapshot for symbol: {}", symbol);

            // Create new empty snapshot
            DistributedOrderBookSnapshot newSnapshot = new DistributedOrderBookSnapshot();

            // Extract and restore orders
            Object allOrdersObj = snapshotMap.get("allOrders");
            Object stopOrdersObj = snapshotMap.get("stopOrders");

            int restoredCount = 0;

            // Restore all orders
            if (allOrdersObj instanceof List) {
                List<?> allOrders = (List<?>) allOrdersObj;
                for (Object orderObj : allOrders) {
                    Order order = convertOrderDocumentToOrder(orderObj);
                    if (order != null) {
                        // Add order to snapshot without processing (restore state as-is)
                        addOrderToSnapshot(order, newSnapshot);
                        restoredCount++;
                    }
                }
            }

            // Restore stop orders
            if (stopOrdersObj instanceof List) {
                List<?> stopOrders = (List<?>) stopOrdersObj;
                for (Object orderObj : stopOrders) {
                    Order order = convertOrderDocumentToOrder(orderObj);
                    if (order != null) {
                        // Add stop order to snapshot
                        newSnapshot.addStopOrder(order);
                        restoredCount++;
                    }
                }
            }

            // Update internal references: apply restored snapshot to the correct segment
            int segmentIndex = computeSegmentIndexBySymbol(this.symbol);
            segmentedOrderBooks[segmentIndex].set(newSnapshot);
            // Keep orderBookRef for compatibility
            orderBookRef.set(newSnapshot);

            logger.info("Successfully restored {} orders from Map snapshot for symbol: {} into segment {}",
                    restoredCount, symbol, segmentIndex);
            return restoredCount > 0;

        } catch (Exception e) {
            logger.error("Error restoring from Map snapshot for symbol: {}", symbol, e);
            return false;
        }
    }

    /**
     * Restore from DistributedOrderBookSnapshot
     */
    private boolean restoreFromDistributedSnapshot(DistributedOrderBookSnapshot snapshot) {
        try {
            logger.info("Restoring from DistributedOrderBookSnapshot for symbol: {}", symbol);

            // Create optimized copy of the snapshot
            DistributedOrderBookSnapshot newSnapshot = snapshot.createOptimizedCopy();

            // Apply to the correct segment so readers can see it
            int segmentIndex = computeSegmentIndexBySymbol(this.symbol);
            segmentedOrderBooks[segmentIndex].set(newSnapshot);
            // Keep orderBookRef for compatibility
            orderBookRef.set(newSnapshot);

            int totalOrders = newSnapshot.getSizeOptimized();
            logger.info("Successfully restored {} orders from DistributedOrderBookSnapshot for symbol: {} into segment {}",
                    totalOrders, symbol, segmentIndex);

            return totalOrders > 0;

        } catch (Exception e) {
            logger.error("Error restoring from DistributedOrderBookSnapshot for symbol: {}", symbol, e);
            return false;
        }
    }

    /**
     * Convert OrderDocument back to Order object
     */
    private Order convertOrderDocumentToOrder(Object orderObj) {
        try {
            if (orderObj == null) {
                return null;
            }

            // Handle Map format (from MongoDB OrderDocument)
            if (orderObj instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> orderMap = (Map<String, Object>) orderObj;
                return convertMapToOrder(orderMap);
            }

            // Handle direct Order object
            if (orderObj instanceof Order order) {
                return order;
            }

            logger.warn("Unsupported order object type: {}", orderObj.getClass().getSimpleName());
            return null;

        } catch (Exception e) {
            logger.error("Error converting order document to Order", e);
            return null;
        }
    }

    /**
     * Convert Map to Order object
     */
    private Order convertMapToOrder(Map<String, Object> orderMap) {
        try {
            // Extract required fields
            String orderId = extractStringField(orderMap, "orderId");
            Long memberId = extractLongField(orderMap, "memberId");
            String symbolMap = extractStringField(orderMap, "symbol");
            String direction = extractStringField(orderMap, "direction");
            String type = extractStringField(orderMap, "type");
            String status = extractStringField(orderMap, "status");
            Long positionId = extractLongField(orderMap, "positionId");

            // Extract Money fields
            Money price = extractMoneyField(orderMap, "price");
            Money quantity = extractMoneyField(orderMap, "quantity");
            Money filledQuantity = extractMoneyField(orderMap, "filledSize");

            // Stop price (for stop orders)
            Money stopPrice = extractMoneyField(orderMap, "stopPrice");

            // Extract timestamp
            Long createdAtMillis = extractLongField(orderMap, "createdAt");
            Instant createdAt = createdAtMillis != null ?
                    Instant.ofEpochMilli(createdAtMillis) : Instant.now();

            // Validate required fields
            if (orderId == null || memberId == null || symbolMap == null ||
                    direction == null || type == null || quantity == null) {
                logger.warn("Missing required fields in order map: {}", orderMap);
                return null;
            }

            // Build Order object
            return Order.builder()
                    .orderId(OrderId.of(orderId))
                    .memberId(memberId)
                    .symbol(Symbol.of(symbolMap))
                    .direction(OrderDirection.valueOf(direction.toUpperCase()))
                    .type(com.icetea.lotus.matching.domain.enums.OrderType.valueOf(type.toUpperCase()))
                    .price(price != null ? price : Money.ZERO)
                    .size(quantity)
                    .filledSize(filledQuantity != null ? filledQuantity : Money.ZERO)
                    .stopPrice(stopPrice)
                    .status(status != null ? OrderStatus.valueOf(status.toUpperCase()) : OrderStatus.NEW)
                    .timestamp(createdAt)
                    .positionId(positionId)
                    .build();

        } catch (Exception e) {
            logger.error("Error converting map to Order: {}", orderMap, e);
            return null;
        }
    }

    /**
     * Extract String field from map
     */
    private String extractStringField(Map<String, Object> map, String fieldName) {
        Object value = map.get(fieldName);
        return value != null ? value.toString() : null;
    }

    /**
     * Extract Long field from map
     */
    private Long extractLongField(Map<String, Object> map, String fieldName) {
        Object value = map.get(fieldName);
        if (value == null) return null;

        if (value instanceof Long longValue) return longValue;
        if (value instanceof Integer integer) return integer.longValue();
        if (value instanceof String string) {
            try {
                return Long.parseLong(string);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    /**
     * Extract Money field from map
     */
    private Money extractMoneyField(Map<String, Object> map, String fieldName) {
        Object value = map.get(fieldName);
        if (value == null) return null;

        try {
            if (value instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> moneyMap = (Map<String, Object>) value;
                Object amountObj = moneyMap.get("amount");
                if (amountObj != null) {
                    BigDecimal amount = new BigDecimal(amountObj.toString());
                    return Money.of(amount);
                }
            } else if (value instanceof Number) {
                BigDecimal amount = new BigDecimal(value.toString());
                return Money.of(amount);
            } else if (value instanceof String string) {
                BigDecimal amount = new BigDecimal(string);
                return Money.of(amount);
            }
        } catch (Exception e) {
            logger.warn("Error extracting Money field {}: {}", fieldName, e.getMessage());
        }

        return null;
    }

    // ==================== SIMD & VECTORIZATION ====================

    /**
     * ULTRA Trade creation with pre-allocated objects and minimal overhead
     */
    private Trade createTrade(Order takerOrder, Order makerOrder, Money quantity, Money price) {
        try {
            SelfTradePreventionMode stpMode = SelfTradePreventionMode.CANCEL_MAKER;
            SelfTradePreventionResult result = (stpService != null)
                    ? stpService.checkAndPreventSelfTrade(takerOrder, makerOrder, stpMode)
                    : SelfTradePreventionResult.noSelfTrade(takerOrder);

            if (!result.isTradeAllowed()) {
                return null;
            }

            // Pre-generate trade ID without string conversion
            long tradeIdLong = snowflakeIdGenerator.nextId();
            // Cache timestamp to avoid multiple Instant.now() calls
            Instant timestamp = Instant.now();

            return Trade.builder()
                    .tradeId(tradeIdLong)
                    .symbol(takerOrder.getSymbol().getValue())
                    .price(price.getValue())
                    .size(quantity.getValue())
                    .volume(quantity.multiply(price).getValue())
                    .makerOrderId(makerOrder.getOrderId().getValue())
                    .takerOrderId(takerOrder.getOrderId().getValue())
                    .takerDirection(takerOrder.getDirection())
                    .makerDirection(makerOrder.getDirection())
                    .makerMemberId(makerOrder.getMemberId())
                    .takerMemberId(takerOrder.getMemberId())
                    .makerFee(makerOrder.getFee().getValue())
                    .takerFee(takerOrder.getFee().getValue())
                    .tradeTime(timestamp)
                    .build();
        } catch (Exception ex) {
            logger.error("Error executing trade {}: {}", takerOrder.getOrderId(), ex.getMessage());
            return null;
        }
    }

    private void publishTradeExecuteEvent(String symbol, Trade trade) {
        try {
            TradeExecutedEvent event = TradeExecutedEvent.createFutureDetailed(symbol, trade.getPrice(), trade.getSize(), String.valueOf(trade.getTradeId()));
            if (applicationEventPublisher != null) {
                applicationEventPublisher.publishEvent(event);
            }
        } catch (Exception e) {
            logger.error("Error publishing Future Trade Executed: {}", trade, e);
        }
    }

    /**
     * ULTRA Buy order matching with minimal overhead
     */
    @SuppressWarnings("java:S3776")
    private void matchBuyOrder(Order buyOrder, DistributedOrderBookSnapshot snapshot, List<Trade> trades) {
        Money remainingQuantity = buyOrder.getRemainingQuantity();

        // Fast exit if no remaining quantity
        if (remainingQuantity.isZero()) {
            return;
        }

        // Cache order properties
        boolean isLimitOrder = buyOrder.getType() == OrderType.LIMIT || buyOrder.getType() == OrderType.STOP_LIMIT;
        Money buyPrice = isLimitOrder ? buyOrder.getPrice() : null;


        // Direct iterator access without validation
        Iterator<Map.Entry<Money, List<Order>>> sellIterator = snapshot.getSellOrdersIterator();

        while (sellIterator.hasNext() && !remainingQuantity.isZero()) {
            Map.Entry<Money, List<Order>> entry = sellIterator.next();
            Money priceLevel = entry.getKey();
            List<Order> sellOrders = entry.getValue();

            // Fast price check for limit orders
            if (isLimitOrder && buyPrice.getValue().compareTo(priceLevel.getValue()) < 0) {
                break;
            }

            Iterator<Order> sellOrderIterator = sellOrders.iterator();
            while (sellOrderIterator.hasNext() && !remainingQuantity.isZero()) {
                Order sellOrder = sellOrderIterator.next();

                // Fast null and quantity checks
                if (sellOrder == null || sellOrder.getRemainingQuantity().isZero()) {
                    continue;
                }

                // Fast trade quantity calculation
                Money sellQuantity = sellOrder.getRemainingQuantity();
                Money tradeQuantity = remainingQuantity.getValue().compareTo(sellQuantity.getValue()) <= 0
                        ? remainingQuantity : sellQuantity;

                // Use ultra-fast trade creation
                Trade trade = createTrade(buyOrder, sellOrder, tradeQuantity, priceLevel);
                if (trade != null) {
                    trades.add(trade);

                    // Fast quantity updates
                    remainingQuantity = remainingQuantity.subtract(tradeQuantity);
                    buyOrder.addFilledQuantity(tradeQuantity);
                    sellOrder.addFilledQuantity(tradeQuantity);

                    // Remove filled maker orders from snapshot to prevent re-matching
                    if (sellOrder.getRemainingQuantity().isZero()) {
                        sellOrderIterator.remove();
                        snapshot.removeOrder(sellOrder.getOrderId());
                        logger.info("Removed filled sell maker order {} from snapshot", sellOrder.getOrderId().getValue());
                    }
                }

                logger.info("Trade executed: buyOrder {} filled {}, sellOrder {} filled {}",
                        buyOrder.getOrderId().getValue(), tradeQuantity.getValue(),
                        sellOrder.getOrderId().getValue(), tradeQuantity.getValue());
            }

            // Remove empty price level
            if (sellOrders.isEmpty()) {
                sellIterator.remove();
            }
        }
    }

    /**
     * ULTRA Sell order matching with minimal overhead
     */
    @SuppressWarnings("java:S3776")
    private void matchSellOrder(Order sellOrder, DistributedOrderBookSnapshot snapshot, List<Trade> trades) {
        Money remainingQuantity = sellOrder.getRemainingQuantity();

        // Fast exit if no remaining quantity
        if (remainingQuantity.isZero()) {
            return;
        }

        // Cache order properties
        boolean isLimitOrder = sellOrder.getType() == OrderType.LIMIT || sellOrder.getType() == OrderType.STOP_LIMIT;
        Money sellPrice = isLimitOrder ? sellOrder.getPrice() : null;


        // Direct iterator access without validation
        Iterator<Map.Entry<Money, List<Order>>> buyIterator = snapshot.getBuyOrdersIterator();

        while (buyIterator.hasNext() && !remainingQuantity.isZero()) {
            Map.Entry<Money, List<Order>> entry = buyIterator.next();
            Money priceLevel = entry.getKey();
            List<Order> buyOrders = entry.getValue();

            // Fast price check for limit orders
            if (isLimitOrder && sellPrice.getValue().compareTo(priceLevel.getValue()) > 0) {
                break;
            }

            Iterator<Order> buyOrderIterator = buyOrders.iterator();
            while (buyOrderIterator.hasNext() && !remainingQuantity.isZero()) {
                Order buyOrder = buyOrderIterator.next();

                // Fast null and quantity checks
                if (buyOrder == null || buyOrder.getRemainingQuantity().isZero()) {
                    continue;
                }

                // Fast trade quantity calculation
                Money buyQuantity = buyOrder.getRemainingQuantity();
                Money tradeQuantity = remainingQuantity.getValue().compareTo(buyQuantity.getValue()) <= 0
                        ? remainingQuantity : buyQuantity;

                // Use ultra-fast trade creation
                Trade trade = createTrade(sellOrder, buyOrder, tradeQuantity, priceLevel);
                if (trade != null) {
                    trades.add(trade);

                    // Fast quantity updates
                    remainingQuantity = remainingQuantity.subtract(tradeQuantity);
                    sellOrder.addFilledQuantity(tradeQuantity);
                    buyOrder.addFilledQuantity(tradeQuantity);

                    // Remove filled maker orders from snapshot to prevent re-matching
                    if (buyOrder.getRemainingQuantity().isZero()) {
                        // Maker order is fully filled - remove from snapshot using iterator
                        buyOrderIterator.remove();
                        snapshot.removeOrder(buyOrder.getOrderId());
                        logger.info("Removed filled buy maker order {} from snapshot", buyOrder.getOrderId().getValue());
                    }
                }

                logger.info("Trade executed: sellOrder {} filled {}, buyOrder {} filled {}",
                        sellOrder.getOrderId().getValue(), tradeQuantity.getValue(),
                        buyOrder.getOrderId().getValue(), tradeQuantity.getValue());
            }

            // Remove empty price level
            if (buyOrders.isEmpty()) {
                buyIterator.remove();
            }
        }
    }

    /**
     * ULTRA Market buy order matching
     */
    @SuppressWarnings("java:S3776")
    private void matchMarketBuyOrder(Order marketBuyOrder, DistributedOrderBookSnapshot snapshot, List<Trade> trades, String commandType, List<Order> orderCanceled) {
        Money remainingQuantity = marketBuyOrder.getRemainingQuantity();

        if (remainingQuantity.isZero()) {
            return;
        }

        Iterator<Map.Entry<Money, List<Order>>> sellIterator = snapshot.getSellOrdersIterator();

        // Check if there are any sell orders to match against
        boolean hasMatchableOrders = false;
        while (sellIterator.hasNext()) {
            Map.Entry<Money, List<Order>> entry = sellIterator.next();
            List<Order> sellOrders = entry.getValue();
            if (sellOrders != null && !sellOrders.isEmpty()) {
                hasMatchableOrders = true;
                break;
            }
        }

        // If no matchable orders, handle market order cancellation
        if (!hasMatchableOrders && CommandTypeConstance.PLACE_ORDER.equals(commandType)) {
            handleMarketOrderWithNoMatches(marketBuyOrder);
            orderCanceled.add(marketBuyOrder);
            return;
        }

        // Reset iterator for actual matching
        sellIterator = snapshot.getSellOrdersIterator();

        while (sellIterator.hasNext() && !remainingQuantity.isZero()) {
            Map.Entry<Money, List<Order>> entry = sellIterator.next();
            Money priceLevel = entry.getKey();
            List<Order> sellOrders = entry.getValue();

            if (sellOrders == null || sellOrders.isEmpty()) {
                continue;
            }

            Iterator<Order> sellOrderIterator = sellOrders.iterator();
            while (sellOrderIterator.hasNext() && !remainingQuantity.isZero()) {
                Order sellOrder = sellOrderIterator.next();

                if (sellOrder == null || sellOrder.getRemainingQuantity().isZero()) {
                    continue;
                }

                Money sellQuantity = sellOrder.getRemainingQuantity();
                Money tradeQuantity = remainingQuantity.getValue().compareTo(sellQuantity.getValue()) <= 0
                        ? remainingQuantity : sellQuantity;

                Trade trade = createTrade(marketBuyOrder, sellOrder, tradeQuantity, priceLevel);
                if (trade != null) {
                    trades.add(trade);

                    remainingQuantity = remainingQuantity.subtract(tradeQuantity);
                    marketBuyOrder.addFilledQuantity(tradeQuantity);
                    sellOrder.addFilledQuantity(tradeQuantity);

                    // Remove filled maker orders from snapshot to prevent re-matching
                    if (sellOrder.getRemainingQuantity().isZero()) {
                        sellOrderIterator.remove();
                        snapshot.removeOrder(sellOrder.getOrderId());
                        logger.info("Removed filled sell maker order {} from snapshot", sellOrder.getOrderId().getValue());
                    }
                }
            }

            if (sellOrders.isEmpty()) {
                sellIterator.remove();
            }
        }
    }

    /**
     * ULTRA Market sell order matching
     */
    @SuppressWarnings("java:S3776")
    private void matchMarketSellOrder(Order marketSellOrder, DistributedOrderBookSnapshot snapshot, List<Trade> trades, String commandType, List<Order> orderCanceled) {
        Money remainingQuantity = marketSellOrder.getRemainingQuantity();

        if (remainingQuantity.isZero()) {
            return;
        }

        Iterator<Map.Entry<Money, List<Order>>> buyIterator = snapshot.getBuyOrdersIterator();

        // Check if there are any buy orders to match against
        boolean hasMatchableOrders = false;
        while (buyIterator.hasNext()) {
            Map.Entry<Money, List<Order>> entry = buyIterator.next();
            List<Order> buyOrders = entry.getValue();
            if (buyOrders != null && !buyOrders.isEmpty()) {
                hasMatchableOrders = true;
                break;
            }
        }

        // If no matchable orders, handle market order cancellation
        if (!hasMatchableOrders && CommandTypeConstance.PLACE_ORDER.equals(commandType)) {
            handleMarketOrderWithNoMatches(marketSellOrder);
            orderCanceled.add(marketSellOrder);
            return;
        }

        // Reset iterator for actual matching
        buyIterator = snapshot.getBuyOrdersIterator();

        while (buyIterator.hasNext() && !remainingQuantity.isZero()) {
            Map.Entry<Money, List<Order>> entry = buyIterator.next();
            Money priceLevel = entry.getKey();
            List<Order> buyOrders = entry.getValue();

            if (buyOrders == null || buyOrders.isEmpty()) {
                continue;
            }

            Iterator<Order> buyOrderIterator = buyOrders.iterator();
            while (buyOrderIterator.hasNext() && !remainingQuantity.isZero()) {
                Order buyOrder = buyOrderIterator.next();

                if (buyOrder == null || buyOrder.getRemainingQuantity().isZero()) {
                    continue;
                }

                Money buyQuantity = buyOrder.getRemainingQuantity();
                Money tradeQuantity = remainingQuantity.getValue().compareTo(buyQuantity.getValue()) <= 0
                        ? remainingQuantity : buyQuantity;

                Trade trade = createTrade(marketSellOrder, buyOrder, tradeQuantity, priceLevel);
                if (trade != null) {
                    trades.add(trade);

                    remainingQuantity = remainingQuantity.subtract(tradeQuantity);
                    marketSellOrder.addFilledQuantity(tradeQuantity);
                    buyOrder.addFilledQuantity(tradeQuantity);

                    // Remove filled maker orders from snapshot to prevent re-matching
                    if (buyOrder.getRemainingQuantity().isZero()) {
                        buyOrderIterator.remove();
                        snapshot.removeOrder(buyOrder.getOrderId());
                        logger.info("Removed filled buy maker order {} from snapshot", buyOrder.getOrderId().getValue());
                    }
                }
            }

            if (buyOrders.isEmpty()) {
                buyIterator.remove();
            }
        }
    }

    // ==================== MARKET ORDER HANDLING METHODS ====================

    /**
     * Handle market order when no matches available
     */
    private void handleMarketOrderWithNoMatches(Order marketOrder) {
        logger.info("Market {} order {} cancelled immediately - no limit orders available in queue",
                marketOrder.getDirection(), marketOrder.getOrderId().getValue());
        // Publish cancel event to Kafka via CANCEL_ORDER command path
        futureCoreKafkaProducer.sendCancelOrderCommand(marketOrder);
    }

    // ==================== ORDERBOOK UPDATE METHODS ====================

    /**
     * Create orderbook snapshot from current state
     */
    public FutureTradePlateMessage.OrderBookSnapshot createOrderBookSnapshot(String symbol) {
        try {
            List<Order> allOrders = new ArrayList<>();

            // Collect orders from all segments
            for (int i = 0; i < ORDER_BOOK_SEGMENTS; i++) {
                DistributedOrderBookSnapshot segmentSnapshot = segmentedOrderBooks[i].get();
                if (segmentSnapshot != null) {
                    List<Order> segmentOrders = segmentSnapshot.getAllOrders();
                    if (segmentOrders != null && !segmentOrders.isEmpty()) {
                        allOrders.addAll(segmentOrders);
                    }
                }
            }

            logger.info("Collected {} orders from {} segments for symbol {}",
                    allOrders.size(), ORDER_BOOK_SEGMENTS, symbol);

            // Convert to price levels - compatible với future-core format
            List<FutureTradePlateMessage.PriceLevelDto> asks = convertOrdersToPriceLevels(allOrders, OrderDirection.SELL);
            List<FutureTradePlateMessage.PriceLevelDto> bids = convertOrdersToPriceLevels(allOrders, OrderDirection.BUY);

            return FutureTradePlateMessage.OrderBookSnapshot.builder()
                    .symbol(symbol)
                    .asks(asks)
                    .bids(bids)
                    .build();

        } catch (Exception e) {
            logger.error("Error creating orderbook snapshot for symbol: {}", symbol, e);
            return FutureTradePlateMessage.OrderBookSnapshot.builder()
                    .symbol(symbol)
                    .asks(Collections.emptyList())
                    .bids(Collections.emptyList())
                    .build();
        }
    }

    /**
     * Convert orders to price levels for orderbook display - compatible với future-core
     */
    private List<FutureTradePlateMessage.PriceLevelDto> convertOrdersToPriceLevels(List<Order> orders, OrderDirection direction) {
        Map<BigDecimal, FutureTradePlateMessage.PriceLevelDto> priceLevelMap = new HashMap<>();

        for (Order order : orders) {
            if (order.getDirection() == direction && (Arrays.asList(OrderStatus.NEW, OrderStatus.PARTIALLY_FILLED, OrderStatus.TRIGGERED).contains(order.getStatus()))) {
                BigDecimal price = order.getPrice().getValue();
                FutureTradePlateMessage.PriceLevelDto level = priceLevelMap.get(price);

                if (level == null) {
                    level = FutureTradePlateMessage.PriceLevelDto.builder()
                            .price(price)
                            .volume(order.getRemainingQuantity().getValue())  // Use volume instead of amount
                            .orderCount(1)
                            .build();
                    priceLevelMap.put(price, level);
                } else {
                    level.setVolume(level.getVolume().add(order.getRemainingQuantity().getValue()));
                    level.setOrderCount(level.getOrderCount() + 1);
                }
            }
        }

        return new ArrayList<>(priceLevelMap.values());
    }

    public void saveLastPriceToMongoDB(String symbol, Money price) {
        try {
            String collectionName = "future_last_price";

            Map<String, Object> lastPriceDoc = new HashMap<>();
            lastPriceDoc.put(SYMBOL, symbol);
            lastPriceDoc.put(PRICE, price);
            lastPriceDoc.put(TIMESTAMP, System.currentTimeMillis());
            lastPriceDoc.put("updatedAt", new Date());

            // Upsert: update if exists, insert if not exists
            if (mongoTemplate != null) {
                Query query = new Query(Criteria.where(SYMBOL).is(symbol));
                Update update = new Update()
                        .set(PRICE, price)
                        .set(TIMESTAMP, System.currentTimeMillis())
                        .set("updatedAt", new Date());

                mongoTemplate.upsert(query, update, collectionName);
                logger.info("Saved last price to MongoDB: symbol={}, price={}", symbol, price);
            } else {
                logger.info("Skipping MongoDB upsert for last price (mongoTemplate is null): symbol={}, price={}", symbol, price);
            }

        } catch (Exception e) {
            logger.error("Error saving last price to MongoDB: symbol={}, price={}", symbol, price, e);
        }
    }

    public enum MatchingAlgorithm {
        FIFO,       // First In First Out
        PRO_RATA,   // Proportional allocation
        HYBRID,     // Combination of FIFO and Pro-Rata
        TWAP        // Time Weighted Average Price
    }
}
