package com.icetea.lotus.matching.infrastructure.stp;

/**
 * <AUTHOR> nguyen
 */

import lombok.Builder;
import lombok.Data;

/**
 * Self Trade Prevention Result
 * Migrated from future-core
 */
@Data
@Builder
public class SelfTradePreventionResult {
    
    private final boolean isSelfTrade;
    private final boolean tradeAllowed;
    private final boolean cancelTaker;
    private final boolean cancelMaker;
    private final Object takerOrder;
    private final Object makerOrder;
    private final String reason;
    
    /**
     * No self-trade detected
     * @param takerOrder Taker order
     * @return STP result
     */
    public static SelfTradePreventionResult noSelfTrade(Object takerOrder) {
        return SelfTradePreventionResult.builder()
                .isSelfTrade(false)
                .tradeAllowed(true)
                .cancelTaker(false)
                .cancelMaker(false)
                .takerOrder(takerOrder)
                .makerOrder(null)
                .reason("No self-trade")
                .build();
    }
    
    /**
     * Allow self-trade
     * @param takerOrder Taker order
     * @param makerOrder Maker order
     * @return STP result
     */
    public static SelfTradePreventionResult allowSelfTrade(Object takerOrder, Object makerOrder) {
        return SelfTradePreventionResult.builder()
                .isSelfTrade(true)
                .tradeAllowed(true)
                .cancelTaker(false)
                .cancelMaker(false)
                .takerOrder(takerOrder)
                .makerOrder(makerOrder)
                .reason("Self-trade allowed by STP mode NONE")
                .build();
    }
    
    /**
     * Cancel taker order
     * @param takerOrder Taker order
     * @param makerOrder Maker order
     * @return STP result
     */
    public static SelfTradePreventionResult cancelTaker(Object takerOrder, Object makerOrder) {
        return SelfTradePreventionResult.builder()
                .isSelfTrade(true)
                .tradeAllowed(false)
                .cancelTaker(true)
                .cancelMaker(false)
                .takerOrder(takerOrder)
                .makerOrder(makerOrder)
                .reason("Taker order cancelled due to self-trade prevention")
                .build();
    }
    
    /**
     * Cancel maker order
     * @param takerOrder Taker order
     * @param makerOrder Maker order
     * @return STP result
     */
    public static SelfTradePreventionResult cancelMaker(Object takerOrder, Object makerOrder) {
        return SelfTradePreventionResult.builder()
                .isSelfTrade(true)
                .tradeAllowed(false)
                .cancelTaker(false)
                .cancelMaker(true)
                .takerOrder(takerOrder)
                .makerOrder(makerOrder)
                .reason("Maker order cancelled due to self-trade prevention")
                .build();
    }
    
    /**
     * Cancel both orders
     * @param takerOrder Taker order
     * @param makerOrder Maker order
     * @return STP result
     */
    public static SelfTradePreventionResult cancelBoth(Object takerOrder, Object makerOrder) {
        return SelfTradePreventionResult.builder()
                .isSelfTrade(true)
                .tradeAllowed(false)
                .cancelTaker(true)
                .cancelMaker(true)
                .takerOrder(takerOrder)
                .makerOrder(makerOrder)
                .reason("Both orders cancelled due to self-trade prevention")
                .build();
    }
    
    /**
     * Check if any order should be cancelled
     * @return true if any order should be cancelled
     */
    public boolean shouldCancelAnyOrder() {
        return cancelTaker || cancelMaker;
    }
    
    /**
     * Check if both orders should be cancelled
     * @return true if both orders should be cancelled
     */
    public boolean shouldCancelBothOrders() {
        return cancelTaker && cancelMaker;
    }
}
