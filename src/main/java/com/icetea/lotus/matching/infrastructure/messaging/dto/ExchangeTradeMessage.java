package com.icetea.lotus.matching.infrastructure.messaging.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Exchange Trade Message DTO for Kafka messaging
 * Compatible with Exchange module format
 * 
 * <AUTHOR> nguyen
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExchangeTradeMessage {
    
    /**
     * Trade ID
     */
    private String tradeId;
    
    /**
     * Trading symbol
     */
    private String symbol;
    
    /**
     * Trade price
     */
    private BigDecimal price;
    
    /**
     * Trade quantity
     */
    private BigDecimal quantity;
    
    /**
     * Taker order ID
     */
    private String takerOrderId;
    
    /**
     * Maker order ID
     */
    private String makerOrderId;
    
    /**
     * Taker user ID
     */
    private Long takerUserId;
    
    /**
     * Maker user ID
     */
    private Long makerUserId;
    
    /**
     * Trade direction from taker perspective
     * BUY or SELL
     */
    private String direction;
    
    /**
     * Trade timestamp
     */
    private LocalDateTime timestamp;
    
    /**
     * Trade type (SPOT, FUTURES, etc.)
     */
    private String tradeType;
    
    /**
     * Fee amount for taker
     */
    private BigDecimal takerFee;
    
    /**
     * Fee amount for maker
     */
    private BigDecimal makerFee;
    
    /**
     * Fee currency
     */
    private String feeCurrency;
    
    /**
     * Additional metadata
     */
    private String metadata;
}
