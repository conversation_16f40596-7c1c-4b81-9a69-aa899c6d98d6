package com.icetea.lotus.matching.infrastructure.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * Async Executor Configuration cho Matching Engine Module
 * 
 * C<PERSON>u hình các thread pool executors cho xử lý bất đồng bộ
 * trong matching engine, đặc biệt cho stop order processing.
 * 
 * <AUTHOR> nguyen
 */
@Configuration
@EnableAsync
public class AsyncExecutorConfig {
    
    /**
     * Thread pool cho stop order processing
     * Optimized cho stop order trigger processing
     */
    @Bean("stopOrderExecutor")
    public Executor stopOrderExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // Core pool size - nhiều hơn trade event vì stop order processing phức tạp hơn
        executor.setCorePoolSize(6);
        
        // Max pool size
        executor.setMaxPoolSize(12);
        
        // Queue capacity - lớn hơn để handle burst traffic
        executor.setQueueCapacity(2000);
        
        // Thread name prefix
        executor.setThreadNamePrefix("stop-order-");
        
        // Keep alive time
        executor.setKeepAliveSeconds(120);
        
        // Rejection policy khi queue đầy
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // Graceful shutdown
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        return executor;
    }
    

    
    /**
     * Thread pool cho trade event publishing
     * Optimized cho high-frequency event publishing
     */
    @Bean("tradeEventExecutor")
    public Executor tradeEventExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // Core pool size - số threads luôn sẵn sàng
        executor.setCorePoolSize(4);
        
        // Max pool size - số threads tối đa
        executor.setMaxPoolSize(8);
        
        // Queue capacity - số events có thể queue
        executor.setQueueCapacity(1000);
        
        // Thread name prefix
        executor.setThreadNamePrefix("trade-event-");
        
        // Keep alive time cho idle threads
        executor.setKeepAliveSeconds(60);
        
        // Rejection policy khi queue đầy
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        
        executor.initialize();
        return executor;
    }
    
    /**
     * Thread pool cho general async tasks
     * Backup executor cho các tasks khác
     */
    @Bean("generalAsyncExecutor")
    public Executor generalAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // Smaller pool cho general tasks
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(4);
        executor.setQueueCapacity(500);
        executor.setThreadNamePrefix("async-general-");
        executor.setKeepAliveSeconds(60);
        
        // Default rejection policy
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        
        executor.initialize();
        return executor;
    }
}
