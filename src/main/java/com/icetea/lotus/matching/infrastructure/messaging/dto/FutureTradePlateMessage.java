package com.icetea.lotus.matching.infrastructure.messaging.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Message DTO cho Future Order Book Updates
 * Gửi từ matching-engine sang market module để xử lý orderbook updates
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FutureTradePlateMessage implements Serializable {

    /**
     * Symbol của contract
     */
    private String symbol;

    /**
     * Timestamp của update
     */
    private LocalDateTime timestamp;

    /**
     * Order book snapshot sau khi update
     */
    private OrderBookSnapshot orderBookSnapshot;

    /**
     * <PERSON><PERSON> sách trades được tạo ra (nếu có)
     */
    private transient List<Object> trades;

    /**
     * Order được update (nếu có)
     */
    private transient Object updatedOrder;

    /**
     * Danh sách orders đã completed (nếu có)
     */
    private transient List<Object> completedOrders;



    /**
     * Nested class cho Order Book Snapshot - Simplified version
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class OrderBookSnapshot implements Serializable {
        /**
         * Symbol của contract
         */
        private String symbol;

        /**
         * Danh sách các mức giá bán (asks)
         */
        private List<PriceLevelDto> asks;

        /**
         * Danh sách các mức giá mua (bids)
         */
        private List<PriceLevelDto> bids;
    }

    /**
     * Nested class cho Price Level - Compatible với future-core PriceLevelDto
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class PriceLevelDto implements Serializable {
        /**
         * Giá
         */
        private BigDecimal price;

        /**
         * Khối lượng (volume thay vì amount để tương thích với future-core)
         */
        private BigDecimal volume;

        /**
         * Số lượng lệnh
         */
        private Integer orderCount;
    }



    /**
     * Factory method để tạo orderbook update message
     */
    public static FutureTradePlateMessage createTradePlateMessage(String symbol, OrderBookSnapshot snapshot) {
        return FutureTradePlateMessage.builder()
                .symbol(symbol)
                .timestamp(LocalDateTime.now())
                .orderBookSnapshot(snapshot)
                .build();
    }
}
