package com.icetea.lotus.matching.infrastructure.config;

import com.icetea.lotus.matching.infrastructure.messaging.filter.SymbolOwnershipFilterStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.listener.DefaultErrorHandler;

import java.util.HashMap;
import java.util.Map;

/**
 * Consolidated Kafka configuration for both spot and futures trading
 * Handles all Kafka topics in the matching-engine module
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class ConsolidatedKafkaConfig {

    private final ObjectProvider<SymbolOwnershipFilterStrategy> symbolOwnershipFilterStrategyProvider;

    @Value("${spring.kafka.bootstrap-servers}")
    private String bootstrapServers;

    @Value("${spring.kafka.consumer.group-id:matching-engine-group}")
    private String defaultGroupId;

    // ==================== PRODUCER CONFIGURATION ====================

    @Bean
    public ProducerFactory<String, String> producerFactory() {
        Map<String, Object> configProps = new HashMap<>();
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);

        // Performance optimizations
        configProps.put(ProducerConfig.ACKS_CONFIG, "all"); // Required for idempotent producer
        configProps.put(ProducerConfig.RETRIES_CONFIG, 3);
        configProps.put(ProducerConfig.BATCH_SIZE_CONFIG, 16384);
        configProps.put(ProducerConfig.LINGER_MS_CONFIG, 1);
        configProps.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 33554432);
        configProps.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, "gzip");

        // Reliability settings
        configProps.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, true);
        configProps.put(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION, 5);

        return new DefaultKafkaProducerFactory<>(configProps);
    }

    @Bean
    public KafkaTemplate<String, String> kafkaTemplate() {
        KafkaTemplate<String, String> template = new KafkaTemplate<>(producerFactory());

        // Set default topic for convenience
        template.setDefaultTopic("matching-engine-default");

        return template;
    }

    // ==================== CONSUMER CONFIGURATION ====================

    /**
     * Base consumer configuration
     */
    private Map<String, Object> baseConsumerConfig(String groupId) {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, groupId);

        // Use simple String deserializers for now
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);

        // Performance settings
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 50);
        props.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, 300000);
        props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 15000);
        props.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, 5000);

        return props;
    }

    /**
     * Spot consumer factory
     */
    @Bean
    public ConsumerFactory<String, String> spotConsumerFactory() {
        return new DefaultKafkaConsumerFactory<>(baseConsumerConfig("spot-trading-group"));
    }

    /**
     * Futures consumer factory
     */
    @Bean
    public ConsumerFactory<String, String> futuresConsumerFactory() {
        return new DefaultKafkaConsumerFactory<>(baseConsumerConfig("futures-trading-group"));
    }

    /**
     * Spot Kafka listener container factory
     */
    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> spotKafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory =
                new ConcurrentKafkaListenerContainerFactory<>();

        factory.setConsumerFactory(spotConsumerFactory());

        // Spot-specific settings
        factory.setConcurrency(9); // 9 consumers as per current setup
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);
        factory.getContainerProperties().setPollTimeout(3000);

        // Enable batch listener for List<ConsumerRecord> support
        factory.setBatchListener(true);

        // Error handling
        factory.setCommonErrorHandler(new DefaultErrorHandler((consumerRecord, exception)
                -> log.error("Error in spot Kafka listener", exception)));

        // Set the custom record filter strategy using ObjectProvider to break circular dependency
        factory.setRecordFilterStrategy(symbolOwnershipFilterStrategyProvider.getObject());
        factory.setAckDiscarded(true); // Acknowledge discarded records (safe vì đã check hasOtherOwner)

        return factory;
    }

    /**
     * Futures Kafka listener container factory
     */
    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> futuresKafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory =
                new ConcurrentKafkaListenerContainerFactory<>();

        factory.setConsumerFactory(futuresConsumerFactory());

        // Futures-specific settings (higher throughput)
        factory.setConcurrency(14); // More consumers for futures (added INPUT topics: order-commands, order-routing)
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);
        factory.getContainerProperties().setPollTimeout(3000);

        // Enable batch listener for List<ConsumerRecord> support
        factory.setBatchListener(true);

        // Error handling
        factory.setCommonErrorHandler(new DefaultErrorHandler((consumerRecord, exception) ->
                log.error("Error in futures Kafka listener", exception)));

        // Apply the same filter strategy for consistency with spot trading
        factory.setRecordFilterStrategy(symbolOwnershipFilterStrategyProvider.getObject());
        factory.setAckDiscarded(true); // Safe vì đã check hasOtherOwner trong filter

        return factory;
    }

    /**
     * Default Kafka listener container factory
     */
    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> kafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory =
                new ConcurrentKafkaListenerContainerFactory<>();

        factory.setConsumerFactory(new DefaultKafkaConsumerFactory<>(baseConsumerConfig(defaultGroupId)));
        factory.setConcurrency(5);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);

        return factory;
    }

    // ==================== TOPIC MANAGEMENT ====================

    /**
     * Topic configuration properties
     */
    @Bean
    public TopicConfigProperties topicConfigProperties() {
        return TopicConfigProperties.builder()
                .defaultPartitions(10)
                .defaultReplicationFactor(3)
                .retentionMs(604800000L) // 7 days
                .build();
    }

    /**
     * Topic configuration properties class
     */
    public static class TopicConfigProperties {
        private final int defaultPartitions;
        private final int defaultReplicationFactor;
        private final long retentionMs;

        private TopicConfigProperties(int defaultPartitions, int defaultReplicationFactor, long retentionMs) {
            this.defaultPartitions = defaultPartitions;
            this.defaultReplicationFactor = defaultReplicationFactor;
            this.retentionMs = retentionMs;
        }

        public static TopicConfigPropertiesBuilder builder() {
            return new TopicConfigPropertiesBuilder();
        }

        public int getDefaultPartitions() {
            return defaultPartitions;
        }

        public int getDefaultReplicationFactor() {
            return defaultReplicationFactor;
        }

        public long getRetentionMs() {
            return retentionMs;
        }

        public static class TopicConfigPropertiesBuilder {
            private int defaultPartitions = 10;
            private int defaultReplicationFactor = 3;
            private long retentionMs = 604800000L;

            public TopicConfigPropertiesBuilder defaultPartitions(int defaultPartitions) {
                this.defaultPartitions = defaultPartitions;
                return this;
            }

            public TopicConfigPropertiesBuilder defaultReplicationFactor(int defaultReplicationFactor) {
                this.defaultReplicationFactor = defaultReplicationFactor;
                return this;
            }

            public TopicConfigPropertiesBuilder retentionMs(long retentionMs) {
                this.retentionMs = retentionMs;
                return this;
            }

            public TopicConfigProperties build() {
                return new TopicConfigProperties(defaultPartitions, defaultReplicationFactor, retentionMs);
            }
        }
    }
}
