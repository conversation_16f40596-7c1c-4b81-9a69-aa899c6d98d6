package com.icetea.lotus.matching.infrastructure.config;

import com.icetea.lotus.matching.infrastructure.futurecore.FutureCoreCompatibilityService;
import com.icetea.lotus.matching.infrastructure.integration.MatchingEngineIntegrationService;
import com.icetea.lotus.matching.infrastructure.sharding.SymbolShardingManager;
import com.icetea.lotus.matching.infrastructure.exchange.ExchangeCompatibilityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Matching Engine Startup Event - Copy từ Exchange CoinTraderEvent pattern
 * Khôi phục order book từ database khi startup ứng dụng
 *
 * <AUTHOR> nguyen
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MatchingEngineStartupEvent implements ApplicationListener<ContextRefreshedEvent> {

    private final MatchingEngineIntegrationService integrationService;
    private final SymbolShardingManager shardingManager;
    private final ExchangeCompatibilityService exchangeService;
    private final FutureCoreCompatibilityService futureCoreCompatibilityService;

    @Value("${matching-engine.redis.snapshot.startup-restore-enabled:true}")
    private boolean startupRestoreEnabled;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        log.info("======initialize matching engines======");
        log.info("Startup restore enabled: {}", startupRestoreEnabled);

        try {
            // Get symbols owned by this pod
            List<String> ownedSymbols = shardingManager.getOwnedSymbols();
            log.info("Found {} symbols owned by this pod: {}", ownedSymbols.size(), ownedSymbols);

            // Process each symbol in parallel
            ownedSymbols.parallelStream()
                    .forEach(this::processSymbol);

            log.info("======matching engine initialization completed======");

        } catch (Exception e) {
            log.error("Error during matching engine startup", e);
        }
    }

    /**
     * Process single symbol - khôi phục order book từ Redis snapshot hoặc database
     */
    private void processSymbol(String symbol) {
        try {
            log.info("======Processing Symbol: {}======", symbol);

            //   CRITICAL FIX: Khôi phục last trade price từ database trước
            exchangeService.restoreLastTradePriceFromDatabase(symbol);
            futureCoreCompatibilityService.restoreLastTradePriceFromDatabase(symbol);
            boolean restored = false;

            // Khôi phục order book từ Redis snapshot hoặc database
            if (startupRestoreEnabled) {
                log.info("Startup restore enabled, attempting to restore order book for symbol: {}", symbol);
                restored = integrationService.restoreOrderBookForSymbol(symbol);

                if (restored) {
                    log.info("Successfully restored order book from snapshot/database for symbol: {}", symbol);
                } else {
                    log.info("No snapshot/pending orders found for symbol: {}, starting with empty order book", symbol);
                }
            } else {
                log.info("Startup restore disabled, starting with empty order book for symbol: {}", symbol);
            }

            // Initialize matching engine cho symbol
            integrationService.initializeMatchingEngine(symbol);

            log.info("Matching engine ready for symbol: {} (restored: {})", symbol, restored);

        } catch (Exception e) {
            log.error("Error processing symbol: {}", symbol, e);
        }
    }
}
