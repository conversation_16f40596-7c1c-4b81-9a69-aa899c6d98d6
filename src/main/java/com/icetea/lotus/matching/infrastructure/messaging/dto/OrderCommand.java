package com.icetea.lotus.matching.infrastructure.messaging.dto;

import com.icetea.lotus.matching.domain.entity.Order;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Command for order-related operations
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderCommand {

    /**
     * Command ID
     */
    private String commandId;

    /**
     * Command type
     */
    private OrderCommandType type;

    /**
     * Order
     */
    private Order order;

    /**
     * Command creation time
     */
    private LocalDateTime timestamp;

    /**
     * Command type
     */
    public enum OrderCommandType {
        /**
         * Place order
         */
        PLACE_ORDER,

        /**
         * Cancel order
         */
        CANCEL_ORDER,

        /**
         * Update order
         */
        UPDATE_ORDER,

        /**
         * Order close position
         */
        ORDER_CLOSE_POSITION
    }

    private String requestId;
}
