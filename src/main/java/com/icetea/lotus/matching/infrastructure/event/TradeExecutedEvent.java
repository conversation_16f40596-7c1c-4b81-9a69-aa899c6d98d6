package com.icetea.lotus.matching.infrastructure.event;

import com.icetea.lotus.matching.domain.enums.SpotOrderDirection;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Random;

/**
 * Trade Executed Event - Event được publish sau khi có order khớp trong matching engine
 * 
 * Event này được sử dụng để trigger stop order processing một cách bất đồng bộ,
 * tránh làm chậm luồng match order chính.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TradeExecutedEvent {

    public static final String SPOT_MATCHING_ENGINE = "spot";
    public static final String FUTURE_MATCHING_ENGINE = "future";
    /**
     * Symbol - Cặp giao dịch (e.g., BTC/USDT)
     */
    private String symbol;
    
    /**
     * Price - G<PERSON><PERSON> khớp lệnh
     */
    private BigDecimal price;
    
    /**
     * Quantity - Khối lượng khớp
     */
    private BigDecimal quantity;
    
    /**
     * Timestamp - Thời gian khớp lệnh
     */
    private Long timestamp;
    
    /**
     * Trade ID - ID của trade
     */
    private String tradeId;
    
    /**
     * Direction - Hướng của trade (BUY/SELL)
     */
    private SpotOrderDirection direction;
    
    /**
     * Taker Order ID - ID của taker order
     */
    private String takerOrderId;
    
    /**
     * Maker Order ID - ID của maker order
     */
    private String makerOrderId;
    
    // ========== EVENT METADATA ==========
    
    /**
     * Event ID - Unique identifier cho event
     */
    private String eventId;
    
    /**
     * Event Timestamp - Thời gian tạo event
     */
    private Long eventTimestamp;
    
    /**
     * Source - Nguồn tạo event
     */
    private String source;
    
    /**
     * Event Version - Version của event schema
     */
    private String eventVersion;
    
    /**
     * Processing Priority - Độ ưu tiên xử lý (1=highest, 10=lowest)
     */
    private Integer priority;
    
    /**
     * Retry Count - Số lần retry đã thực hiện
     */
    private Integer retryCount;
    
    /**
     * Max Retries - Số lần retry tối đa
     */
    private Integer maxRetries;

    @SuppressWarnings("java:S2245")
    private static final Random random = new Random();



    /**
     * Create event with default metadata
     * 
     * @param symbol Trading symbol
     * @param price Trade price
     * @param quantity Trade quantity
     * @param tradeId Trade ID
     * @return TradeExecutedEvent with metadata
     */
    public static TradeExecutedEvent create(String symbol, BigDecimal price, BigDecimal quantity, String tradeId) {
        return TradeExecutedEvent.builder()
            .symbol(symbol)
            .price(price)
            .quantity(quantity)
            .tradeId(tradeId)
            .timestamp(System.currentTimeMillis())
            .eventId(generateEventId())
            .eventTimestamp(System.currentTimeMillis())
            .source(SPOT_MATCHING_ENGINE)
            .eventVersion("1.0")
            .priority(5) // Default priority
            .retryCount(0)
            .maxRetries(3)
            .build();
    }
    
    /**
     * Create event from trade details for spot matching engine
     *
     * @param symbol Trading symbol
     * @param price Trade price
     * @param quantity Trade quantity
     * @param tradeId Trade ID
     * @param direction Trade direction
     * @param takerOrderId Taker order ID
     * @param makerOrderId Maker order ID
     * @return TradeExecutedEvent with full details
     */
    public static TradeExecutedEvent createSpotDetailed(String symbol, BigDecimal price, BigDecimal quantity,
                                                        String tradeId, SpotOrderDirection direction,
                                                        String takerOrderId, String makerOrderId) {
        return TradeExecutedEvent.builder()
            .symbol(symbol)
            .price(price)
            .quantity(quantity)
            .tradeId(tradeId)
            .direction(direction)
            .takerOrderId(takerOrderId)
            .makerOrderId(makerOrderId)
            .timestamp(System.currentTimeMillis())
            .eventId(generateEventId())
            .eventTimestamp(System.currentTimeMillis())
            .source(SPOT_MATCHING_ENGINE)
            .eventVersion("1.0")
            .priority(5)
            .retryCount(0)
            .maxRetries(3)
            .build();
    }

    /**
     * Create event from trade details for spot matching engine
     *
     * @param symbol Trading symbol
     * @param price Trade price
     * @param quantity Trade quantity
     * @param tradeId Trade ID
     * @return TradeExecutedEvent with full details
     */
    public static TradeExecutedEvent createFutureDetailed(String symbol, BigDecimal price, BigDecimal quantity,
                                                        String tradeId) {
        return TradeExecutedEvent.builder()
            .symbol(symbol)
            .price(price)
            .quantity(quantity)
            .tradeId(tradeId)
            .timestamp(System.currentTimeMillis())
            .eventId(generateEventId())
            .eventTimestamp(System.currentTimeMillis())
            .source(FUTURE_MATCHING_ENGINE)
            .eventVersion("1.0")
            .priority(5)
            .retryCount(0)
            .maxRetries(3)
            .build();
    }

    /**
     * Generate unique event ID
     */
    private static String generateEventId() {
        return "TE_" + System.currentTimeMillis() + "_" +
               Integer.toHexString(random.nextInt() * 0xFFFF);
    }

    /**
     * For successful trades, we only need symbol and price to trigger stop orders
     */
    public boolean isValidForStopOrderTrigger() {
        return symbol != null && !symbol.trim().isEmpty()
            && price != null && price.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * Get event age in milliseconds
     */
    public long getEventAge() {
        if (eventTimestamp == null) {
            return 0;
        }
        return System.currentTimeMillis() - eventTimestamp;
    }
    
    /**
     * Check if event is stale (older than threshold)
     */
    public boolean isStale(long thresholdMs) {
        return getEventAge() > thresholdMs;
    }
    
    /**
     * Get description for logging
     */
    public String getDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append("TradeExecuted[");
        sb.append("symbol=").append(symbol);
        sb.append(", price=").append(price);
        sb.append(", quantity=").append(quantity);
        sb.append(", tradeId=").append(tradeId);
        if (direction != null) {
            sb.append(", direction=").append(direction);
        }
        sb.append(", eventId=").append(eventId);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public String toString() {
        return getDescription();
    }
}
