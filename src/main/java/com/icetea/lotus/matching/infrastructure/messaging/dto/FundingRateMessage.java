package com.icetea.lotus.matching.infrastructure.messaging.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Funding Rate Message DTO
 * Extracted từ FutureCoreKafkaProducer
 * 
 * <AUTHOR> nguyen
 */
@Data
@Builder
public class FundingRateMessage {
    private String symbol;
    private BigDecimal fundingRate;
    private BigDecimal fundingFee;
    private LocalDateTime nextFundingTime;
    private LocalDateTime timestamp;
}
