package com.icetea.lotus.matching.infrastructure.exchange;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.matching.domain.enums.SpotOrderDirection;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Exchange Trade Data - Đồng bộ hoàn toàn với exchange-core.ExchangeTrade
 * Exact same structure như exchange-core.ExchangeTrade để đảm bảo compatibility
 * Áp dụng logic tính toán từ CoinTraderV2.java:867
 *
 * <AUTHOR> nguyen
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExchangeTradeData implements Serializable {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Trading symbol (e.g., "BTC/USDT")
     */
    private String symbol;

    /**
     * Trade execution price
     */
    private BigDecimal price;

    /**
     * Trade amount/quantity
     */
    private BigDecimal amount;

    /**
     * Buy side turnover (price * amount for buy side)
     * Tính toán theo logic CoinTraderV2: turnover + adjustTurnover cho market buy orders
     */
    private BigDecimal buyTurnover;

    /**
     * Sell side turnover (price * amount for sell side)
     * Luôn bằng price * amount cho sell orders
     */
    private BigDecimal sellTurnover;

    /**
     * Trade direction from taker perspective
     * BUY = taker is buying (maker is selling)
     * SELL = taker is selling (maker is buying)
     */
    private SpotOrderDirection direction;

    /**
     * Buy order ID (order that bought)
     * Logic từ CoinTraderV2:867 - nếu focusedOrder.direction == BUY thì buyOrderId = focusedOrder.orderId
     */
    private String buyOrderId;

    /**
     * Sell order ID (order that sold)
     * Logic từ CoinTraderV2:867 - nếu focusedOrder.direction == SELL thì sellOrderId = focusedOrder.orderId
     */
    private String sellOrderId;

    /**
     * Trade execution time (timestamp in milliseconds)
     * Theo CoinTraderV2:871 - Calendar.getInstance().getTimeInMillis()
     */
    private Long time;

    /**
     * Indicates if this trade resulted in partially filled orders
     * Used by market service to handle order status updates
     */
    private Boolean isPartiallyFilled;

    /**
     * JSON serialization - đồng bộ với exchange-core.ExchangeTrade
     */
    @Override
    public String toString() {
        try {
            return objectMapper.writeValueAsString(this);
        } catch (JsonProcessingException e) {
            return "{\"error\":\"Failed to serialize ExchangeTradeData\"}";
        }
    }

    /**
     * Calculate basic turnover (price * amount)
     * Đây là turnover cơ bản trước khi adjust cho market orders
     */
    public BigDecimal getBasicTurnover() {
        if (price != null && amount != null) {
            return price.multiply(amount);
        }
        return BigDecimal.ZERO;
    }

    /**
     * Set turnover values theo logic CoinTraderV2
     * @param basicTurnover turnover cơ bản (price * amount)
     * @param adjustTurnover turnover điều chỉnh cho market buy orders (có thể null)
     * @param isBuyMarketOrder có phải market buy order không
     */
    public void setTurnoverValues(BigDecimal basicTurnover, BigDecimal adjustTurnover, boolean isBuyMarketOrder) {
        if (isBuyMarketOrder && adjustTurnover != null) {
            // Logic từ CoinTraderV2:856,860 - buyTurnover = turnover + adjustTurnover cho market buy
            this.buyTurnover = basicTurnover.add(adjustTurnover);
            this.sellTurnover = basicTurnover;
        } else {
            // Trường hợp bình thường - cả buy và sell đều bằng basicTurnover
            this.buyTurnover = basicTurnover;
            this.sellTurnover = basicTurnover;
        }
    }

    /**
     * Set order IDs theo logic CoinTraderV2:863-869
     * @param focusedOrderId ID của focused order (order đang được xử lý)
     * @param matchOrderId ID của match order (order được khớp)
     * @param focusedOrderDirection hướng của focused order
     */
    public void setOrderIds(String focusedOrderId, String matchOrderId, SpotOrderDirection focusedOrderDirection) {
        if (focusedOrderDirection == SpotOrderDirection.BUY) {
            // focusedOrder là BUY -> buyOrderId = focusedOrder, sellOrderId = matchOrder
            this.buyOrderId = focusedOrderId;
            this.sellOrderId = matchOrderId;
        } else {
            // focusedOrder là SELL -> buyOrderId = matchOrder, sellOrderId = focusedOrder
            this.buyOrderId = matchOrderId;
            this.sellOrderId = focusedOrderId;
        }
    }
}
