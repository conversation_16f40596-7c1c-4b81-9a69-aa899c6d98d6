package com.icetea.lotus.matching.infrastructure.messaging.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Mark Price Message DTO
 * Extracted từ FutureCoreKafkaProducer
 * 
 * <AUTHOR> nguyen
 */
@Data
@Builder
public class MarkPriceMessage {
    private String symbol;
    private BigDecimal price;
    private BigDecimal volume;
    private LocalDateTime timestamp;
}
