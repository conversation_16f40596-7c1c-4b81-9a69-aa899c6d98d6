package com.icetea.lotus.matching.infrastructure.sharding;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.matching.infrastructure.sharding.model.PodLoadInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Random;

import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.AMOUNT;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.DIRECTION;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.MEMBER_ID;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.ORDERID;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.PRICE;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.QUANTITY;

/**
 * Intelligent Order Router với partition-based load balancing
 * Thay thế logic sharding cũ bằng cơ chế thông minh hơn
 * Migrated từ future-core
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class IntelligentOrderRouter {

    public static final String USER_ID = "userId";
    public static final String ORDER_TYPE = "orderType";
    private final PartitionBasedLoadBalancer loadBalancer;
    private final PodLoadMonitor podLoadMonitor;
    private final SmartShardingManager smartShardingManager;
    private final RedissonClient redissonClient;
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ObjectMapper objectMapper;

    // Constants
    private static final BigDecimal LARGE_ORDER_THRESHOLD = new BigDecimal("10000");
    private static final String ORDER_ROUTING_TOPIC = "matching-engine-order-routing";
    @SuppressWarnings("java:S2245")
    private static final Random random = new Random();

    /**
     * Route order đến partition/pod phù hợp
     */
    public OrderRoutingResult routeOrder(Object order, String symbol) {
        try {
            // Xác định routing strategy
            RoutingStrategy strategy = determineRoutingStrategy(order);

            return switch (strategy) {
                case DIRECT_PROCESSING -> processDirectly(order, symbol);
                case PARTITION_BASED -> routeToPartition(order, symbol);
                case LOAD_BALANCED -> routeWithLoadBalancing(order, symbol);
                case SEQUENTIAL -> routeSequentially(order, symbol);
            };

        } catch (Exception e) {
            log.error("Error routing order for symbol {}", symbol, e);
            return OrderRoutingResult.error("Routing failed: " + e.getMessage());
        }
    }

    /**
     * Xác định routing strategy
     */
    private RoutingStrategy determineRoutingStrategy(Object order) {
        // Kiểm tra load của pod hiện tại
        PodLoadInfo currentPodLoad = podLoadMonitor.getPodLoadInfo(smartShardingManager.getCurrentPodName());

        if (currentPodLoad != null && currentPodLoad.getOverallLoad() < 0.5) {
            return RoutingStrategy.DIRECT_PROCESSING;
        }

        // Kiểm tra xem có phải large order không
        if (isLargeOrder(order)) {
            return RoutingStrategy.LOAD_BALANCED;
        }

        // Kiểm tra xem có phải market order không
        if (isMarketOrder(order)) {
            return RoutingStrategy.SEQUENTIAL;
        }

        // Default: partition-based
        return RoutingStrategy.PARTITION_BASED;
    }

    /**
     * Xử lý trực tiếp
     */
    private OrderRoutingResult processDirectly(Object order, String symbol) {
        if (smartShardingManager.canProcessSymbol(symbol)) {
            log.info("Processing order directly for symbol {}", symbol);
            return OrderRoutingResult.success("DIRECT", smartShardingManager.getCurrentPodName());
        } else {
            // Try to assign symbol to current pod
            if (smartShardingManager.assignSymbolToCurrentPod(symbol)) {
                return OrderRoutingResult.success("DIRECT_ASSIGNED", smartShardingManager.getCurrentPodName());
            } else {
                return routeToCorrectPod(order, symbol);
            }
        }
    }

    /**
     * Route đến partition
     */
    private OrderRoutingResult routeToPartition(Object order, String symbol) {
        try {
            String partitionId = calculatePartition(order, symbol);
            String assignedPod = loadBalancer.getPartitionOwner(partitionId);

            if (assignedPod == null) {
                // Assign partitions if not exists
                loadBalancer.assignPartitionsToPods(symbol);
                assignedPod = loadBalancer.getPartitionOwner(partitionId);
            }

            if (smartShardingManager.getCurrentPodName().equals(assignedPod)) {
                log.info("Processing order in assigned partition {} for symbol {}", partitionId, symbol);
                return OrderRoutingResult.success("PARTITION", assignedPod);
            } else {
                // Route to correct pod
                return routeToSpecificPod(order, symbol, assignedPod);
            }

        } catch (Exception e) {
            log.error("Error routing to partition for symbol {}", symbol, e);
            return routeToCorrectPod(order, symbol);
        }
    }

    /**
     * Route với load balancing
     */
    private OrderRoutingResult routeWithLoadBalancing(Object order, String symbol) {
        try {
            // Tìm pod có load thấp nhất
            String bestPod = findBestPodForLargeOrder();

            if (smartShardingManager.getCurrentPodName().equals(bestPod)) {
                log.info("Processing large order locally for symbol {}", symbol);
                return OrderRoutingResult.success("LOAD_BALANCED_LOCAL", bestPod);
            } else {
                return routeToSpecificPod(order, symbol, bestPod);
            }

        } catch (Exception e) {
            log.error("Error load balancing order for symbol {}", symbol, e);
            return processDirectly(order, symbol);
        }
    }

    /**
     * Route tuần tự
     */
    private OrderRoutingResult routeSequentially(Object order, String symbol) {
        String primaryPod = smartShardingManager.getPrimaryPodForSymbol(symbol);

        if (primaryPod == null) {
            // Assign primary pod
            if (smartShardingManager.assignSymbolToCurrentPod(symbol)) {
                primaryPod = smartShardingManager.getCurrentPodName();
            } else {
                return OrderRoutingResult.error("Cannot assign primary pod for symbol " + symbol);
            }
        }

        if (smartShardingManager.getCurrentPodName().equals(primaryPod)) {
            log.info("Processing market order sequentially for symbol {}", symbol);
            return OrderRoutingResult.success("SEQUENTIAL", primaryPod);
        } else {
            return routeToSpecificPod(order, symbol, primaryPod);
        }
    }

    /**
     * Route đến pod cụ thể
     */
    private OrderRoutingResult routeToSpecificPod(Object order, String symbol, String targetPod) {
        try {
            // Publish order to Kafka for routing
            String orderJson = serializeOrder(order);
            kafkaTemplate.send(ORDER_ROUTING_TOPIC, symbol, orderJson);

            log.info("Routed order for symbol {} to pod {}", symbol, targetPod);
            return OrderRoutingResult.success("ROUTED", targetPod);

        } catch (Exception e) {
            log.error("Error routing order to pod {} for symbol {}", targetPod, symbol, e);
            return OrderRoutingResult.error("Failed to route to pod " + targetPod);
        }
    }

    /**
     * Route đến pod đúng
     */
    private OrderRoutingResult routeToCorrectPod(Object order, String symbol) {
        String primaryPod = smartShardingManager.getPrimaryPodForSymbol(symbol);

        if (primaryPod != null) {
            return routeToSpecificPod(order, symbol, primaryPod);
        } else {
            return OrderRoutingResult.error("No pod assigned for symbol " + symbol);
        }
    }

    /**
     * Tính toán partition cho order
     */
    private String calculatePartition(Object order, String symbol) {
        List<String> partitions = loadBalancer.getAllPartitions(symbol);

        if (partitions.isEmpty()) {
            return symbol + ":default";
        }

        try {
            // Extract order properties for intelligent partitioning
            OrderProperties props = extractOrderProperties(order);

            // Strategy 1: Large orders get dedicated partitions
            if (props.isLargeOrder()) {
                String largeOrderPartition = findLeastLoadedPartition(partitions);
                if (largeOrderPartition != null) {
                    log.info("Routing large order to dedicated partition: {}", largeOrderPartition);
                    return largeOrderPartition;
                }
            }

            // Strategy 2: Market orders get high-priority partitions
            if (props.isMarketOrder()) {
                String marketOrderPartition = findFastestPartition(partitions);
                if (marketOrderPartition != null) {
                    log.info("Routing market order to fastest partition: {}", marketOrderPartition);
                    return marketOrderPartition;
                }
            }

            // Strategy 3: Price-based partitioning for limit orders
            if (props.price != null) {
                String pricePartition = calculatePriceBasedPartition(props.price, partitions);
                if (pricePartition != null) {
                    log.info("Routing order to price-based partition: {}", pricePartition);
                    return pricePartition;
                }
            }

            // Strategy 4: Member-based partitioning for consistency
            if (props.memberId != null) {
                int memberHash = props.memberId.hashCode();
                int partitionIndex = Math.abs(memberHash) % partitions.size();
                String memberPartition = partitions.get(partitionIndex);
                log.info("Routing order to member-based partition: {}", memberPartition);
                return memberPartition;
            }

            // Fallback: Simple hash-based partitioning
            int hash = order.hashCode();
            int index = Math.abs(hash) % partitions.size();
            return partitions.get(index);

        } catch (Exception e) {
            log.error("Error calculating partition for order, using fallback", e);
            // Fallback to simple hash
            int hash = order.hashCode();
            int index = Math.abs(hash) % partitions.size();
            return partitions.get(index);
        }
    }

    /**
     * Tìm pod tốt nhất cho large order
     */
    private String findBestPodForLargeOrder() {
        return loadBalancer.getActivePods().stream()
                .min((p1, p2) -> {
                    PodLoadInfo load1 = podLoadMonitor.getPodLoadInfo(p1);
                    PodLoadInfo load2 = podLoadMonitor.getPodLoadInfo(p2);

                    if (load1 == null) return 1;
                    if (load2 == null) return -1;

                    return Double.compare(load1.getOverallLoad(), load2.getOverallLoad());
                })
                .orElse(smartShardingManager.getCurrentPodName());
    }

    /**
     * Kiểm tra xem có phải large order không
     */
    private boolean isLargeOrder(Object order) {
        try {
            OrderProperties props = extractOrderProperties(order);
            return props.isLargeOrder();
        } catch (Exception e) {
            log.warn("Error checking if order is large order", e);
            return false;
        }
    }

    /**
     * Kiểm tra xem có phải market order không
     */
    private boolean isMarketOrder(Object order) {
        try {
            OrderProperties props = extractOrderProperties(order);
            return props.isMarketOrder();
        } catch (Exception e) {
            log.warn("Error checking if order is market order", e);
            return false;
        }
    }

    /**
     * Serialize order to JSON
     */
    private String serializeOrder(Object order) {
        try {
            if (order == null) {
                return "{}";
            }

            // Use ObjectMapper for proper JSON serialization
            return objectMapper.writeValueAsString(order);

        } catch (Exception e) {
            log.warn("Error serializing order to JSON, using toString fallback", e);
            return order.toString();
        }
    }

    // ===== HELPER METHODS FOR ORDER PROPERTY EXTRACTION =====

    /**
     * Extract order properties from various order formats
     */
    private OrderProperties extractOrderProperties(Object order) {
        if (order == null) {
            return new OrderProperties();
        }

        try {
            // Handle Map-based order data
            if (order instanceof Map<?, ?> rawOrderMap) {
                // Safe cast with type checking
                Map<String, Object> orderMap = new java.util.HashMap<>();
                for (Map.Entry<?, ?> entry : rawOrderMap.entrySet()) {
                    if (entry.getKey() instanceof String) {
                        orderMap.put((String) entry.getKey(), entry.getValue());
                    }
                }
                return extractFromMap(orderMap);
            }

            // Handle domain Order objects
            if (order.getClass().getSimpleName().contains("Order")) {
                return extractFromOrderObject(order);
            }

            // Handle JSON string
            if (order instanceof String string) {
                @SuppressWarnings("unchecked")
                Map<String, Object> orderMap = objectMapper.readValue(string, Map.class);
                return extractFromMap(orderMap);
            }

            // Fallback: use reflection
            return extractUsingReflection(order);

        } catch (Exception e) {
            log.warn("Error extracting order properties", e);
            return new OrderProperties();
        }
    }

    /**
     * Extract properties from Map
     */
    private OrderProperties extractFromMap(Map<String, Object> orderMap) {
        OrderProperties props = new OrderProperties();

        // Extract basic properties
        props.orderId = getString(orderMap, ORDERID, "id", "orderNumber");
        props.memberId = getString(orderMap, MEMBER_ID, USER_ID, "customerId");
        props.price = getBigDecimal(orderMap, PRICE, "limitPrice");
        props.quantity = getBigDecimal(orderMap, QUANTITY, AMOUNT, "size");
        props.orderType = getString(orderMap, ORDER_TYPE, "type");
        props.direction = getString(orderMap, DIRECTION, "side");

        return props;
    }

    /**
     * Extract properties from Order object using reflection
     */
    private OrderProperties extractFromOrderObject(Object order) {
        OrderProperties props = new OrderProperties();
        Class<?> orderClass = order.getClass();

        try {
            props.orderId = getFieldValueAsString(order, orderClass, ORDERID, "id");
            props.memberId = getFieldValueAsString(order, orderClass, MEMBER_ID, USER_ID);
            props.price = getFieldValueAsBigDecimal(order, orderClass, PRICE);
            props.quantity = getFieldValueAsBigDecimal(order, orderClass, QUANTITY, AMOUNT);
            props.orderType = getFieldValueAsString(order, orderClass, "type", ORDER_TYPE);
            props.direction = getFieldValueAsString(order, orderClass, DIRECTION, "side");
        } catch (Exception e) {
            log.warn("Error extracting from order object", e);
        }

        return props;
    }

    /**
     * Extract using reflection for unknown types
     */
    private OrderProperties extractUsingReflection(Object order) {
        OrderProperties props = new OrderProperties();
        Class<?> clazz = order.getClass();

        // Try common field names
        props.orderId = getFieldValueAsString(order, clazz, ORDERID, "id", "orderNumber");
        props.memberId = getFieldValueAsString(order, clazz, MEMBER_ID, USER_ID, "customerId");
        props.price = getFieldValueAsBigDecimal(order, clazz, PRICE, "limitPrice");
        props.quantity = getFieldValueAsBigDecimal(order, clazz, QUANTITY, AMOUNT, "size");
        props.orderType = getFieldValueAsString(order, clazz, ORDER_TYPE, "type");
        props.direction = getFieldValueAsString(order, clazz, DIRECTION, "side");

        return props;
    }

    // ===== ADDITIONAL HELPER METHODS =====

    /**
     * Find least loaded partition for specific order type
     */
    private String findLeastLoadedPartition(List<String> partitions) {
        return partitions.stream()
                .min((p1, p2) -> {
                    // Compare partition loads (simplified)
                    double load1 = getPartitionLoad();
                    double load2 = getPartitionLoad();
                    return Double.compare(load1, load2);
                })
                .orElse(partitions.get(0));
    }

    /**
     * Find fastest partition for market orders
     */
    private String findFastestPartition(List<String> partitions) {
        return partitions.stream()
                .min((p1, p2) -> {
                    // Compare partition response times (simplified)
                    double latency1 = getPartitionLatency();
                    double latency2 = getPartitionLatency();
                    return Double.compare(latency1, latency2);
                })
                .orElse(partitions.get(0));
    }

    /**
     * Calculate price-based partition
     */
    private String calculatePriceBasedPartition(java.math.BigDecimal price, List<String> partitions) {
        // Simple price range partitioning
        int priceHash = price.hashCode();
        int index = Math.abs(priceHash) % partitions.size();
        return partitions.get(index);
    }

    /**
     * Get partition load (simplified)
     */
    private double getPartitionLoad() {
        // In real implementation, this would query actual partition metrics

        return random.nextInt(); // Placeholder
    }

    /**
     * Get partition latency (simplified)
     */
    private double getPartitionLatency() {

        return random.nextInt(); // Placeholder
    }

    /**
     * Helper methods for data extraction
     */
    private String getString(Map<String, Object> map, String... keys) {
        for (String key : keys) {
            Object value = map.get(key);
            if (value != null) {
                return String.valueOf(value);
            }
        }
        return null;
    }

    private BigDecimal getBigDecimal(Map<String, Object> map, String... keys) {
        for (String key : keys) {
            Object value = map.get(key);
            if (value != null) {
                try {
                    if (value instanceof BigDecimal bigDecimal) {
                        return bigDecimal;
                    }
                    return new BigDecimal(String.valueOf(value));
                } catch (NumberFormatException e) {
                    log.warn("Invalid decimal value for key {}: {}", key, value);
                }
            }
        }
        return null;
    }

    private String getFieldValueAsString(Object obj, Class<?> clazz, String... fieldNames) {
        for (String fieldName : fieldNames) {
            try {
                java.lang.reflect.Field field = clazz.getDeclaredField(fieldName);
                Object value = field.get(obj);
                if (value != null) {
                    return String.valueOf(value);
                }
            } catch (Exception e) {
                // Try next field name
            }
        }
        return null;
    }

    private BigDecimal getFieldValueAsBigDecimal(Object obj, Class<?> clazz, String... fieldNames) {
        String value = getFieldValueAsString(obj, clazz, fieldNames);
        if (value != null) {
            try {
                return new BigDecimal(value);
            } catch (NumberFormatException e) {
                log.warn("Invalid decimal value: {}", value);
            }
        }
        return null;
    }

    /**
     * Order properties container
     */
    private static class OrderProperties {
        String orderId;
        String memberId;
        BigDecimal price;
        BigDecimal quantity;
        String orderType;
        String direction;

        // Large order threshold: 100,000 USDT equivalent
        private static final BigDecimal LARGE_ORDER_THRESHOLD = new BigDecimal("100000");

        boolean isLargeOrder() {
            if (price != null && quantity != null) {
                BigDecimal orderValue = price.multiply(quantity);
                return orderValue.compareTo(LARGE_ORDER_THRESHOLD) > 0;
            }
            return false;
        }

        boolean isMarketOrder() {
            if (orderType == null) return false;
            String upperType = orderType.toUpperCase();
            return "MARKET".equals(upperType) || "1".equals(upperType);
        }
    }

    /**
     * Routing strategies
     */
    public enum RoutingStrategy {
        DIRECT_PROCESSING,  // Xử lý trực tiếp (load thấp)
        PARTITION_BASED,    // Route theo partition (load trung bình)
        LOAD_BALANCED,      // Phân tán tải (orders lớn)
        SEQUENTIAL          // Xử lý tuần tự (market orders)
    }

    /**
     * Order routing result
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class OrderRoutingResult {
        private boolean success;
        private String strategy;
        private String targetPod;
        private String message;

        public static OrderRoutingResult success(String strategy, String targetPod) {
            return OrderRoutingResult.builder()
                    .success(true)
                    .strategy(strategy)
                    .targetPod(targetPod)
                    .message("Success")
                    .build();
        }

        public static OrderRoutingResult error(String message) {
            return OrderRoutingResult.builder()
                    .success(false)
                    .message(message)
                    .build();
        }
    }
}
