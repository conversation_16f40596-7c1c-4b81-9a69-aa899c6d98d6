package com.icetea.lotus.matching.infrastructure.integration;

import com.icetea.lotus.matching.domain.entity.ExchangeOrder;
import com.icetea.lotus.matching.domain.entity.Order;
import com.icetea.lotus.matching.domain.enums.OrderType;
import com.icetea.lotus.matching.domain.enums.SpotOrderStatus;
import com.icetea.lotus.matching.domain.enums.SpotOrderType;
import com.icetea.lotus.matching.infrastructure.constants.CommandTypeConstance;
import com.icetea.lotus.matching.infrastructure.constants.OrderEventType;
import com.icetea.lotus.matching.infrastructure.exchange.ExchangeCompatibilityService;
import com.icetea.lotus.matching.infrastructure.exchange.ExchangeTradeResult;
import com.icetea.lotus.matching.infrastructure.futurecore.FutureCoreCompatibilityService;
import com.icetea.lotus.matching.infrastructure.futurecore.FutureCoreTradeResult;
import com.icetea.lotus.matching.infrastructure.messaging.dto.FutureTradePlateMessage;
import com.icetea.lotus.matching.infrastructure.messaging.producer.ExchangeKafkaProducer;
import com.icetea.lotus.matching.infrastructure.messaging.producer.FutureCoreKafkaProducer;
import com.icetea.lotus.matching.infrastructure.sharding.SymbolShardingManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Matching Engine Integration Service
 * Orchestrates matching engine operations across spot and futures
 * Handles symbol sharding and routing
 *
 * <AUTHOR> nguyen
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MatchingEngineIntegrationService {

    private final FutureCoreCompatibilityService futureCoreService;
    private final ExchangeCompatibilityService exchangeService;
    private final ExchangeKafkaProducer exchangeKafkaProducer;
    private final FutureCoreKafkaProducer futureCoreKafkaProducer;
    private final SymbolShardingManager shardingManager;
    private final FutureCoreCompatibilityService futureCoreCompatibilityService;

    // ========== ORDER BOOK RECOVERY METHODS - Copy từ Exchange CoinTraderEvent pattern ==========

    /**
     * Restore order book for symbol from database - Smart restore strategy
     * Attempts to restore from both SPOT and FUTURE snapshots since same symbol can exist in both markets
     */
    public boolean restoreOrderBookForSymbol(String symbol) {
        log.info("Restoring order book for symbol: {} using smart restore strategy", symbol);

        try {
            // Check if symbol is owned by this pod
            if (!shardingManager.isSymbolOwnedByThisPod(symbol)) {
                log.info("Symbol {} not owned by this pod, skipping restore", symbol);
                return false;
            }

            // Try to restore SPOT order book
            log.info("Attempting to restore SPOT order book for symbol: {}", symbol);
            boolean spotRestored = exchangeService.restoreOrderBookFromDatabase(symbol);
            if (spotRestored) {
                log.info("Successfully restored SPOT order book for symbol: {}", symbol);
            }

            // Try to restore FUTURE order book
            log.info("Attempting to restore FUTURE order book for symbol: {}", symbol);
            boolean futureRestored = futureCoreService.restoreOrderBookFromDatabase(symbol);
            if (futureRestored) {
                log.info("Successfully restored FUTURE order book for symbol: {}", symbol);
            }

            // Log overall result
            if (spotRestored && futureRestored) {
                log.info("Successfully restored BOTH SPOT and FUTURE order books for symbol: {}", symbol);
            } else if (spotRestored) {
                log.info("Successfully restored SPOT order book for symbol: {} (FUTURE not available)", symbol);
            } else if (futureRestored) {
                log.info("Successfully restored FUTURE order book for symbol: {} (SPOT not available)", symbol);
            } else {
                log.warn("Could not restore any order book for symbol: {}", symbol);
            }

            return spotRestored || futureRestored;

        } catch (Exception e) {
            log.error("Error restoring order book for symbol: {}", symbol, e);
            return false;
        }
    }


    /**
     * Initialize matching engine for symbol - Smart initialization strategy
     * Attempts to initialize both SPOT and FUTURE engines since same symbol can exist in both markets
     */
    public void initializeMatchingEngine(String symbol) {
        log.info("Initializing matching engine for symbol: {} using smart initialization strategy", symbol);

        try {
            // Check if symbol is owned by this pod
            if (!shardingManager.isSymbolOwnedByThisPod(symbol)) {
                log.warn("Symbol {} not owned by this pod, skipping initialization", symbol);
                return;
            }

            // Try to initialize SPOT matching engine
            log.info("Attempting to initialize SPOT matching engine for symbol: {}", symbol);
            exchangeService.initializeMatchingEngine(symbol);
            log.info("Successfully initialized SPOT matching engine for symbol: {}", symbol);

            // Try to initialize FUTURE matching engine
            log.info("Attempting to initialize FUTURE matching engine for symbol: {}", symbol);
            futureCoreService.initializeMatchingEngine(symbol);
            log.info("Successfully initialized FUTURE matching engine for symbol: {}", symbol);

        } catch (Exception e) {
            log.error("Error initializing matching engines for symbol: {}", symbol, e);
        }
    }

    /**
     * Get order book for symbol - Smart retrieval strategy
     * Returns the first available order book (tries SPOT first, then FUTURE)
     */
    public Object getOrderBook(String symbol) {
        log.info("Getting order book for symbol: {} using smart retrieval strategy", symbol);

        try {
            // Check if symbol is owned by this pod
            if (!shardingManager.isSymbolOwnedByThisPod(symbol)) {
                log.warn("Symbol {} not owned by this pod", symbol);
                return null;
            }

            // Try to get SPOT order book first
            Object spotOrderBook = exchangeService.getOrderBook(symbol);
            if (spotOrderBook != null) {
                log.info("Retrieved SPOT order book for symbol: {}", symbol);
                return spotOrderBook;
            }

            // Try to get FUTURE order book as fallback
            Object futureOrderBook = futureCoreService.getOrderBook(symbol);
            if (futureOrderBook != null) {
                log.info("Retrieved FUTURE order book for symbol: {}", symbol);
                return futureOrderBook;
            }

            log.warn("No order book available for symbol: {}", symbol);
            return null;

        } catch (Exception e) {
            log.error("Error getting order book for symbol: {}", symbol, e);
            return null;
        }
    }

    /**
     * Get order books for symbol from BOTH spot and future markets
     * Returns a map with "SPOT" and "FUTURE" keys
     */
    public java.util.Map<String, Object> getAllOrderBooks(String symbol) {
        log.info("Getting all order books for symbol: {} from both markets", symbol);

        java.util.Map<String, Object> result = new java.util.HashMap<>();

        try {
            // Check if symbol is owned by this pod
            if (!shardingManager.isSymbolOwnedByThisPod(symbol)) {
                log.warn("Symbol {} not owned by this pod", symbol);
                return result;
            }

            // Get SPOT order book
            Object spotOrderBook = exchangeService.getOrderBook(symbol);
            result.put("SPOT", spotOrderBook);
            log.info("Retrieved SPOT order book for symbol: {}", symbol);

            // Get FUTURE order book
            Object futureOrderBook = futureCoreService.getOrderBook(symbol);
            result.put("FUTURE", futureOrderBook);
            log.info("Retrieved FUTURE order book for symbol: {}", symbol);

            log.info("Retrieved order books for symbol: {} from both markets", symbol);
            return result;

        } catch (Exception e) {
            log.error("Error getting order books for symbol: {} from both markets", symbol, e);
            return result;
        }
    }

    // ========== STOP ORDER INTEGRATION METHODS ==========

    /**
     * Submit triggered stop order back to matching engine
     *
     * @param triggeredOrder The triggered stop order as ExchangeOrder
     * @return ExchangeTradeResult from processing
     */
    public ExchangeTradeResult submitExchangeTriggeredStopOrder(ExchangeOrder triggeredOrder) {
        try {
            log.info("Submitting triggered stop order: {} for symbol: {}, type: {}, direction: {}, price: {}, amount: {}",
                    triggeredOrder.getOrderId(), triggeredOrder.getSymbol(),
                    triggeredOrder.getType(), triggeredOrder.getDirection(),
                    triggeredOrder.getPrice(), triggeredOrder.getAmount());

            // Validate triggered order
            if (triggeredOrder == null || !triggeredOrder.isValid()) {
                log.warn("Invalid triggered stop order: {}", triggeredOrder);
                return ExchangeTradeResult.error("Invalid triggered stop order");
            }

            String symbol = triggeredOrder.getSymbol();

            // Check if symbol is owned by this pod
            if (!shardingManager.isSymbolOwnedByThisPod(symbol)) {
                log.warn("Symbol {} not owned by this pod, cannot submit triggered stop order", symbol);
                return ExchangeTradeResult.error("Symbol not owned by this pod: " + symbol);
            }

            //   CRITICAL FIX: Submit through full processing pipeline to ensure Kafka messages are sent
            ExchangeTradeResult result = processExchangeTriggeredOrderWithMessaging(triggeredOrder);

            if (result.isSuccess()) {
                log.info("  Successfully submitted triggered stop order: {} for symbol: {}, trades: {}, completed: {}, partial: {}",
                        triggeredOrder.getOrderId(), symbol, result.getTradesCount(),
                        result.getCompletedOrders().size(), result.getPartiallyFilledOrders().size());
            } else {
                log.error("  Failed to submit triggered stop order: {} for symbol: {}, error: {}",
                        triggeredOrder.getOrderId(), symbol, result.getMessage());
            }

            return result;

        } catch (Exception e) {
            log.error("Error submitting triggered stop order: {} - {}",
                    triggeredOrder.getOrderId(), e.getMessage(), e);
            return ExchangeTradeResult.error("Error submitting triggered stop order: " + e.getMessage());
        }
    }

    /**
     * Submit triggered stop order back to matching engine
     *
     * @param triggeredOrder The triggered stop order as Order
     */
    public void submitFutureTriggeredStopOrder(Order triggeredOrder) {
        try {
            log.info("Submitting triggered stop order: {} for symbol: {}, type: {}, direction: {}, price: {}, amount: {}",
                    triggeredOrder.getOrderId(), triggeredOrder.getSymbol(),
                    triggeredOrder.getType(), triggeredOrder.getDirection(),
                    triggeredOrder.getPrice(), triggeredOrder.getSize());

            String symbol = triggeredOrder.getSymbol().getValue();

            // Check if symbol is owned by this pod
            if (!shardingManager.isSymbolOwnedByThisPod(symbol)) {
                log.info("Symbol {} not owned by this pod, cannot submit triggered stop order", symbol);
                FutureCoreTradeResult.error("Symbol not owned by this pod: " + symbol);
                return;
            }
            // Submit through full processing pipeline to ensure Kafka messages are sent
            processFutureTriggeredOrderWithMessaging(triggeredOrder, symbol);

        } catch (Exception e) {
            log.error("Error submitting triggered stop order: {} - {}",
                    triggeredOrder.getOrderId(), e.getMessage(), e);
            FutureCoreTradeResult.error("Error submitting triggered stop order: " + e.getMessage());
        }
    }

    /**
     * Process triggered order with full messaging pipeline
     * This ensures Kafka messages are sent after matching (like ExchangeOrderConsumer does)
     *
     * @param triggeredOrder The triggered stop order
     * @return ExchangeTradeResult from processing
     */
    private ExchangeTradeResult processExchangeTriggeredOrderWithMessaging(ExchangeOrder triggeredOrder) {
        try {
            // Process through Exchange compatibility service
            ExchangeTradeResult result = exchangeService.processExchangeOrderDTO(triggeredOrder);

            //   CRITICAL: Publish Kafka messages like ExchangeOrderConsumer does
            if (result.isSuccess()) {
                String symbol = triggeredOrder.getSymbol();
                // Publish completed orders
                if (!result.getCompletedOrders().isEmpty()) {
                    // Convert to exchange-core format (same logic as ExchangeOrderConsumer)
                    List<Object> exchangeCoreOrders = convertToExchangeCoreOrders(result.getCompletedOrders());
                    exchangeKafkaProducer.publishExchangeOrderCompleted(symbol, exchangeCoreOrders);

                    log.info(" Published {} completed orders for triggered order: {}",
                            exchangeCoreOrders.size(), triggeredOrder.getOrderId());
                }

                if (result.getTrades().isEmpty() && SpotOrderType.LIMIT_PRICE.equals(triggeredOrder.getType())) {
                    exchangeKafkaProducer.publishStopOrderTriggered(symbol, triggeredOrder);
                    log.info(" {} trades published individually for triggered order: {}",
                            result.getTrades().size(), triggeredOrder.getOrderId());
                }

                log.info("  Processed triggered order with full messaging: {} → {} trades, {} completed",
                        triggeredOrder.getOrderId(), result.getTradesCount(), result.getCompletedOrders().size());
            }

            return result;

        } catch (Exception e) {
            log.error("Error processing triggered order with messaging: {} - {}",
                    triggeredOrder.getOrderId(), e.getMessage(), e);
            return ExchangeTradeResult.error("Processing with messaging failed: " + e.getMessage());
        }
    }

    /**
     * Process triggered order with full messaging pipeline
     * This ensures Kafka messages are sent after matching (like ExchangeOrderConsumer does)
     *
     * @param triggeredOrder The triggered stop order
     */
    private void processFutureTriggeredOrderWithMessaging(Order triggeredOrder, String symbol) {
        try {
            // Process through Exchange compatibility service
            FutureCoreTradeResult tradeResult = futureCoreCompatibilityService.processContractOrderInternal(triggeredOrder, CommandTypeConstance.PLACE_ORDER);

            if (tradeResult.isSuccess()) {
                if (tradeResult.getTradePlate() != null) {
                    FutureTradePlateMessage message = FutureTradePlateMessage.createTradePlateMessage(symbol, tradeResult.getTradePlate());
                    futureCoreKafkaProducer.publishContractTradePlate(symbol, message);
                }
                // Publish OUTPUT: completed orders if any
                if (tradeResult.getCompletedOrder() != null) {
                    futureCoreKafkaProducer.publishContractOrderCompleted(symbol, tradeResult.getCompletedOrder());
                }
                // PERFORMANCE OPTIMIZATION: Publish trades in batch instead of individually
                if (tradeResult.getTrades() != null && !tradeResult.getTrades().isEmpty()) {
                    // Publish OUTPUT: order placed event
                    futureCoreKafkaProducer.publishOrderPlacedEvent(tradeResult.getTrades(), OrderEventType.ORDER_PLACED, symbol, tradeResult.getOrderId());
                    futureCoreKafkaProducer.publishContractTradeBatch(tradeResult.getTrades(), tradeResult.getSymbol());
                } else if (OrderType.LIMIT.equals(triggeredOrder.getType()) ||  OrderType.STOP_LIMIT.equals(triggeredOrder.getType())) {
                    futureCoreKafkaProducer.publishStopOrderTriggered(symbol, triggeredOrder);
                }
                log.info("Successfully processed PLACE_ORDER command for symbol: {} with {} trades",
                        symbol, tradeResult.getTrades() != null ? tradeResult.getTrades().size() : 0);
            } else {
                log.warn("Failed to process PLACE_ORDER command for symbol: {} - {}",
                        symbol, tradeResult.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("Error processing triggered order with messaging: {} - {}",
                    triggeredOrder.getOrderId(), e.getMessage(), e);
            FutureCoreTradeResult.error("Processing with messaging failed: " + e.getMessage());
        }
    }

    /**
     * Submit multiple triggered stop orders in batch
     *
     * @param triggeredOrders List of triggered stop orders
     * @return List of ExchangeTradeResults
     */
    public List<ExchangeTradeResult> submitTriggeredStopOrdersBatch(List<ExchangeOrder> triggeredOrders) {
        log.info("Submitting {} triggered stop orders in batch", triggeredOrders.size());

        return triggeredOrders.stream()
                .map(this::submitExchangeTriggeredStopOrder)
                .toList();
    }

    /**
     * Submit triggered stop order asynchronously
     *
     * @param triggeredOrder The triggered stop order
     * @return CompletableFuture with result
     */
    public CompletableFuture<ExchangeTradeResult> submitTriggeredStopOrderAsync(ExchangeOrder triggeredOrder) {
        return CompletableFuture.supplyAsync(() -> submitExchangeTriggeredStopOrder(triggeredOrder));
    }

    /**
     * Convert matching engine ExchangeOrder to exchange-core format
     * Copy from ExchangeOrderConsumer to ensure consistency
     */
    private List<Object> convertToExchangeCoreOrders(List<Object> completedOrders) {
        List<Object> exchangeCoreOrders = new ArrayList<>();

        for (Object orderObj : completedOrders) {
            if (orderObj instanceof ExchangeOrder matchingOrder) {

                // Create exchange-core compatible order using Map to avoid enum conflicts
                Map<String, Object> exchangeCoreOrder = new HashMap<>();
                exchangeCoreOrder.put("orderId", matchingOrder.getOrderId());
                exchangeCoreOrder.put("memberId", matchingOrder.getMemberId());
                exchangeCoreOrder.put("symbol", matchingOrder.getSymbol());
                exchangeCoreOrder.put("amount", matchingOrder.getAmount());
                exchangeCoreOrder.put("tradedAmount", matchingOrder.getTradedAmount());
                exchangeCoreOrder.put("turnover", matchingOrder.getTurnover());
                exchangeCoreOrder.put("price", matchingOrder.getPrice());
                exchangeCoreOrder.put("coinSymbol", matchingOrder.getCoinSymbol());
                exchangeCoreOrder.put("baseSymbol", matchingOrder.getBaseSymbol());
                exchangeCoreOrder.put("time", matchingOrder.getTime());
                exchangeCoreOrder.put("completedTime", matchingOrder.getCompletedTime());
                exchangeCoreOrder.put("canceledTime", matchingOrder.getCanceledTime());
                exchangeCoreOrder.put("useDiscount", matchingOrder.getUseDiscount());
                exchangeCoreOrder.put("stopPrice", matchingOrder.getStopPrice());
                exchangeCoreOrder.put("status", SpotOrderStatus.TRADING);
                //   Add triggered and triggerTime fields for stop orders
                exchangeCoreOrder.put("triggered", matchingOrder.getTriggered());
                exchangeCoreOrder.put("triggerTime", matchingOrder.getTriggerTime());

                // Convert enums to String values that exchange-core can understand
                convertEnumsToStringValue(matchingOrder, exchangeCoreOrder);

                exchangeCoreOrders.add(exchangeCoreOrder);
            } else {
                // If already in correct format, keep as is
                exchangeCoreOrders.add(orderObj);
            }
        }

        return exchangeCoreOrders;
    }

    private static void convertEnumsToStringValue(ExchangeOrder matchingOrder, Map<String, Object> exchangeCoreOrder) {
        if (matchingOrder.getType() != null) {
            exchangeCoreOrder.put("type", matchingOrder.getType().getExchangeCode());
        }
        if (matchingOrder.getDirection() != null) {
            exchangeCoreOrder.put("direction", matchingOrder.getDirection().getExchangeCode());
        }
        if (matchingOrder.getStatus() != null) {
            exchangeCoreOrder.put("status", matchingOrder.getStatus().getExchangeCode());
        }
        if (matchingOrder.getSelfTradePreventionMode() != null) {
            exchangeCoreOrder.put("selfTradePreventionMode", matchingOrder.getSelfTradePreventionMode().name());
        }
    }
}
