package com.icetea.lotus.matching.infrastructure.stp;

/**
 * <AUTHOR> nguyen
 */

/**
 * Self Trade Prevention Mode enum
 * Migrated from future-core
 */
public enum SelfTradePreventionMode {
    
    /**
     * Không ngăn chặn self-trade
     */
    NONE,
    
    /**
     * Cancel taker order (order mới)
     */
    CANCEL_TAKER,
    
    /**
     * Cancel maker order (order trong book)
     */
    CANCEL_MAKER,
    
    /**
     * Cancel cả hai orders
     */
    CANCEL_BOTH,
    
    /**
     * Cancel order mới hơn (based on timestamp)
     */
    CANCEL_NEWEST,
    
    /**
     * Cancel order cũ hơn (based on timestamp)
     */
    CANCEL_OLDEST;
    
    /**
     * Get default STP mode
     * @return Default mode
     */
    public static SelfTradePreventionMode getDefault() {
        return CANCEL_TAKER;
    }
    
    /**
     * Parse STP mode from string
     * @param mode Mode string
     * @return STP mode
     */
    public static SelfTradePreventionMode fromString(String mode) {
        if (mode == null || mode.trim().isEmpty()) {
            return getDefault();
        }
        
        try {
            return valueOf(mode.toUpperCase());
        } catch (IllegalArgumentException e) {
            return getDefault();
        }
    }
}
