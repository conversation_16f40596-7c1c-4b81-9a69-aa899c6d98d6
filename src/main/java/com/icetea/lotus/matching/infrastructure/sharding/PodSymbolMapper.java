package com.icetea.lotus.matching.infrastructure.sharding;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Pattern;

import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.POD_PATTERN;

/**
 * Pod Symbol Mapper
 * Quản lý mapping symbols cho từng pod dựa trên pod name pattern và profile
 * 
 * <AUTHOR> nguyen
 */
@Slf4j
@Component
public class PodSymbolMapper {

    @Value("${matching-engine.pod-name:${HOSTNAME:matching-engine-pod}}")
    private String podName;

    // Predefined symbol groups for different pod patterns
    private static final Map<String, List<String>> POD_SYMBOL_GROUPS = new HashMap<>();
    
    static {
        // Major cryptocurrencies - Pod 1
        POD_SYMBOL_GROUPS.put("pod-1", Arrays.asList(
            "BTC/USDT", "ETH/USDT", "BNB/USDT"
        ));
        
        // Altcoins - Pod 2
        POD_SYMBOL_GROUPS.put("pod-2", Arrays.asList(
            "ADA/USDT", "XRP/USDT", "SOL/USDT", "DOT/USDT"
        ));
        
        // DeFi tokens - Pod 3
        POD_SYMBOL_GROUPS.put("pod-3", Arrays.asList(
            "UNI/USDT", "LINK/USDT", "AAVE/USDT", "COMP/USDT"
        ));
        
        // Meme coins and others - Pod 4
        POD_SYMBOL_GROUPS.put("pod-4", Arrays.asList(
            "DOGE/USDT", "SHIB/USDT", "AVAX/USDT", "MATIC/USDT"
        ));
        
        // Additional coins - Pod 5
        POD_SYMBOL_GROUPS.put("pod-5", Arrays.asList(
            "LTC/USDT", "ATOM/USDT", "NEAR/USDT", "FTM/USDT"
        ));
    }

    /**
     * Get symbols for current pod based on pod name pattern
     * @return List of symbols assigned to this pod
     */
    public List<String> getSymbolsForCurrentPod() {
        return getSymbolsForPod(podName);
    }

    /**
     * Get symbols for specific pod based on pod name pattern
     * @param podName Pod name
     * @return List of symbols assigned to the pod
     */
    public List<String> getSymbolsForPod(String podName) {
        if (podName == null || podName.trim().isEmpty()) {
            log.warn("Pod name is null or empty, returning empty symbol list");
            return new ArrayList<>();
        }

        try {
            // Extract pod identifier from pod name
            String podIdentifier = extractPodIdentifier(podName);
            
            // Get symbols for the pod identifier
            List<String> symbols = POD_SYMBOL_GROUPS.get(podIdentifier);
            
            if (symbols != null) {
                log.info("Found {} symbols for pod {}: {}", symbols.size(), podName, symbols);
                return new ArrayList<>(symbols);
            } else {
                log.info("No predefined symbols found for pod {}, using default assignment", podName);
                return getDefaultSymbolsForPod(podName);
            }
            
        } catch (Exception e) {
            log.error("Error getting symbols for pod {}: {}", podName, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Extract pod identifier from pod name
     * Supports patterns like: matching-engine-pod-1, pod-1, me-pod-2, etc.
     */
    private String extractPodIdentifier(String podName) {
        // Pattern to extract pod number: pod-1, pod-2, etc.
        Pattern podPattern = Pattern.compile(POD_PATTERN, Pattern.CASE_INSENSITIVE);
        var matcher = podPattern.matcher(podName);
        
        if (matcher.matches()) {
            String podNumber = matcher.group(1);
            return "pod-" + podNumber;
        }
        
        // If no pattern matches, use hash-based assignment
        return "pod-" + (Math.abs(Integer.parseInt(podName)) % 5 + 1);
    }

    /**
     * Get default symbols for pod using hash-based distribution
     */
    private List<String> getDefaultSymbolsForPod(String podName) {
        // Use hash to distribute symbols evenly
        int podHash = Math.abs(Integer.parseInt(podName)) % 5 + 1;
        String defaultPodKey = "pod-" + podHash;
        
        List<String> symbols = POD_SYMBOL_GROUPS.get(defaultPodKey);
        if (symbols != null) {
            log.info("Using default symbol assignment for pod {}: {} (mapped to {})", 
                    podName, symbols, defaultPodKey);
            return new ArrayList<>(symbols);
        }
        
        // Fallback to first group
        return new ArrayList<>(POD_SYMBOL_GROUPS.get("pod-1"));
    }

    /**
     * Get all available pod identifiers
     * @return Set of pod identifiers
     */
    public Set<String> getAllPodIdentifiers() {
        return new HashSet<>(POD_SYMBOL_GROUPS.keySet());
    }

    /**
     * Get all symbols across all pods
     * @return List of all symbols
     */
    public List<String> getAllSymbols() {
        return POD_SYMBOL_GROUPS.values().stream()
                .flatMap(List::stream)
                .distinct()
                .sorted()
                .toList();
    }

    /**
     * Check if symbol is assigned to specific pod
     * @param symbol Symbol to check
     * @param podName Pod name
     * @return true if symbol is assigned to pod, false otherwise
     */
    public boolean isSymbolAssignedToPod(String symbol, String podName) {
        List<String> podSymbols = getSymbolsForPod(podName);
        return podSymbols.contains(symbol);
    }

    /**
     * Get pod assignment for symbol
     * @param symbol Symbol to check
     * @return Pod identifier that should handle this symbol, or null if not found
     */
    public String getPodForSymbol(String symbol) {
        for (Map.Entry<String, List<String>> entry : POD_SYMBOL_GROUPS.entrySet()) {
            if (entry.getValue().contains(symbol)) {
                return entry.getKey();
            }
        }
        
        // If symbol not found in predefined groups, use hash-based assignment
        int symbolHash = Math.abs(Integer.parseInt(symbol)) % 5 + 1;
        return "pod-" + symbolHash;
    }

    /**
     * Add custom symbol group for pod
     * @param podIdentifier Pod identifier
     * @param symbols List of symbols
     */
    public void addCustomSymbolGroup(String podIdentifier, List<String> symbols) {
        if (podIdentifier != null && symbols != null && !symbols.isEmpty()) {
            POD_SYMBOL_GROUPS.put(podIdentifier, new ArrayList<>(symbols));
            log.info("Added custom symbol group for {}: {}", podIdentifier, symbols);
        }
    }

    /**
     * Get current pod identifier
     * @return Current pod identifier
     */
    public String getCurrentPodIdentifier() {
        return extractPodIdentifier(podName);
    }

    /**
     * Get statistics about symbol distribution
     * @return Map with distribution statistics
     */
    public Map<String, Object> getDistributionStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalPods", POD_SYMBOL_GROUPS.size());
        stats.put("totalSymbols", getAllSymbols().size());
        stats.put("currentPod", podName);
        stats.put("currentPodIdentifier", getCurrentPodIdentifier());
        stats.put("currentPodSymbols", getSymbolsForCurrentPod());
        stats.put("symbolsPerPod", POD_SYMBOL_GROUPS.entrySet().stream()
                .collect(HashMap::new, 
                        (map, entry) -> map.put(entry.getKey(), entry.getValue().size()),
                        HashMap::putAll));
        return stats;
    }
}
