package com.icetea.lotus.matching.infrastructure.messaging.producer;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.icetea.lotus.matching.infrastructure.messaging.dto.CancelOrdersRequest;
import com.icetea.lotus.matching.infrastructure.messaging.dto.ExchangeOrderMessage;
import com.icetea.lotus.matching.infrastructure.messaging.dto.LastPriceMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Exchange Kafka Producer - Copy từ Exchange module
 * Handles publishing spot trading events
 * 
 * <AUTHOR> nguyen
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ExchangeKafkaProducer {

    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ObjectMapper objectMapper = new ObjectMapper()
            .registerModule(new JavaTimeModule())
            .setSerializationInclusion(JsonInclude.Include.NON_NULL);

    // Message executor for async processing
    private final ExecutorService messageExecutor = Executors.newFixedThreadPool(10);

    // Batch processing constants
    private static final int MAX_BATCH_SIZE = 100;

    // Exchange Topics - Copy từ Exchange module configuration
    @Value("${topic-kafka.exchange.order:exchange-order}")
    private String exchangeOrderTopic;

    @Value("${topic-kafka.exchange.order-cancel:exchange-order-cancel}")
    private String exchangeOrderCancelTopic;

    @Value("${topic-kafka.exchange.order-cancel-success:exchange-order-cancel-success}")
    private String exchangeOrderCancelSuccessTopic;

    @Value("${topic-kafka.exchange.order-completed:exchange-order-completed}")
    private String exchangeOrderCompletedTopic;

    @Value("${topic-kafka.exchange.order-cancel-all-completed:exchange-order-cancel-all-completed}")
    private String exchangeOrderCancelAllCompletedTopic;

    @Value("${topic-kafka.exchange.trade:exchange-trade}")
    private String exchangeTradeTopic;

    @Value("${topic-kafka.exchange.trade-plate:exchange-trade-plate}")
    private String exchangeTradePlateTopic;

    @Value("${topic-kafka.exchange.trade-events:exchange-trade-events}")
    private String exchangeTradeEventsTopic;

    @Value("${topic-kafka.exchange.order-events:exchange-order-events}")
    private String exchangeOrderEventsTopic;

    @Value("${topic-kafka.exchange.last-price:exchange-last-price}")
    private String exchangeLastPriceTopic;

    @Value("${topic-kafka.exchange.stop-order-triggered:exchange-stop-order-triggered}")
    private String exchangeStopOrderTriggeredTopic;

    /**
     * Publish exchange order - Copy từ CoinTraderV2 (2 parameters pattern)
     */
    public void publishExchangeOrder(String symbol, Object order) {
        messageExecutor.submit(() -> {
            try {
                String orderJson = objectMapper.writeValueAsString(order);
                kafkaTemplate.send(exchangeOrderTopic, symbol, orderJson);
                log.info("Published exchange order for symbol: {}", symbol);
            } catch (JsonProcessingException e) {
                log.error("Error serializing exchange order to JSON for symbol: {}", symbol, e);
            }
        });
    }

    public void publishExchangeOrderCancel(String symbol, ExchangeOrderMessage cancelRequest) {
        messageExecutor.submit(() -> {
            try {
                String cancelJson = objectMapper.writeValueAsString(cancelRequest);
                kafkaTemplate.send(exchangeOrderCancelTopic, symbol, cancelJson);
                log.info("Published exchange order cancel for symbol: {}", symbol);
            } catch (JsonProcessingException e) {
                log.error("Error serializing exchange order cancel to JSON for symbol: {}", symbol, e);
            }
        });
    }

    /**
     * Publish exchange order cancel success (2 parameters pattern like Exchange module)
     */
    public void publishExchangeOrderCancelSuccess(String symbol, ExchangeOrderMessage cancelSuccess) {
        messageExecutor.submit(() -> {
            try {
                String cancelSuccessJson = objectMapper.writeValueAsString(cancelSuccess);
                kafkaTemplate.send(exchangeOrderCancelSuccessTopic, cancelSuccessJson);
                log.info("Published exchange order cancel success for symbol: {}", symbol);
            } catch (JsonProcessingException e) {
                log.error("Error serializing exchange order cancel success to JSON for symbol: {}", symbol, e);
            }
        });
    }

    public void publishExchangeOrderCancelAllCompleted(CancelOrdersRequest request) {
        messageExecutor.submit(() -> {
            try {
                String cancelSuccessJson = objectMapper.writeValueAsString(request);
                kafkaTemplate.send(exchangeOrderCancelAllCompletedTopic, cancelSuccessJson);
                log.info("Published exchange order cancel success for member: {}", request.getMemberId());
            } catch (JsonProcessingException e) {
                log.error("Error serializing exchange order cancel success to JSON for member: {}", request.getMemberId(), e);
            }
        });
    }

    /**
     * Publish exchange order completed - Copy từ CoinTraderV2
     */
    public void publishExchangeOrderCompleted(String symbol, List<?> orders) {
        if (orders.isEmpty()) {
            return;
        }

        messageExecutor.submit(() -> {
            try {
                if (orders.size() > MAX_BATCH_SIZE) {
                    int size = orders.size();
                    for (int index = 0; index < size; index += MAX_BATCH_SIZE) {
                        int length = Math.min(MAX_BATCH_SIZE, size - index);
                        List<?> subOrders = orders.subList(index, index + length);
                        String ordersJson = objectMapper.writeValueAsString(subOrders);
                        kafkaTemplate.send(exchangeOrderCompletedTopic, ordersJson);
                    }
                } else {
                    String ordersJson = objectMapper.writeValueAsString(orders);
                    kafkaTemplate.send(exchangeOrderCompletedTopic, ordersJson);
                }
                log.info("Published {} exchange order completed for symbol: {}", orders.size(), symbol);
            } catch (JsonProcessingException e) {
                log.error("Error serializing exchange order completed to JSON for symbol: {}", symbol, e);
            }
        });
    }

    /**
     * Publish individual exchange trade - Real-time trade publishing
     * Gửi từng trade ngay khi được khớp để đúng với nghiệp vụ thực tế
     * CRITICAL FIX: Wrap single trade in array to match consumer expectation
     */
    public void publishExchangeTrade(String symbol, Object trade) {
        if (trade == null) {
            return;
        }

        messageExecutor.submit(() -> {
            try {
                // CRITICAL FIX: Consumer expects List<ExchangeTrade>, so wrap single trade in array
                List<Object> tradeArray = Arrays.asList(trade);
                String tradeJson = objectMapper.writeValueAsString(tradeArray);
                kafkaTemplate.send(exchangeTradeTopic, symbol, tradeJson);
                log.info("Published individual exchange trade (wrapped in array) for symbol: {}", symbol);
            } catch (JsonProcessingException e) {
                log.error("Error serializing individual exchange trade to JSON for symbol: {}", symbol, e);
            }
        });
    }

    /**
     * Publish exchange trade batch
     */
    public void publishExchangeTradeBatch(List<?> trades) {
        if (trades == null || trades.isEmpty()) {
            return;
        }

        messageExecutor.submit(() -> {
            try {
                // Extract symbol from first trade for key
                String symbol = extractSymbolFromTrade(trades.get(0));

                if (trades.size() > MAX_BATCH_SIZE) {
                    int size = trades.size();
                    for (int index = 0; index < size; index += MAX_BATCH_SIZE) {
                        int length = Math.min(MAX_BATCH_SIZE, size - index);
                        List<?> subTrades = trades.subList(index, index + length);
                        String tradesJson = objectMapper.writeValueAsString(subTrades);
                        kafkaTemplate.send(exchangeTradeTopic, tradesJson);
                    }
                } else {
                    String tradesJson = objectMapper.writeValueAsString(trades);
                    kafkaTemplate.send(exchangeTradeTopic, tradesJson);
                }
                log.info("Published {} exchange trades for symbol: {}", trades.size(), symbol);
            } catch (JsonProcessingException e) {
                log.error("Error serializing exchange trades to JSON", e);
            }
        });
    }

    /**
     * Publish exchange trade plate (2 parameters pattern)
     */
    public void publishExchangeTradePlate(String symbol, Object tradePlate) {
        messageExecutor.submit(() -> {
            try {
                String tradePlateJson = objectMapper.writeValueAsString(tradePlate);
                kafkaTemplate.send(exchangeTradePlateTopic, tradePlateJson);
                log.info("Published exchange trade plate for symbol: {}", symbol);
            } catch (JsonProcessingException e) {
                log.error("Error serializing exchange trade plate to JSON for symbol: {}", symbol, e);
            }
        });
    }

    /**
     * Publish exchange trade with symbol key
     */
    public void publishExchangeTradeWithSymbol(String symbol, List<?> trades) {
        if (trades.isEmpty()) {
            return;
        }

        messageExecutor.submit(() -> {
            try {
                if (trades.size() > MAX_BATCH_SIZE) {
                    int size = trades.size();
                    for (int index = 0; index < size; index += MAX_BATCH_SIZE) {
                        int length = Math.min(MAX_BATCH_SIZE, size - index);
                        List<?> subTrades = trades.subList(index, index + length);
                        String tradesJson = objectMapper.writeValueAsString(subTrades);
                        kafkaTemplate.send(exchangeTradeTopic, tradesJson);
                    }
                } else {
                    String tradesJson = objectMapper.writeValueAsString(trades);
                    kafkaTemplate.send(exchangeTradeTopic, tradesJson);
                }
                log.info("Published {} exchange trades for symbol: {}", trades.size(), symbol);
            } catch (JsonProcessingException e) {
                log.error("Error serializing exchange trades to JSON for symbol: {}", symbol, e);
            }
        });
    }

    /**
     * Publish stop order triggered event
     *
     * @param symbol Trading symbol
     * @param stopOrderTriggeredEvent Stop order triggered event data
     */
    public void publishStopOrderTriggered(String symbol, Object stopOrderTriggeredEvent) {
        messageExecutor.submit(() -> {
            try {
                String eventJson = objectMapper.writeValueAsString(stopOrderTriggeredEvent);
                kafkaTemplate.send(exchangeStopOrderTriggeredTopic, eventJson);
                log.info("Published stop order triggered event for symbol: {}", symbol);
            } catch (JsonProcessingException e) {
                log.error("Error serializing stop order triggered event to JSON for symbol: {}", symbol, e);
            }
        });
    }

    /**
     * Generic async publish method with event type logging (2 parameters pattern)
     */
    private void publishAsync(String topic, String key, Object message, String eventType) {
        messageExecutor.submit(() -> {
            try {
                String messageJson = objectMapper.writeValueAsString(message);
                kafkaTemplate.send(topic, messageJson);
                log.info("Published {} for key: {} to topic: {}", eventType, key, topic);
            } catch (JsonProcessingException e) {
                log.error("Error serializing {} to JSON for key: {}", eventType, key, e);
            }
        });
    }

    /**
     * Publish trade executed event - Migrated từ TradeEventPublisher.publishTradeExecuted() (2 parameters pattern)
     */
    public void publishTradeExecutedEvent(String symbol, Object tradeEvent) {
        messageExecutor.submit(() -> {
            try {
                String eventJson = objectMapper.writeValueAsString(tradeEvent);
                kafkaTemplate.send(exchangeTradeEventsTopic, eventJson);
                log.info("Published trade executed event for symbol: {}", symbol);
            } catch (JsonProcessingException e) {
                log.error("Error serializing trade executed event to JSON for symbol: {}", symbol, e);
            }
        });
    }

    /**
     * Publish order event - Migrated từ Exchange OrderEventProducer (2 parameters pattern)
     */
    public void publishOrderEvent(String symbol, Object orderEvent) {
        messageExecutor.submit(() -> {
            try {
                String eventJson = objectMapper.writeValueAsString(orderEvent);
                kafkaTemplate.send(exchangeOrderEventsTopic, eventJson);
                log.info("Published order event for symbol: {}", symbol);
            } catch (JsonProcessingException e) {
                log.error("Error serializing order event to JSON for symbol: {}", symbol, e);
            }
        });
    }

    /**
     * Publish last price update - Migrated từ Exchange PriceProducer (2 parameters pattern)
     */
    public void publishLastPriceUpdate(String symbol, BigDecimal price, BigDecimal volume) {
        messageExecutor.submit(() -> {
            try {
                LastPriceMessage message = LastPriceMessage.builder()
                        .symbol(symbol)
                        .price(price)
                        .volume(volume)
                        .timestamp(LocalDateTime.now())
                        .build();

                String messageJson = objectMapper.writeValueAsString(message);
                kafkaTemplate.send(exchangeLastPriceTopic, messageJson);
                log.info("Published last price update for symbol: {} price: {}", symbol, price);
            } catch (JsonProcessingException e) {
                log.error("Error serializing last price update to JSON for symbol: {}", symbol, e);
            }
        });
    }

    /**
     * Publish exchange order event
     */
    public void publishExchangeOrderEvent(String symbol, Object orderEvent) {
        publishAsync(exchangeOrderEventsTopic, symbol, orderEvent, "exchange order event");
    }

    // ==================== HELPER METHODS ====================

    /**
     * Extract symbol from trade object
     */
    private String extractSymbolFromTrade(Object trade) {
        try {
            if (trade instanceof Map) {
                return String.valueOf(((Map<?, ?>) trade).get("symbol"));
            }
            return "unknown";
        } catch (Exception e) {
            return "unknown";
        }
    }

    /**
     * Shutdown executor
     */
    public void shutdown() {
        messageExecutor.shutdown();
    }
}
