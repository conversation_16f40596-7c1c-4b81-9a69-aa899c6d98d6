package com.icetea.lotus.matching.infrastructure.persistence.redis.document;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * Redis Snapshot Document cho Order Book Snapshots
 * Tối ưu hóa cho Redis storage với JSON serialization
 * 
 * <AUTHOR> nguyen
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class RedisSnapshotDocument {

    @JsonProperty("id")
    private String id;

    @JsonProperty("symbol")
    private String symbol;

    @JsonProperty("trading_type")
    private String tradingType; // "SPOT" or "FUTURES"

    @JsonProperty("version")
    private Long version;

    @JsonProperty("timestamp")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'", timezone = "UTC")
    private Instant timestamp;

    @JsonProperty("buy_orders")
    private Map<String, List<OrderDocument>> buyOrders;

    @JsonProperty("sell_orders")
    private Map<String, List<OrderDocument>> sellOrders;

    @JsonProperty("all_orders")
    private List<OrderDocument> allOrders;

    @JsonProperty("stop_orders")
    private List<OrderDocument> stopOrders;

    @JsonProperty("metadata")
    private SnapshotMetadata metadata;

    @JsonProperty("created_at")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'", timezone = "UTC")
    private Instant createdAt;

    @JsonProperty("expires_at")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'", timezone = "UTC")
    private Instant expiresAt;

    /**
     * Order Document cho Redis storage
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class OrderDocument {
        @JsonProperty("order_id")
        private String orderId;

        @JsonProperty("member_id")
        private Long memberId;

        @JsonProperty("direction")
        private String direction; // BUY, SELL

        @JsonProperty("type")
        private String type; // LIMIT_PRICE, MARKET_PRICE, STOP_LIMIT, STOP_MARKET

        @JsonProperty("status")
        private String status; // NEW, PARTIAL_FILLED, FILLED, CANCELED

        @JsonProperty("position_id")
        private Long positionId;

        @JsonProperty("price")
        private MoneyDocument price;

        @JsonProperty("quantity")
        private MoneyDocument quantity;

        @JsonProperty("filled_quantity")
        private MoneyDocument filledQuantity;

        @JsonProperty("turnover")
        private String turnover;

        @JsonProperty("fee")
        private String fee;

        @JsonProperty("leverage")
        private Integer leverage;

        @JsonProperty("margin_trade")
        private Boolean marginTrade;

        @JsonProperty("stop_price")
        private MoneyDocument stopPrice;

        @JsonProperty("trigger_condition")
        private String triggerCondition;

        @JsonProperty("time_in_force")
        private String timeInForce;

        @JsonProperty("created_time")
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'", timezone = "UTC")
        private Instant createdTime;

        @JsonProperty("updated_time")
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'", timezone = "UTC")
        private Instant updatedTime;

        @JsonProperty("completed_time")
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'", timezone = "UTC")
        private Instant completedTime;

        @JsonProperty("canceled_time")
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'", timezone = "UTC")
        private Instant canceledTime;
    }

    /**
     * Money Document cho Redis storage
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class MoneyDocument {
        @JsonProperty("amount")
        private String amount;

        @JsonProperty("scale")
        private Integer scale;

        @JsonProperty("currency")
        private String currency;
    }

    /**
     * Snapshot Metadata cho Redis storage
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SnapshotMetadata {
        @JsonProperty("total_orders")
        private Integer totalOrders;

        @JsonProperty("buy_orders_count")
        private Integer buyOrdersCount;

        @JsonProperty("sell_orders_count")
        private Integer sellOrdersCount;

        @JsonProperty("stop_orders_count")
        private Integer stopOrdersCount;

        @JsonProperty("price_levels_buy")
        private Integer priceLevelsBuy;

        @JsonProperty("price_levels_sell")
        private Integer priceLevelsSell;

        @JsonProperty("snapshot_size_bytes")
        private Long snapshotSizeBytes;

        @JsonProperty("compression_ratio")
        private Double compressionRatio;

        @JsonProperty("creation_time_micros")
        private Long creationTimeMicros;

        @JsonProperty("source_node")
        private String sourceNode;

        @JsonProperty("checksum")
        private String checksum;

        @JsonProperty("trading_type")
        private String tradingType;

        @JsonProperty("engine_type")
        private String engineType;
    }

    /**
     * Generate unique ID cho Redis snapshot
     */
    public static String generateId(String symbol, String tradingType, Long version) {
        return String.format("%s_%s_%d_%d", symbol, tradingType, version, System.currentTimeMillis());
    }

    /**
     * Generate Redis key cho snapshot
     */
    public static String generateRedisKey(String symbol, String tradingType, Long version) {
        return String.format("matching-engine:snapshot:%s:%s:%d", symbol, tradingType, version);
    }

    /**
     * Generate Redis key cho latest snapshot
     */
    public static String generateLatestKey(String symbol, String tradingType) {
        return String.format("matching-engine:snapshot:latest:%s:%s", symbol, tradingType);
    }

    /**
     * Generate Redis key cho version list
     */
    public static String generateVersionsKey(String symbol, String tradingType) {
        return String.format("matching-engine:snapshot:versions:%s:%s", symbol, tradingType);
    }

    /**
     * Calculate TTL cho snapshot (default 24 hours)
     */
    public static Instant calculateExpiryTime(int ttlHours) {
        return Instant.now().plusSeconds(ttlHours * 3600L);
    }

    /**
     * Calculate TTL in seconds
     */
    public static long calculateTtlSeconds(int ttlHours) {
        return ttlHours * 3600L;
    }
}
