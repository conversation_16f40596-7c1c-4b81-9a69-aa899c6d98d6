package com.icetea.lotus.matching.infrastructure.exchange;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.matching.domain.enums.SpotOrderDirection;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * ExchangeTrade - Đồng bộ với exchange-core.ExchangeTrade
 * Exact same structure như exchange-core.ExchangeTrade để đảm bảo compatibility
 *
 * <AUTHOR> nguyen
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExchangeTrade implements Serializable {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Trading symbol (e.g., "BTC/USDT")
     */
    private String symbol;

    /**
     * Trade execution price
     */
    private BigDecimal price;

    /**
     * Trade amount/quantity
     */
    private BigDecimal amount;

    /**
     * Buy side turnover (price * amount for buy side)
     */
    private BigDecimal buyTurnover;

    /**
     * Sell side turnover (price * amount for sell side)
     */
    private BigDecimal sellTurnover;

    /**
     * Trade direction from taker perspective
     * BUY = taker is buying (maker is selling)
     * SELL = taker is selling (maker is buying)
     */
    private SpotOrderDirection direction;

    /**
     * Buy order ID (order that bought)
     */
    private String buyOrderId;

    /**
     * Sell order ID (order that sold)
     */
    private String sellOrderId;

    /**
     * Trade execution time (timestamp in milliseconds)
     */
    private Long time;

    /**
     * Indicates if this trade resulted in partially filled orders
     * Used by market service to handle order status updates
     */
    private Boolean isPartiallyFilled;

    /**
     * The price of the maker order in this trade
     * Used for calculating average price as simple average of maker prices
     */
    private BigDecimal makerPrice;

    // ===== COMPATIBILITY METHODS =====

    /**
     * Get trade ID - compatibility method for event publishing
     */
    public String getTradeId() {
        return symbol + "_" + time + "_" + System.nanoTime();
    }

    /**
     * Get taker order ID - compatibility method for event publishing
     */
    public String getTakerOrderId() {
        // Taker is determined by direction
        return direction == SpotOrderDirection.BUY ? buyOrderId : sellOrderId;
    }

    /**
     * Get maker order ID - compatibility method for event publishing
     */
    public String getMakerOrderId() {
        // Maker is opposite of taker
        return direction == SpotOrderDirection.BUY ? sellOrderId : buyOrderId;
    }

    /**
     * JSON serialization - đồng bộ với exchange-core.ExchangeTrade
     */
    @Override
    public String toString() {
        try {
            return objectMapper.writeValueAsString(this);
        } catch (JsonProcessingException e) {
            return "{\"error\":\"Failed to serialize ExchangeTrade\"}";
        }
    }
}
