package com.icetea.lotus.matching.infrastructure.exchange;


import com.icetea.lotus.matching.domain.entity.ExchangeOrder;
import com.icetea.lotus.matching.domain.entity.Member;
import com.icetea.lotus.matching.domain.entity.Order;
import com.icetea.lotus.matching.domain.entity.SimpleStopOrder;
import com.icetea.lotus.matching.domain.enums.OrderDirection;
import com.icetea.lotus.matching.domain.enums.OrderStatus;
import com.icetea.lotus.matching.domain.enums.OrderType;
import com.icetea.lotus.matching.domain.enums.SpotOrderDirection;
import com.icetea.lotus.matching.domain.enums.SpotOrderStatus;
import com.icetea.lotus.matching.domain.enums.SpotOrderType;
import com.icetea.lotus.matching.domain.enums.StopOrderStatus;
import com.icetea.lotus.matching.domain.enums.StopOrderStrategy;
import com.icetea.lotus.matching.domain.enums.StopOrderType;
import com.icetea.lotus.matching.domain.service.StopOrderStrategyDetector;
import com.icetea.lotus.matching.domain.valueobject.Money;
import com.icetea.lotus.matching.domain.valueobject.OrderId;
import com.icetea.lotus.matching.domain.valueobject.Symbol;
import com.icetea.lotus.matching.infrastructure.constants.CommonConstance;
import com.icetea.lotus.matching.infrastructure.messaging.dto.ExchangeOrderMessage;
import com.icetea.lotus.matching.infrastructure.messaging.producer.ExchangeKafkaProducer;
import com.icetea.lotus.matching.infrastructure.persistence.redis.document.RedisSnapshotDocument;
import com.icetea.lotus.matching.infrastructure.persistence.redis.service.RedisSnapshotService;
import com.icetea.lotus.matching.infrastructure.service.LastPriceService;
import com.icetea.lotus.matching.infrastructure.service.MatchingEngineStopOrderManager;
import com.icetea.lotus.matching.infrastructure.stp.SelfTradePreventionService;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.AMOUNT;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.BUY_ORDER_ID;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.BUY_TURNOVER;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.CREATED_TIME;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.DIRECTION;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.FILLED_QUANTITY;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.IS_PARTIALLY_FILL;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.LIMIT_PRICE;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.MARKET_PRICE;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.MEMBER_ID;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.ORDERID;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.ORDER_ID;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.PRICE;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.QUANTITY;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.SELL_ORDER_ID;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.SELL_TURNOVER;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.STATUS;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.STOP_PRICE;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.SYMBOL;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.TIME;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.TIMESTAMP;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.TRADED_AMOUNT;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.TYPE;

/**
 * Exchange Compatibility Service
 * Cung cấp interface tương thích với Exchange module
 * Sử dụng ExchangeMatchingEngine với exact CoinTraderV2 logic
 *
 * <AUTHOR> nguyen
 */
@RequiredArgsConstructor
@Service
public class ExchangeCompatibilityService {

    private static final Logger logger = LoggerFactory.getLogger(ExchangeCompatibilityService.class);
    private final TradePlatePublisher tradePlatePublisher;
    private final RedisSnapshotService snapshotService;
    private final SelfTradePreventionService stpService;
    private final ApplicationEventPublisher eventPublisher;
    private final MatchingEngineStopOrderManager stopOrderManager;
    private final ExchangeKafkaProducer exchangeKafkaProducer;
    private final MongoTemplate mongoTemplate;

    private final LastPriceService lastPriceService;
    // Map symbol -> ExchangeMatchingEngine instance
    private final Map<String, ExchangeMatchingEngine> engineMap = new ConcurrentHashMap<>();

    // Suppress per-order trade-plate sends during restore to avoid spamming Kafka
    private volatile boolean restoreInProgress = false;

    // Cache for last trade price per symbol
    private final Map<String, BigDecimal> lastTradePriceCache = new ConcurrentHashMap<>();


    @PostConstruct
    public void initializeDependencies() {
        // Inject LastPriceService vào MatchingEngineStopOrderManager để tránh circular dependency
        stopOrderManager.setLastPriceService(lastPriceService);
        logger.info("Injected LastPriceService into MatchingEngineStopOrderManager");

        // Initialize default symbols để đảm bảo có engines cho snapshot
        initializeDefaultSymbols();
    }

    /**
     * Initialize default symbols để đảm bảo có engines cho snapshot
     */
    private void initializeDefaultSymbols() {
        try {
            // Default symbols để test snapshot functionality
            String[] defaultSymbols = {"BTC/USDT", "ETH/USDT", "BNB/USDT"};

            logger.info("Initializing default symbols for snapshot functionality...");

            for (String symbol : defaultSymbols) {
                // Initialize matching engine for symbol
                initializeMatchingEngine(symbol);

                // Try to restore from snapshot first
                boolean restored = restoreOrderBookFromDatabase(symbol);
                if (restored) {
                    logger.info("Restored order book from snapshot/database for symbol: {}", symbol);
                } else {
                    logger.info("Started with empty order book for symbol: {}", symbol);
                }
            }

            logger.info("Completed initializing {} default symbols, total engines: {}",
                    defaultSymbols.length, engineMap.size());

        } catch (Exception e) {
            logger.error("Error initializing default symbols", e);
        }
    }

    /**
     * Process exchange order DTO directly - no mapper overhead
     * Preferred method for direct DTO usage
     */
    public ExchangeTradeResult processExchangeOrderDTO(ExchangeOrder exchangeOrder) {
        try {
            logger.info("Processing exchange order DTO: {} {} {} price={} amount={} triggered={}",
                    exchangeOrder.getOrderId(), exchangeOrder.getDirection(), exchangeOrder.getType(),
                    exchangeOrder.getPrice(), exchangeOrder.getAmount(), exchangeOrder.getTriggered());

            // Validate order before processing
            ExchangeTradeResult validationResult = validateOrderBeforeProcessing(exchangeOrder);
            if (!validationResult.isSuccess()) {
                return validationResult;
            }

            // Handle stop orders before regular processing
            if (exchangeOrder.getType().isStopOrder() && exchangeOrder.getTriggered() == null && SpotOrderStatus.TRIGGER.equals(exchangeOrder.getStatus())) {
                return handleStopOrder(exchangeOrder);
            }

            // Get matching engine and execute trade
            ExchangeMatchingEngine engine = getOrCreateEngine(exchangeOrder.getSymbol());
            if (engine.isTradingHalt()) {
                return ExchangeTradeResult.error("Trading is halted for symbol: " + exchangeOrder.getSymbol());
            }

            Object matchingResult = engine.trade(exchangeOrder);

            // Validate matching result
            MatchingResultValidation resultValidation = validateMatchingResult(matchingResult);
            if (!resultValidation.isSuccess()) {
                return ExchangeTradeResult.error(resultValidation.getMessage());
            }

            // Extract data from matching result
            MatchingResultData resultData = extractMatchingResultData(matchingResult);

            // Convert trades to Exchange format
            List<ExchangeTradeData> exchangeTrades = convertToExchangeTrades(resultData.getTrades());

            // Handle trade plate updates
            TradePlate updatedTradePlate = handleTradePlateUpdates(exchangeOrder, resultData);

            // Build and return final result
            return buildSuccessResult(resultData, exchangeTrades, updatedTradePlate, exchangeOrder);

        } catch (Exception e) {
            logger.error("Failed to process exchange order DTO: {}", exchangeOrder.getSummary(), e);
            return ExchangeTradeResult.error("Processing failed: " + e.getMessage());
        }
    }

    /**
     * Validate order before processing - extracted to reduce complexity
     */
    private ExchangeTradeResult validateOrderBeforeProcessing(ExchangeOrder exchangeOrder) {
        // Validate DTO
        if (!exchangeOrder.isValid()) {
            return ExchangeTradeResult.error("Invalid exchange order DTO: " + exchangeOrder.getSummary());
        }

        // Validate DTO directly - no conversion overhead
        ExchangeTradeResult validationResult = validateExchangeOrderDTO(exchangeOrder);
        if (!validationResult.isSuccess()) {
            return validationResult;
        }

        // Validate spot order compatibility
        if (!isSpotOrderDTOCompatible(exchangeOrder)) {
            return ExchangeTradeResult.error("Order DTO not compatible with spot trading");
        }

        return ExchangeTradeResult.builder()
                .success(true)
                .message("Validation passed")
                .build();
    }

    /**
     * Validate matching result - extracted to reduce complexity
     */
    private MatchingResultValidation validateMatchingResult(Object matchingResult) {
        if (!(matchingResult instanceof Map<?, ?> resultMap)) {
            return new MatchingResultValidation(false, "Invalid matching result format");
        }

        Object successObj = resultMap.get("success");
        if (!(successObj instanceof Boolean booleanValue) || Boolean.TRUE.equals(!booleanValue)) {
            Object messageObj = resultMap.get("message");
            String message = messageObj instanceof String string ? string : "Unknown error";
            return new MatchingResultValidation(false, message);
        }

        return new MatchingResultValidation(true, "Success");
    }

    /**
     * Extract data from matching result - extracted to reduce complexity
     */
    private MatchingResultData extractMatchingResultData(Object matchingResult) {
        if (!(matchingResult instanceof Map<?, ?> resultMap)) {
            return new MatchingResultData("", "", new ArrayList<>(), new ArrayList<>(), new ArrayList<>());
        }

        String orderId = extractStringFromMap(resultMap, ORDERID);
        String symbol = extractStringFromMap(resultMap, SYMBOL);
        List<Object> trades = extractListFromMap(resultMap, "trades");
        List<Object> completedOrders = extractListFromMap(resultMap, "completedOrders");
        List<Object> partiallyFilledOrders = extractListFromMap(resultMap, "partiallyFilledOrders");

        return new MatchingResultData(orderId, symbol, trades, completedOrders, partiallyFilledOrders);
    }

    /**
     * Helper method to extract string from map
     */
    private String extractStringFromMap(Map<?, ?> map, String key) {
        Object obj = map.get(key);
        return obj instanceof String string ? string : "";
    }

    /**
     * Helper method to extract list from map
     */
    private List<Object> extractListFromMap(Map<?, ?> map, String key) {
        Object obj = map.get(key);
        return obj instanceof List<?> list ? (List<Object>) list : new ArrayList<>();
    }

    /**
     * Handle trade plate updates - extracted to reduce complexity
     */
    private TradePlate handleTradePlateUpdates(ExchangeOrder exchangeOrder, MatchingResultData resultData) {
        try {
            // Remove completed orders from trade plates
            removeCompletedOrdersFromTradePlates(resultData.getCompletedOrders(), resultData.getSymbol());

            // Handle unfilled limit orders
            TradePlate tradePlateToSend = handleUnfilledLimitOrders(exchangeOrder, resultData);

            // Send trade plates based on trades
            return sendTradePlatesBasedOnTrades(resultData, tradePlateToSend);

        } catch (Exception e) {
            logger.warn("Failed to update trade plate for order: symbol={}, orderId={}, error={}",
                    resultData.getSymbol(), exchangeOrder.getOrderId(), e.getMessage());
            return null;
        }
    }

    /**
     * Remove completed orders from trade plates
     */
    private void removeCompletedOrdersFromTradePlates(List<Object> completedOrders, String symbol) {
        if (completedOrders.isEmpty()) {
            return;
        }

        for (Object completedOrderObj : completedOrders) {
            ExchangeOrder completedOrder = convertObjectToExchangeOrder(completedOrderObj, symbol);
            if (completedOrder != null) {
                TradePlate completedOrderTradePlate = tradePlatePublisher.getTradePlate(symbol, completedOrder.getDirection());
                completedOrderTradePlate.remove(completedOrder);
                logger.info("Removed completed order {} from trade plate: direction={}",
                        completedOrder.getOrderId(), completedOrder.getDirection());
            }
        }
    }

    /**
     * Handle unfilled limit orders
     */
    private TradePlate handleUnfilledLimitOrders(ExchangeOrder exchangeOrder, MatchingResultData resultData) {
        if ((exchangeOrder.getType() == SpotOrderType.LIMIT_PRICE ||
                exchangeOrder.getType() == SpotOrderType.STOP_LIMIT) &&
                !isOrderFullyFilled(exchangeOrder, resultData.getTrades())) {

            boolean added = addOrderToTradePlate(exchangeOrder, resultData.getSymbol());
            if (added) {
                logger.info("Added unfilled limit/stop-limit order to trade plate: symbol={}, orderId={}, direction={}, type={}",
                        resultData.getSymbol(), exchangeOrder.getOrderId(), exchangeOrder.getDirection(), exchangeOrder.getType());
                return tradePlatePublisher.getTradePlate(resultData.getSymbol(), exchangeOrder.getDirection());
            }
        }
        return null;
    }

    /**
     * Send trade plates based on trades
     */
    private TradePlate sendTradePlatesBasedOnTrades(MatchingResultData resultData, TradePlate tradePlateToSend) {
        if (!resultData.getTrades().isEmpty()) {
            return sendTradePlatesForBothDirections(resultData);
        } else if (tradePlateToSend != null) {
            sendSingleTradePlate(tradePlateToSend, resultData.getSymbol());
            return tradePlateToSend;
        } else {
            logger.info("Skipped trade plate message for symbol: {} (no changes needed)", resultData.getSymbol());
            return null;
        }
    }

    /**
     * Send trade plates for both directions when trades occur
     */
    private TradePlate sendTradePlatesForBothDirections(MatchingResultData resultData) {
        logger.info("Successful matching detected: symbol={}, trades={}, sending trade plates for BOTH directions",
                resultData.getSymbol(), resultData.getTrades().size());

        TradePlate buyTradePlate = tradePlatePublisher.getTradePlate(resultData.getSymbol(), SpotOrderDirection.BUY);
        TradePlate sellTradePlate = tradePlatePublisher.getTradePlate(resultData.getSymbol(), SpotOrderDirection.SELL);

        logger.info("Sending BUY trade plate: symbol={}, depth={}", resultData.getSymbol(), buyTradePlate.getDepth());
        tradePlatePublisher.sendTradePlateMessage(buyTradePlate);

        logger.info("Sending SELL trade plate: symbol={}, depth={}", resultData.getSymbol(), sellTradePlate.getDepth());
        tradePlatePublisher.sendTradePlateMessage(sellTradePlate);

        logger.info("  Sent trade plate messages for BOTH directions after successful matching: symbol={}, trades={}",
                resultData.getSymbol(), resultData.getTrades().size());

        return buyTradePlate;
    }

    /**
     * Send single trade plate
     */
    private void sendSingleTradePlate(TradePlate tradePlate, String symbol) {
        tradePlatePublisher.sendTradePlateMessage(tradePlate);
        logger.info("Sent trade plate message for symbol: {} direction: {} (reason: unfilled limit order)",
                symbol, tradePlate.getDirection());
    }

    /**
     * Build success result - extracted to reduce complexity
     */
    private ExchangeTradeResult buildSuccessResult(MatchingResultData resultData, List<ExchangeTradeData> exchangeTrades,
                                                   TradePlate updatedTradePlate, ExchangeOrder exchangeOrder) {
        ExchangeTradeResult result = ExchangeTradeResult.builder()
                .success(true)
                .orderId(resultData.getOrderId())
                .symbol(resultData.getSymbol())
                .trades(exchangeTrades)
                .tradesCount(resultData.getTrades().size())
                .completedOrders(resultData.getCompletedOrders())
                .partiallyFilledOrders(resultData.getPartiallyFilledOrders())
                .tradePlate(updatedTradePlate)
                .message("Order DTO processed successfully with Exchange logic - no conversion overhead")
                .build();

        logger.info("  Successfully processed exchange order DTO {} with {} trades, {} completed, {} partial",
                exchangeOrder.getOrderId(), resultData.getTrades().size(),
                resultData.getCompletedOrders().size(), resultData.getPartiallyFilledOrders().size());

        return result;
    }

    /**
     * Helper class for matching result validation
     */
    private static class MatchingResultValidation {
        private final boolean success;
        private final String message;

        public MatchingResultValidation(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getMessage() {
            return message;
        }
    }

    /**
     * Helper class for matching result data
     */
    private static class MatchingResultData {
        private final String orderId;
        private final String symbol;
        private final List<Object> trades;
        private final List<Object> completedOrders;
        private final List<Object> partiallyFilledOrders;

        public MatchingResultData(String orderId, String symbol, List<Object> trades,
                                  List<Object> completedOrders, List<Object> partiallyFilledOrders) {
            this.orderId = orderId;
            this.symbol = symbol;
            this.trades = trades;
            this.completedOrders = completedOrders;
            this.partiallyFilledOrders = partiallyFilledOrders;
        }

        public String getOrderId() {
            return orderId;
        }

        public String getSymbol() {
            return symbol;
        }

        public List<Object> getTrades() {
            return trades;
        }

        public List<Object> getCompletedOrders() {
            return completedOrders;
        }

        public List<Object> getPartiallyFilledOrders() {
            return partiallyFilledOrders;
        }
    }

    /**
     * Process order cancel for compatibility with messaging - Copy từ Exchange module logic
     */
    public ExchangeTradeResult processOrderCancel(ExchangeOrderMessage cancelRequest) {
        try {
            // Validate cancel request
            CancelRequestValidation validation = validateCancelRequest(cancelRequest);
            if (!validation.isValid()) {
                return ExchangeTradeResult.error(validation.getErrorMessage());
            }

            // Execute order cancellation
            CancelExecutionResult executionResult = executeCancellation(cancelRequest, validation);
            if (!executionResult.isSuccess()) {
                return ExchangeTradeResult.error(executionResult.getErrorMessage());
            }

            // Update trade plates after cancellation
            TradePlate updatedTradePlate = updateTradePlatesAfterCancel(cancelRequest, executionResult, validation);

            // Finalize cancellation
            return finalizeCancellation(cancelRequest, validation, updatedTradePlate);

        } catch (Exception e) {
            logger.error("Failed to process order cancel", e);
            return ExchangeTradeResult.error("Cancel processing failed: " + e.getMessage());
        }
    }

    /**
     * Validate cancel request - extracted to reduce complexity
     */
    private CancelRequestValidation validateCancelRequest(ExchangeOrderMessage cancelRequest) {
        String orderId = cancelRequest.getOrderId();
        String symbol = cancelRequest.getSymbol();

        if (orderId == null || symbol == null) {
            return new CancelRequestValidation(false, "Invalid cancel request: missing orderId or symbol", orderId, symbol);
        }

        return new CancelRequestValidation(true, null, orderId, symbol);
    }

    /**
     * Execute order cancellation - extracted to reduce complexity
     */
    private CancelExecutionResult executeCancellation(ExchangeOrderMessage cancelRequest, CancelRequestValidation validation) {
        String orderId = validation.getOrderId();
        String symbol = validation.getSymbol();
        ExchangeMatchingEngine engine = null;
        boolean cancelled = false;

        if (cancelRequest.getStatus() == SpotOrderStatus.TRIGGER) {
            logger.info("Processing cancel request for STOP order: symbol={}, orderId={}", symbol, orderId);
            cancelled = stopOrderManager.cancelStopOrder(orderId, CommonConstance.SPOT);
        } else if (cancelRequest.getStatus() == SpotOrderStatus.TRADING || cancelRequest.getStatus() == SpotOrderStatus.PARTIAL_FILLED) {
            logger.info("Processing cancel request for order {} symbol {}", orderId, symbol);

            // Get matching engine for symbol
            engine = engineMap.get(symbol);
            if (engine == null) {
                return new CancelExecutionResult(false, "No engine found for symbol: " + symbol, null);
            }

            // Delete order in order book of engine
            cancelled = engine.cancelOrder(orderId);
        }

        if (!cancelled) {
            return new CancelExecutionResult(false, "Failed to cancel order: " + orderId, engine);
        }

        return new CancelExecutionResult(true, null, engine);
    }

    /**
     * Update trade plates after cancellation - extracted to reduce complexity
     */
    private TradePlate updateTradePlatesAfterCancel(ExchangeOrderMessage cancelRequest, CancelExecutionResult executionResult, CancelRequestValidation validation) {
        // Only update trade plates for TRADING or PARTIAL_FILLED orders
        if (cancelRequest.getStatus() != SpotOrderStatus.TRADING && cancelRequest.getStatus() != SpotOrderStatus.PARTIAL_FILLED) {
            return null;
        }

        try {
            return processLimitOrderTradePlateUpdate(cancelRequest, executionResult.getEngine(), validation);
        } catch (Exception e) {
            logger.warn("Failed to update trade plate for order cancel: symbol={}, orderId={}, error={}",
                    validation.getSymbol(), validation.getOrderId(), e.getMessage());
            return null;
        }
    }

    /**
     * Process trade plate update for limit orders - extracted to reduce complexity
     */
    private TradePlate processLimitOrderTradePlateUpdate(ExchangeOrderMessage cancelRequest, ExchangeMatchingEngine engine, CancelRequestValidation validation) {
        String orderId = validation.getOrderId();
        String symbol = validation.getSymbol();

        // SỬ DỤNG TRỰC TIẾP EXCHANGEORDER - KHÔNG CONVERT
        ExchangeOrder exchangeOrderToCancel = convertCancelRequestToExchangeOrder(cancelRequest, orderId, symbol);
        if (exchangeOrderToCancel == null) {
            return null;
        }

        SpotOrderDirection direction = exchangeOrderToCancel.getDirection();
        BigDecimal price = exchangeOrderToCancel.getPrice();

        // CRITICAL FIX: Get trade plate cùng direction với order để add
        TradePlate tradePlate = tradePlatePublisher.getTradePlate(symbol, direction);
        if (tradePlate == null) {
            return null;
        }

        // CHỈ remove từ trade plate nếu là LIMIT order (bao gồm STOP_LIMIT)
        // Market orders không có trong trade plate
        if (cancelRequest.getType() == SpotOrderType.LIMIT_PRICE || cancelRequest.getType() == SpotOrderType.STOP_LIMIT) {
            return updateLimitOrderTradePlate(exchangeOrderToCancel, tradePlate, engine, direction, price, symbol, orderId);
        } else {
            logger.info("Market order cancel, no trade plate update needed: symbol={}, orderId={}", symbol, orderId);
            return null;
        }
    }

    /**
     * Update trade plate for limit order cancellation - extracted to reduce complexity
     */
    private TradePlate updateLimitOrderTradePlate(ExchangeOrder exchangeOrderToCancel, TradePlate tradePlate,
                                                  ExchangeMatchingEngine engine, SpotOrderDirection direction,
                                                  BigDecimal price, String symbol, String orderId) {
        // Remove traded amount from trade plate
        tradePlate.remove(exchangeOrderToCancel); // DIRECT USAGE

        if (engine != null && !engine.isMergeOrderEmpty(direction, price)) {
            tradePlate.updateOrderId(price, engine.getIDofOldestOrderInOrderBook(direction, price));
        }

        // Send updated trade plate
        tradePlatePublisher.sendTradePlateMessage(tradePlate);

        // CRITICAL FIX: Gửi trade plate NGƯỢC CHIỀU như Exchange module
        SpotOrderDirection plateDirectionToSend = direction == SpotOrderDirection.BUY ?
                SpotOrderDirection.SELL : SpotOrderDirection.BUY;
        TradePlate plateToSend = tradePlatePublisher.getTradePlate(symbol, plateDirectionToSend);
        tradePlatePublisher.sendTradePlateMessage(plateToSend);

        logger.info("Updated trade plate for limit order cancel: symbol={}, orderId={}, direction={}",
                symbol, orderId, plateDirectionToSend);

        return tradePlate;
    }

    /**
     * Finalize cancellation and build result - extracted to reduce complexity
     */
    private ExchangeTradeResult finalizeCancellation(ExchangeOrderMessage cancelRequest, CancelRequestValidation validation, TradePlate updatedTradePlate) {
        cancelRequest.setStatus(SpotOrderStatus.CANCELED);
        cancelRequest.setCanceledTime(System.currentTimeMillis());

        return ExchangeTradeResult.builder()
                .success(true)
                .orderId(validation.getOrderId())
                .symbol(validation.getSymbol())
                .cancelResult(cancelRequest)  // Return ExchangeOrder object, not ExchangeCancelResult
                .tradePlate(updatedTradePlate) // Return updated trade plate
                .message("Order cancelled successfully")
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * Helper class for cancel request validation
     */
    private static class CancelRequestValidation {
        private final boolean valid;
        private final String errorMessage;
        private final String orderId;
        private final String symbol;

        public CancelRequestValidation(boolean valid, String errorMessage, String orderId, String symbol) {
            this.valid = valid;
            this.errorMessage = errorMessage;
            this.orderId = orderId;
            this.symbol = symbol;
        }

        public boolean isValid() {
            return valid;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getOrderId() {
            return orderId;
        }

        public String getSymbol() {
            return symbol;
        }
    }

    /**
     * Helper class for cancel execution result
     */
    private static class CancelExecutionResult {
        private final boolean success;
        private final String errorMessage;
        private final ExchangeMatchingEngine engine;

        public CancelExecutionResult(boolean success, String errorMessage, ExchangeMatchingEngine engine) {
            this.success = success;
            this.errorMessage = errorMessage;
            this.engine = engine;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public ExchangeMatchingEngine getEngine() {
            return engine;
        }
    }

    /**
     * Get or create matching engine for symbol
     */
    private ExchangeMatchingEngine getOrCreateEngine(String symbol) {
        return engineMap.computeIfAbsent(symbol, s -> {
            // Create matching engine for pure order matching
            //   CRITICAL FIX: Include new dependencies for lastprice update
            ExchangeMatchingEngine engine = new ExchangeMatchingEngine(symbol, tradePlatePublisher, stpService, eventPublisher, exchangeKafkaProducer, this, lastPriceService);
            // ⭐ CRITICAL FIX: Set trade plate publisher for real-time trade plate updates

            logger.info("Created new ExchangeMatchingEngine for symbol: {} with trade plate publisher and lastprice services", s);
            return engine;
        });
    }

    /**
     * Convert trade objects to ExchangeTradeData format
     * Cập nhật để sử dụng structure mới đồng bộ với exchange-core.ExchangeTrade
     */
    private List<ExchangeTradeData> convertToExchangeTrades(List<Object> trades) {
        List<ExchangeTradeData> result = new ArrayList<>();

        for (Object trade : trades) {
            if (trade instanceof Map) {
                Map<?, ?> tradeMap = (Map<?, ?>) trade;

                // Parse direction từ String sang SpotOrderDirection
                SpotOrderDirection direction = null;
                String directionStr = getStringFromMap(tradeMap, DIRECTION);
                if (directionStr != null) {
                    try {
                        direction = SpotOrderDirection.valueOf(directionStr.toUpperCase());
                    } catch (IllegalArgumentException e) {
                        logger.warn("Invalid direction value: {}, defaulting to BUY", directionStr);
                        direction = SpotOrderDirection.BUY;
                    }
                }

                ExchangeTradeData tradeData = ExchangeTradeData.builder()
                        .symbol(getStringFromMap(tradeMap, SYMBOL))
                        .price(getBigDecimalFromMap(tradeMap, PRICE))
                        .amount(getBigDecimalFromMap(tradeMap, AMOUNT))
                        .buyTurnover(getBigDecimalFromMap(tradeMap, BUY_TURNOVER))
                        .sellTurnover(getBigDecimalFromMap(tradeMap, SELL_TURNOVER))
                        .direction(direction)
                        .buyOrderId(getStringFromMap(tradeMap, BUY_ORDER_ID))
                        .sellOrderId(getStringFromMap(tradeMap, SELL_ORDER_ID))
                        .time(getLongFromMap(tradeMap, TIME)) // Sử dụng 'time' thay vì 'timestamp'
                        .isPartiallyFilled(getBooleanFromMap(tradeMap, IS_PARTIALLY_FILL))
                        .build();

                result.add(tradeData);
            }
        }

        return result;
    }

    private String getStringFromMap(Map<?, ?> map, String key) {
        Object value = map.get(key);
        return value instanceof String string ? string : "";
    }

    private BigDecimal getBigDecimalFromMap(Map<?, ?> map, String key) {
        Object value = map.get(key);
        return value instanceof BigDecimal bigDecimal ? bigDecimal : BigDecimal.ZERO;
    }

    private Long getLongFromMap(Map<?, ?> map, String key) {
        Object value = map.get(key);
        return value instanceof Long longValue ? longValue : 0L;
    }

    private Boolean getBooleanFromMap(Map<?, ?> map, String key) {
        Object value = map.get(key);
        return value instanceof Boolean booleanValue && booleanValue;
    }

    /**
     * Convert Object to ExchangeOrder
     */
    private ExchangeOrder convertObjectToExchangeOrder(Object orderObj, String symbol) {
        try {
            if (orderObj == null) {
                return null;
            }

            // If it's already an ExchangeOrder, return it
            if (orderObj instanceof ExchangeOrder exchangeOrder) {
                return exchangeOrder;
            }

            // If it's a Map, extract fields
            if (orderObj instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> orderMap = (Map<String, Object>) orderObj;
                return convertMapToExchangeOrder(orderMap, symbol);
            }

            logger.warn("Unsupported order object type: {}", orderObj.getClass().getSimpleName());
            return null;

        } catch (Exception e) {
            logger.error("Error converting object to ExchangeOrder", e);
            return null;
        }
    }

    /**
     * Convert Map to ExchangeOrder - extracted to reduce complexity
     */
    private ExchangeOrder convertMapToExchangeOrder(Map<String, Object> orderMap, String symbol) {
        // Extract basic fields
        OrderBasicFields basicFields = extractBasicFieldsFromMap(orderMap);

        // Extract enum fields
        OrderEnumFields enumFields = extractEnumFieldsFromMap(orderMap);

        // Build ExchangeOrder
        return ExchangeOrder.builder()
                .orderId(basicFields.getOrderId())
                .symbol(symbol)
                .memberId(basicFields.getMemberId())
                .direction(enumFields.getDirection())
                .type(enumFields.getType())
                .status(enumFields.getStatus())
                .price(basicFields.getPrice() != null ? basicFields.getPrice() : BigDecimal.ZERO)
                .amount(basicFields.getAmount() != null ? basicFields.getAmount() : BigDecimal.ZERO)
                .tradedAmount(basicFields.getTradedAmount() != null ? basicFields.getTradedAmount() : BigDecimal.ZERO)
                .time(basicFields.getTime())
                .build();
    }

    /**
     * Extract basic fields from map - extracted to reduce complexity
     */
    private OrderBasicFields extractBasicFieldsFromMap(Map<String, Object> orderMap) {
        String orderId = getStringFromMap(orderMap, ORDERID);
        Long memberId = getLongFromMap(orderMap, MEMBER_ID);
        BigDecimal price = getBigDecimalFromMap(orderMap, PRICE);
        BigDecimal amount = getBigDecimalFromMap(orderMap, AMOUNT);
        BigDecimal tradedAmount = getBigDecimalFromMap(orderMap, TRADED_AMOUNT);
        Long time = getLongFromMap(orderMap, TIME);

        return new OrderBasicFields(orderId, memberId, price, amount, tradedAmount, time);
    }

    /**
     * Extract enum fields from map - extracted to reduce complexity
     */
    private OrderEnumFields extractEnumFieldsFromMap(Map<String, Object> orderMap) {
        // Extract direction
        SpotOrderDirection direction = extractDirectionFromMap(orderMap);

        // Extract type
        SpotOrderType type = extractTypeFromMap(orderMap);

        // Extract status
        SpotOrderStatus status = extractStatusFromMap(orderMap);

        return new OrderEnumFields(direction, type, status);
    }

    /**
     * Extract direction from map - extracted to reduce complexity
     */
    private SpotOrderDirection extractDirectionFromMap(Map<String, Object> orderMap) {
        SpotOrderDirection direction = SpotOrderDirection.BUY; // Default
        Object directionObj = orderMap.get(DIRECTION);
        if (directionObj != null && "SELL".equals(directionObj.toString())) {
            direction = SpotOrderDirection.SELL;
        }
        return direction;
    }

    /**
     * Extract type from map - extracted to reduce complexity
     */
    private SpotOrderType extractTypeFromMap(Map<String, Object> orderMap) {
        SpotOrderType type = SpotOrderType.LIMIT_PRICE; // Default
        Object typeObj = orderMap.get(TYPE);
        if (typeObj != null && MARKET_PRICE.equals(typeObj.toString())) {
            type = SpotOrderType.MARKET_PRICE;
        }
        return type;
    }

    /**
     * Extract status from map - extracted to reduce complexity
     */
    private SpotOrderStatus extractStatusFromMap(Map<String, Object> orderMap) {
        SpotOrderStatus status = SpotOrderStatus.COMPLETED; // Default for completed orders
        Object statusObj = orderMap.get(STATUS);
        if (statusObj != null) {
            try {
                status = SpotOrderStatus.valueOf(statusObj.toString());
            } catch (IllegalArgumentException e) {
                // Keep default
            }
        }
        return status;
    }

    /**
     * Helper class for order basic fields
     */
    private static class OrderBasicFields {
        private final String orderId;
        private final Long memberId;
        private final BigDecimal price;
        private final BigDecimal amount;
        private final BigDecimal tradedAmount;
        private final Long time;

        public OrderBasicFields(String orderId, Long memberId, BigDecimal price, BigDecimal amount, BigDecimal tradedAmount, Long time) {
            this.orderId = orderId;
            this.memberId = memberId;
            this.price = price;
            this.amount = amount;
            this.tradedAmount = tradedAmount;
            this.time = time;
        }

        public String getOrderId() {
            return orderId;
        }

        public Long getMemberId() {
            return memberId;
        }

        public BigDecimal getPrice() {
            return price;
        }

        public BigDecimal getAmount() {
            return amount;
        }

        public BigDecimal getTradedAmount() {
            return tradedAmount;
        }

        public Long getTime() {
            return time;
        }
    }

    /**
     * Helper class for order enum fields
     */
    private static class OrderEnumFields {
        private final SpotOrderDirection direction;
        private final SpotOrderType type;
        private final SpotOrderStatus status;

        public OrderEnumFields(SpotOrderDirection direction, SpotOrderType type, SpotOrderStatus status) {
            this.direction = direction;
            this.type = type;
            this.status = status;
        }

        public SpotOrderDirection getDirection() {
            return direction;
        }

        public SpotOrderType getType() {
            return type;
        }

        public SpotOrderStatus getStatus() {
            return status;
        }
    }

    /**
     * Shutdown all engines
     * CRITICAL FIX: Save snapshots and last prices before shutdown
     */
    public void shutdown() {
        logger.info("Shutting down ExchangeCompatibilityService with {} engines", engineMap.size());

        try {
            //   CRITICAL FIX: Save all snapshots before shutdown
            logger.info("Saving snapshots before shutdown...");
            saveAllSnapshots();
            logger.info("Snapshots saved successfully");

            //   CRITICAL FIX: Save last prices to ensure persistence
            logger.info("Saving last prices before shutdown...");
            saveAllLastPrices();
            logger.info("Last prices saved successfully");

        } catch (Exception e) {
            logger.error("Error saving data during shutdown", e);
        }

        // Halt all engines
        for (Map.Entry<String, ExchangeMatchingEngine> entry : engineMap.entrySet()) {
            String symbol = entry.getKey();
            ExchangeMatchingEngine engine = entry.getValue();

            try {
                engine.setTradingHalt(true);
                logger.info("Shutdown engine for symbol: {}", symbol);
            } catch (Exception e) {
                logger.error("Error shutting down engine for symbol: {}", symbol, e);
            }
        }

        engineMap.clear();
        logger.info("ExchangeCompatibilityService shutdown completed");
    }

    /**
     * CRITICAL FIX: Save all last prices to ensure persistence
     * Lưu last prices vào MongoDB để có thể restore khi restart
     */
    private void saveAllLastPrices() {
        try {
            logger.info("Saving last prices for {} symbols", lastTradePriceCache.size());

            for (Map.Entry<String, BigDecimal> entry : lastTradePriceCache.entrySet()) {
                String symbol = entry.getKey();
                BigDecimal price = entry.getValue();
                saveLastPriceToMongoDB(symbol, price);
                logger.info("Saved last price for symbol: {} = {}", symbol, price);
            }

            logger.info("Completed saving last prices for {} symbols", lastTradePriceCache.size());

        } catch (Exception e) {
            logger.error("Error saving all last prices", e);
        }
    }

    /**
     * CRITICAL FIX: Save last price to MongoDB collection
     * Lưu last price vào collection riêng để có thể restore nhanh chóng
     */
    public void saveLastPriceToMongoDB(String symbol, BigDecimal price) {
        try {
            String collectionName = "spot_last_price";

            Map<String, Object> lastPriceDoc = new HashMap<>();
            lastPriceDoc.put(SYMBOL, symbol);
            lastPriceDoc.put(PRICE, price);
            lastPriceDoc.put(TIMESTAMP, System.currentTimeMillis());
            lastPriceDoc.put("updatedAt", new Date());

            // Upsert: update if exists, insert if not exists
            Query query = new Query(Criteria.where(SYMBOL).is(symbol));
            Update update = new Update()
                    .set(PRICE, price)
                    .set(TIMESTAMP, System.currentTimeMillis())
                    .set("updatedAt", new Date());

            mongoTemplate.upsert(query, update, collectionName);

            logger.info("Saved last price to MongoDB: symbol={}, price={}", symbol, price);

        } catch (Exception e) {
            logger.error("Error saving last price to MongoDB: symbol={}, price={}", symbol, price, e);
        }
    }

    // ===== VALIDATION METHODS - Copy từ CoinTraderV2.trade() logic =====

    /**
     * Validate ExchangeOrder directly - no conversion overhead
     */
    private ExchangeTradeResult validateExchangeOrderDTO(ExchangeOrder dto) {
        // Basic DTO validation
        if (!dto.isValid()) {
            return ExchangeTradeResult.error("Invalid exchange order DTO: " + dto.getSummary());
        }

        // Validate symbol
        if (dto.getSymbol() == null || dto.getSymbol().trim().isEmpty()) {
            return ExchangeTradeResult.error("Symbol cannot be empty");
        }

        // Validate member ID
        if (dto.getMemberId() == null || dto.getMemberId() <= 0) {
            return ExchangeTradeResult.error("Invalid member ID");
        }

        // Validate quantity
        if (dto.getAmount() == null || dto.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return ExchangeTradeResult.error("Order quantity must be greater than zero");
        }

        // Validate price for limit orders (including stop limit)
        if ((dto.getType() == SpotOrderType.LIMIT_PRICE ||
                dto.getType() == SpotOrderType.STOP_LIMIT) &&
                (dto.getPrice() == null || dto.getPrice().compareTo(BigDecimal.ZERO) <= 0)) {
            return ExchangeTradeResult.error("Limit/Stop-limit order price must be greater than zero");
        }

        // Validate stop order specific fields
        if (!dto.isValidStopOrder()) {
            return ExchangeTradeResult.error("Invalid stop order configuration");
        }

        logger.info("DTO validation passed for: {}", dto.getOrderId());
        return ExchangeTradeResult.builder()
                .success(true)
                .message("DTO validation passed")
                .build();
    }

    /**
     * Check if DTO is compatible with spot trading
     */
    private boolean isSpotOrderDTOCompatible(ExchangeOrder dto) {
        try {
            // Since we're using spot enums directly, all DTOs should be compatible
            return dto != null && dto.getOrderId() != null && dto.getSymbol() != null;
        } catch (Exception e) {
            logger.warn("Order DTO {} not compatible with spot trading: {}",
                    dto.getOrderId(), e.getMessage());
            return false;
        }
    }

    /**
     * Restore order book from database - Copy từ Exchange CoinTraderEvent pattern
     * CRITICAL FIX: Added database fallback when snapshot fails
     */
    public boolean restoreOrderBookFromDatabase(String symbol) {
        logger.info("Attempting to restore order book for symbol: {}", symbol);

        try {
            // Try to load snapshot from Redis first
            Optional<RedisSnapshotDocument> snapshotOpt = snapshotService.loadLatestSnapshot(symbol, "SPOT");

            if (snapshotOpt.isEmpty()) {
                logger.warn("No Redis snapshot found for symbol: {}. Starting with an empty order book.", symbol);
                return false;
            }

            logger.info("Found Redis snapshot for symbol: {}. Proceeding with restoration.", symbol);
            RedisSnapshotDocument snapshot = snapshotOpt.get();

            // Get or create matching engine for symbol
            ExchangeMatchingEngine engine = getOrCreateEngine(symbol);

            // Restore snapshot to engine
            boolean restored = restoreSnapshotToEngine(engine, snapshot, symbol);

            if (restored) {
                logger.info("Successfully restored order book from snapshot for symbol: {}", symbol);
            } else {
                logger.warn("Restoration process completed, but no orders were restored for symbol: {}. This could be due to an empty snapshot.", symbol);
            }
            return true; // Return true even if snapshot was empty, as the process itself succeeded.

        } catch (Exception e) {
            logger.error("Error restoring exchange order book for symbol: {}", symbol, e);
            return false;
        }
    }

    /**
     * Initialize matching engine for symbol - Copy từ Exchange CoinTraderEvent pattern
     */
    public void initializeMatchingEngine(String symbol) {
        logger.info("Initializing exchange matching engine for symbol: {}", symbol);

        try {
            // Get or create matching engine for symbol
            ExchangeMatchingEngine engine = getOrCreateEngine(symbol);

            // Set engine as ready and resume trading
            engine.setTradingHalt(false);

            logger.info("Exchange matching engine initialized and ready for symbol: {}", symbol);

        } catch (Exception e) {
            logger.error("Error initializing exchange matching engine for symbol: {}", symbol, e);
        }
    }

    /**
     * Get order book for symbol
     */
    public Object getOrderBook(String symbol) {
        logger.info("Getting order book for symbol: {}", symbol);

        try {
            ExchangeMatchingEngine engine = engineMap.get(symbol);
            if (engine != null) {
                // Get actual order book from engine
                return engine.getOrderBook();
            } else {
                // Return empty order book if engine not found
                return Map.of(
                        SYMBOL, symbol,
                        "bids", List.of(),
                        "asks", List.of(),
                        TIMESTAMP, System.currentTimeMillis()
                );
            }
        } catch (Exception e) {
            logger.error("Error getting order book for symbol: {}", symbol, e);
            return Map.of(
                    SYMBOL, symbol,
                    "error", e.getMessage(),
                    "timestamp", System.currentTimeMillis()
            );
        }
    }

    // ===== REDIS SNAPSHOT METHODS =====

    /**
     * Save order book snapshot to Redis
     * CRITICAL FIX: Include stop orders in snapshot
     */
    public void saveOrderBookSnapshot(String symbol) {
        try {
            logger.info("Saving order book snapshot for symbol: {}", symbol);

            ExchangeMatchingEngine engine = engineMap.get(symbol);
            if (engine == null) {
                logger.warn("No engine found for symbol: {}, cannot save snapshot", symbol);
                return;
            }

            // Get current order book state from engine
            Object orderBook = engine.getOrderBook();

            //   CRITICAL FIX: Enhance order book with stop orders from stop order manager
            Object enhancedOrderBook = enhanceOrderBookWithStopOrders(orderBook, symbol);

            // Save enhanced snapshot to Redis
            String snapshotId = snapshotService.saveSnapshot(symbol, "SPOT", enhancedOrderBook);

            logger.info("Saved order book snapshot for symbol: {}, snapshotId: {}", symbol, snapshotId);

        } catch (Exception e) {
            logger.error("Error saving order book snapshot for symbol: {}", symbol, e);
        }
    }

    /**
     * CRITICAL FIX: Enhance order book with stop orders from stop order manager
     * Thêm stop orders vào order book trước khi save snapshot
     */
    private Object enhanceOrderBookWithStopOrders(Object orderBook, String symbol) {
        try {
            if (orderBook instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> orderBookMap = new HashMap<>((Map<String, Object>) orderBook);

                // Get stop orders from stop order manager
                List<Object> stopOrdersForSnapshot = getStopOrdersForSnapshot(symbol);

                // Update stop orders in order book
                orderBookMap.put("stopOrders", stopOrdersForSnapshot);

                logger.info("Enhanced order book with {} stop orders for symbol: {}",
                        stopOrdersForSnapshot.size(), symbol);

                return orderBookMap;
            } else {
                logger.warn("Order book is not a Map, cannot enhance with stop orders for symbol: {}", symbol);
                return orderBook;
            }

        } catch (Exception e) {
            logger.error("Error enhancing order book with stop orders for symbol: {}", symbol, e);
            return orderBook; // Return original if enhancement fails
        }
    }

    /**
     * Save snapshots for all active symbols
     */
    public void saveAllSnapshots() {
        try {
            logger.info(" Saving snapshots for all {} active symbols", engineMap.size());

            if (engineMap.isEmpty()) {
                logger.warn("   No active symbols found in engineMap - no snapshots will be saved");
                logger.info("🔧 Available symbols: {}", engineMap.keySet());
                return;
            }

            int savedCount = 0;
            int errorCount = 0;

            for (String symbol : engineMap.keySet()) {
                logger.info(" Saving snapshot for symbol: {}", symbol);
                saveOrderBookSnapshot(symbol);
                savedCount++;
                logger.info("  Saved snapshot for symbol: {}", symbol);
            }

            logger.info(" Completed saving snapshots - Success: {}, Errors: {}, Total: {}",
                    savedCount, errorCount, engineMap.size());

        } catch (Exception e) {
            logger.error(" Critical error saving snapshots for all symbols", e);
        }
    }

    /**
     * Restore snapshot data to matching engine
     */
    private boolean restoreSnapshotToEngine(ExchangeMatchingEngine engine, RedisSnapshotDocument snapshotData, String symbol) {
        try {
            logger.info("Starting snapshot restoration to engine for symbol: {}", symbol);

            if (snapshotData == null) {
                logger.warn("Snapshot data is null for symbol: {}", symbol);
                return false;
            }

            // Validate and extract snapshot data
            SnapshotValidationResult validationResult = validateAndExtractSnapshotData(snapshotData, symbol);
            if (!validationResult.isValid()) {
                return false;
            }

            // Convert orders to required format
            ConvertedOrdersData convertedData = convertSnapshotOrdersToFormat(validationResult, symbol);

            // Begin restore (suppress per-order trade plate sends)
            this.restoreInProgress = true;

            // Restore all orders to engine
            OrderRestorationResult restorationResult = restoreAllOrdersToEngine(engine, convertedData, symbol);

            // End restore
            this.restoreInProgress = false;

            // After restoration, send exactly TWO Kafka messages (BUY and SELL) to exchange-trade-plate
            sendTwoTradePlatesAfterRestoration(symbol);

            return restorationResult.hasRestoredOrders();

        } catch (Exception e) {
            logger.error("Error restoring snapshot to engine for symbol: {}", symbol, e);
            return false;
        }
    }

    /**
     * Validate and extract snapshot data - extracted to reduce complexity
     */
    private SnapshotValidationResult validateAndExtractSnapshotData(RedisSnapshotDocument snapshotData, String symbol) {
        // Extract order data from Redis snapshot document
        Map<String, List<RedisSnapshotDocument.OrderDocument>> buyOrdersMap = snapshotData.getBuyOrders();
        Map<String, List<RedisSnapshotDocument.OrderDocument>> sellOrdersMap = snapshotData.getSellOrders();
        List<RedisSnapshotDocument.OrderDocument> allOrders = snapshotData.getAllOrders();
        List<RedisSnapshotDocument.OrderDocument> stopOrders = snapshotData.getStopOrders();

        int buyOrderCount = buyOrdersMap != null ? buyOrdersMap.values().stream().mapToInt(List::size).sum() : 0;
        int sellOrderCount = sellOrdersMap != null ? sellOrdersMap.values().stream().mapToInt(List::size).sum() : 0;
        int stopOrderCount = stopOrders != null ? stopOrders.size() : 0;

        logger.info(" DEBUG: Snapshot data for symbol {}: {} buy orders, {} sell orders, {} stop orders.",
                symbol, buyOrderCount, sellOrderCount, stopOrderCount);

        if (buyOrderCount == 0 && sellOrderCount == 0 && stopOrderCount == 0) {
            logger.warn("Snapshot for symbol {} is empty. No orders to restore.", symbol);
            return new SnapshotValidationResult(false, null, null, null, null);
        }

        return new SnapshotValidationResult(true, buyOrdersMap, sellOrdersMap, allOrders, stopOrders);
    }

    /**
     * Convert snapshot orders to required format - extracted to reduce complexity
     */
    private ConvertedOrdersData convertSnapshotOrdersToFormat(SnapshotValidationResult validationResult, String symbol) {
        logger.info(" DEBUG: Converting Redis orders to map format...");

        Object buyOrdersObj = convertRedisOrdersToMap(validationResult.getBuyOrdersMap(), symbol);
        Object sellOrdersObj = convertRedisOrdersToMap(validationResult.getSellOrdersMap(), symbol);
        Object allOrdersObj = convertRedisOrdersToList(validationResult.getAllOrders(), symbol);
        Object stopOrdersObj = convertRedisStopOrdersToMap(validationResult.getStopOrders(), symbol);

        logger.info(" DEBUG: Converted buyOrdersObj type: {}, sellOrdersObj type: {}",
                buyOrdersObj.getClass().getSimpleName(), sellOrdersObj.getClass().getSimpleName());

        return new ConvertedOrdersData(buyOrdersObj, sellOrdersObj, allOrdersObj, stopOrdersObj);
    }

    /**
     * Restore all orders to engine - extracted to reduce complexity
     */
    private OrderRestorationResult restoreAllOrdersToEngine(ExchangeMatchingEngine engine, ConvertedOrdersData convertedData, String symbol) {
        int restoredCount = 0;

        // Restore buy limit orders (preserve queue order within each price level)
        restoredCount += restoreOrdersByPriceLevel(engine, convertedData.getBuyOrdersObj(), "BUY_LIMIT");
        // Restore sell limit orders (preserve queue order within each price level)
        restoredCount += restoreOrdersByPriceLevel(engine, convertedData.getSellOrdersObj(), "SELL_LIMIT");

        // Fallback: Restore from allOrders if price-level data not available
        restoredCount += handleFallbackOrderRestoration(engine, convertedData.getAllOrdersObj(), restoredCount);

        // Restore stop orders
        int restoredStopOrdersCount = restoreStopOrdersByPriceLevel(convertedData.getStopOrdersObj(), symbol);

        logger.info("Restored {} regular orders and {} stop orders to engine for symbol: {}",
                restoredCount, restoredStopOrdersCount, symbol);

        return new OrderRestorationResult(restoredCount, restoredStopOrdersCount);
    }

    /**
     * Handle fallback order restoration - extracted to reduce complexity
     */
    private int handleFallbackOrderRestoration(ExchangeMatchingEngine engine, Object allOrdersObj, int currentRestoredCount) {
        if (currentRestoredCount > 0) {
            return 0; // No fallback needed
        }

        List<?> allOrdersList = (List<?>) allOrdersObj;
        if (allOrdersList.isEmpty()) {
            return 0;
        }

        logger.warn("No orders restored from dedicated buy/sell books. Attempting fallback restore from 'allOrders' list ({} orders). This may affect order priority.",
                allOrdersList.size());

        int fallbackRestoredCount = 0;
        for (Object orderObj : allOrdersList) {
            if (restoreOrderToEngine(engine, orderObj)) {
                fallbackRestoredCount++;
            }
        }

        return fallbackRestoredCount;
    }

    // Rebuild trade plates after restoration - extracted to reduce complexity
    // NOTE: Not used in restore flow anymore to avoid duplicate Kafka messages

    /**
     * Send exactly two trade plate messages (BUY and SELL) after full restoration
     */
    private void sendTwoTradePlatesAfterRestoration(String symbol) {
        try {
            logger.info("Sending two trade plate messages after restoration for symbol: {}", symbol);

            // Ensure both plates exist
            tradePlatePublisher.ensureBothTradePlatesExist(symbol);

            // Get both plates; if any is null, create an empty one to still emit exactly one per side
            TradePlate[] pair = tradePlatePublisher.getTradePlatePair(symbol);
            TradePlate buyPlate = (pair != null && pair.length > 0 && pair[0] != null)
                    ? pair[0] : new TradePlate(symbol, SpotOrderDirection.BUY);
            TradePlate sellPlate = (pair != null && pair.length > 1 && pair[1] != null)
                    ? pair[1] : new TradePlate(symbol, SpotOrderDirection.SELL);

            // Send BUY then SELL exactly once
            tradePlatePublisher.sendTradePlateMessage(buyPlate);
            tradePlatePublisher.sendTradePlateMessage(sellPlate);

            logger.info("Sent two trade plates after restore: symbol={}, buyDepth={}, sellDepth={}",
                    symbol, buyPlate.getDepth(), sellPlate.getDepth());

        } catch (Exception e) {
            logger.error("Failed to send two trade plates after restoration for symbol: {}", symbol, e);
        }
    }

    /**
     * Helper class for snapshot validation result
     */
    private static class SnapshotValidationResult {
        private final boolean valid;
        private final Map<String, List<RedisSnapshotDocument.OrderDocument>> buyOrdersMap;
        private final Map<String, List<RedisSnapshotDocument.OrderDocument>> sellOrdersMap;
        private final List<RedisSnapshotDocument.OrderDocument> allOrders;
        private final List<RedisSnapshotDocument.OrderDocument> stopOrders;

        public SnapshotValidationResult(boolean valid,
                                        Map<String, List<RedisSnapshotDocument.OrderDocument>> buyOrdersMap,
                                        Map<String, List<RedisSnapshotDocument.OrderDocument>> sellOrdersMap,
                                        List<RedisSnapshotDocument.OrderDocument> allOrders,
                                        List<RedisSnapshotDocument.OrderDocument> stopOrders) {
            this.valid = valid;
            this.buyOrdersMap = buyOrdersMap;
            this.sellOrdersMap = sellOrdersMap;
            this.allOrders = allOrders;
            this.stopOrders = stopOrders;
        }

        public boolean isValid() {
            return valid;
        }

        public Map<String, List<RedisSnapshotDocument.OrderDocument>> getBuyOrdersMap() {
            return buyOrdersMap;
        }

        public Map<String, List<RedisSnapshotDocument.OrderDocument>> getSellOrdersMap() {
            return sellOrdersMap;
        }

        public List<RedisSnapshotDocument.OrderDocument> getAllOrders() {
            return allOrders;
        }

        public List<RedisSnapshotDocument.OrderDocument> getStopOrders() {
            return stopOrders;
        }
    }

    /**
     * Helper class for converted orders data
     */
    private static class ConvertedOrdersData {
        private final Object buyOrdersObj;
        private final Object sellOrdersObj;
        private final Object allOrdersObj;
        private final Object stopOrdersObj;

        public ConvertedOrdersData(Object buyOrdersObj, Object sellOrdersObj, Object allOrdersObj, Object stopOrdersObj) {
            this.buyOrdersObj = buyOrdersObj;
            this.sellOrdersObj = sellOrdersObj;
            this.allOrdersObj = allOrdersObj;
            this.stopOrdersObj = stopOrdersObj;
        }

        public Object getBuyOrdersObj() {
            return buyOrdersObj;
        }

        public Object getSellOrdersObj() {
            return sellOrdersObj;
        }

        public Object getAllOrdersObj() {
            return allOrdersObj;
        }

        public Object getStopOrdersObj() {
            return stopOrdersObj;
        }
    }

    /**
     * Helper class for order restoration result
     */
    private static class OrderRestorationResult {
        private final int restoredCount;
        private final int restoredStopOrdersCount;

        public OrderRestorationResult(int restoredCount, int restoredStopOrdersCount) {
            this.restoredCount = restoredCount;
            this.restoredStopOrdersCount = restoredStopOrdersCount;
        }

        public boolean hasRestoredOrders() {
            return restoredCount > 0 || restoredStopOrdersCount > 0;
        }

        public int getRestoredCount() {
            return restoredCount;
        }

        public int getRestoredStopOrdersCount() {
            return restoredStopOrdersCount;
        }
    }

    /**
     * CRITICAL FIX: Restore orders by price level to preserve queue order
     * Đảm bảo thứ tự queue trong từng price level được preserve
     */
    private int restoreOrdersByPriceLevel(ExchangeMatchingEngine engine, Object ordersObj, String orderType) {
        try {
            logger.info(" DEBUG: restoreOrdersByPriceLevel called with orderType={}", orderType);

            if (!(ordersObj instanceof Map<?, ?> ordersByPrice)) {
                logger.warn(" DEBUG: ordersObj is not a Map, it's: {}",
                        ordersObj != null ? ordersObj.getClass().getSimpleName() : "null");
                return 0;
            }

            logger.info(" DEBUG: Processing {} price levels for {}", ordersByPrice.size(), orderType);

            // Sort entries by price based on order type
            List<Map.Entry<?, ?>> sortedEntries = sortEntriesByPrice(ordersByPrice, orderType);

            // Restore orders for each price level
            return restoreOrdersFromSortedEntries(engine, sortedEntries, orderType);

        } catch (Exception e) {
            logger.error(" DEBUG: Error restoring {} orders by price level", orderType, e);
            return 0;
        }
    }

    /**
     * Sort price level entries based on order type - extracted to reduce complexity
     */
    private List<Map.Entry<?, ?>> sortEntriesByPrice(Map<?, ?> ordersByPrice, String orderType) {
        List<Map.Entry<?, ?>> sortedEntries = new ArrayList<>(ordersByPrice.entrySet());

        if (orderType.contains("BUY")) {
            // BUY orders: high to low price
            sortedEntries.sort((e1, e2) -> {
                BigDecimal p1 = new BigDecimal(e1.getKey().toString());
                BigDecimal p2 = new BigDecimal(e2.getKey().toString());
                return p2.compareTo(p1); // Descending for BUY
            });
        } else {
            // SELL orders: low to high price
            sortedEntries.sort((e1, e2) -> {
                BigDecimal p1 = new BigDecimal(e1.getKey().toString());
                BigDecimal p2 = new BigDecimal(e2.getKey().toString());
                return p1.compareTo(p2); // Ascending for SELL
            });
        }

        return sortedEntries;
    }

    /**
     * Restore orders from sorted price level entries - extracted to reduce complexity
     */
    private int restoreOrdersFromSortedEntries(ExchangeMatchingEngine engine, List<Map.Entry<?, ?>> sortedEntries, String orderType) {
        int restoredCount = 0;

        for (Map.Entry<?, ?> entry : sortedEntries) {
            String price = entry.getKey().toString();
            Object ordersAtPrice = entry.getValue();

            logger.info(" DEBUG: Processing price level {}", price);

            if (ordersAtPrice instanceof List<?> orderList) {
                restoredCount += restoreOrdersAtPriceLevel(engine, orderList, price, orderType);
            } else {
                logger.warn(" DEBUG: ordersAtPrice is not a List, it's: {}",
                        ordersAtPrice != null ? ordersAtPrice.getClass().getSimpleName() : "null");
            }
        }

        logger.info(" DEBUG: Final restored count for {}: {}", orderType, restoredCount);
        return restoredCount;
    }

    /**
     * Restore orders at a specific price level - extracted to reduce complexity
     */
    private int restoreOrdersAtPriceLevel(ExchangeMatchingEngine engine, List<?> orderList, String price, String orderType) {
        int restoredCount = 0;

        logger.info(" DEBUG: Found {} orders at price {} for {}", orderList.size(), price, orderType);

        // Restore orders in queue order (FIFO)
        for (Object orderObj : orderList) {
            logger.info(" DEBUG: Attempting to restore order: {}",
                    orderObj != null ? orderObj.getClass().getSimpleName() : "null");

            if (restoreOrderToEngine(engine, orderObj)) {
                restoredCount++;
                logger.info(" DEBUG: Successfully restored order, count now: {}", restoredCount);
            } else {
                logger.warn(" DEBUG: Failed to restore order");
            }
        }

        return restoredCount;
    }

    /**
     * Process stop orders grouped by trigger price like BUY/SELL orders are processed by price level
     */
    private int restoreStopOrdersByPriceLevel(Object stopOrdersObj, String symbol) {
        try {
            logger.info(" DEBUG: restoreStopOrdersByPriceLevel called for symbol={}", symbol);

            if (!(stopOrdersObj instanceof Map<?, ?> ordersByTriggerPrice)) {
                logger.warn("Stop orders object is not in Map format: {}",
                        stopOrdersObj != null ? stopOrdersObj.getClass().getSimpleName() : "null");
                return 0;
            }

            logger.info(" DEBUG: Processing {} trigger price levels for stop orders", ordersByTriggerPrice.size());

            // Sort trigger price levels and restore stop orders
            List<Map.Entry<?, ?>> sortedEntries = sortStopOrderEntriesByTriggerPrice(ordersByTriggerPrice);
            int restoredCount = restoreStopOrdersFromSortedEntries(sortedEntries, symbol);

            logger.info(" DEBUG: restoreStopOrdersByPriceLevel completed. Restored {} stop orders for symbol: {}",
                    restoredCount, symbol);
            return restoredCount;

        } catch (Exception e) {
            logger.error("Error restoring stop orders by trigger price level for symbol: {}", symbol, e);
            return 0;
        }
    }

    /**
     * Sort stop order entries by trigger price - extracted to reduce complexity
     */
    private List<Map.Entry<?, ?>> sortStopOrderEntriesByTriggerPrice(Map<?, ?> ordersByTriggerPrice) {
        List<Map.Entry<?, ?>> sortedEntries = new ArrayList<>(ordersByTriggerPrice.entrySet());

        // Sort trigger price levels (ascending order for consistency)
        sortedEntries.sort((e1, e2) -> {
            BigDecimal p1 = new BigDecimal(e1.getKey().toString());
            BigDecimal p2 = new BigDecimal(e2.getKey().toString());
            return p1.compareTo(p2); // Ascending order
        });

        return sortedEntries;
    }

    /**
     * Restore stop orders from sorted trigger price entries - extracted to reduce complexity
     */
    private int restoreStopOrdersFromSortedEntries(List<Map.Entry<?, ?>> sortedEntries, String symbol) {
        int restoredCount = 0;

        for (Map.Entry<?, ?> entry : sortedEntries) {
            String triggerPrice = entry.getKey().toString();
            Object ordersAtTriggerPrice = entry.getValue();

            logger.info(" DEBUG: Processing trigger price level {} with ordersAtTriggerPrice type={}",
                    triggerPrice, ordersAtTriggerPrice != null ? ordersAtTriggerPrice.getClass().getSimpleName() : "null");

            if (ordersAtTriggerPrice instanceof List<?> orderList) {
                restoredCount += restoreStopOrdersAtTriggerPrice(orderList, triggerPrice, symbol);
            } else {
                logger.warn("Orders at trigger price {} are not in List format", triggerPrice);
            }
        }

        return restoredCount;
    }

    /**
     * Restore stop orders at a specific trigger price level - extracted to reduce complexity
     */
    private int restoreStopOrdersAtTriggerPrice(List<?> orderList, String triggerPrice, String symbol) {
        int restoredCount = 0;

        logger.info(" DEBUG: Found {} stop orders at trigger price {}", orderList.size(), triggerPrice);

        // Restore stop orders in queue order (FIFO)
        for (Object orderObj : orderList) {
            logger.info(" DEBUG: Attempting to restore stop order: {}",
                    orderObj != null ? orderObj.getClass().getSimpleName() : "null");

            if (restoreStopOrderFromSnapshot(orderObj, symbol)) {
                restoredCount++;
                logger.info(" DEBUG: Successfully restored stop order, count now: {}", restoredCount);
            } else {
                logger.warn(" DEBUG: Failed to restore stop order at trigger price: {}", triggerPrice);
            }
        }

        return restoredCount;
    }

    /**
     * Restore single order to matching engine
     */
    private boolean restoreOrderToEngine(ExchangeMatchingEngine engine, Object orderObj) {
        try {
            logger.info(" DEBUG: restoreOrderToEngine called with orderObj type: {}",
                    orderObj != null ? orderObj.getClass().getSimpleName() : "null");

            // Convert OrderDocument back to Order object
            Order order = convertOrderDocumentToOrder(orderObj);
            if (order == null) {
                logger.warn(" DEBUG: Failed to convert order document to Order object: {}", orderObj);
                return false;
            }

            logger.info(" DEBUG: Successfully converted to Order: orderId={}, symbol={}, direction={}, type={}",
                    order.getOrderId().getValue(), order.getSymbol().getValue(),
                    order.getDirection(), order.getType());

            // IMPORTANT: Add order directly to order book WITHOUT processing/matching
            // This only rebuilds the order book state, no trades should be generated
            // Convert Order to DTO for new engine
            ExchangeOrder orderDTO = convertOrderToDTO(order);
            logger.info(" DEBUG: Converted to ExchangeOrder DTO: orderId={}, symbol={}, direction={}, type={}",
                    orderDTO.getOrderId(), orderDTO.getSymbol(), orderDTO.getDirection(), orderDTO.getType());

            //   CRITICAL FIX: Check if this is a stop order and handle accordingly
            if (isStopOrderType(orderDTO.getType())) {
                // This is a stop order - add to stop order manager instead of engine
                logger.info(" DEBUG: Detected stop order, adding to stop order manager: {}", orderDTO.getOrderId());
                return restoreStopOrderToManager(orderDTO, order.getSymbol().getValue());
            }

            boolean added = engine.addOrderDirectly(orderDTO);

            if (added) {
                logger.info(" DEBUG: Successfully restored order to engine: {}", order.getOrderId().getValue());

                //   CRITICAL FIX: Initialize both BUY and SELL trade plates for symbol
                // Ensure both trade plates exist in the maps for this symbol
                tradePlatePublisher.ensureBothTradePlatesExist(orderDTO.getSymbol());
                logger.info(" DEBUG: Ensured both trade plates exist for symbol: {}", orderDTO.getSymbol());


                //   REFACTOR: Reuse the same logic as processing new orders
                boolean addedToTradePlate = addOrderToTradePlate(orderDTO, orderDTO.getSymbol());
                if (addedToTradePlate) {
                    logger.info(" DEBUG:   Successfully added restored order to trade plate: orderId={}, symbol={}, direction={}",
                            orderDTO.getOrderId(), orderDTO.getSymbol(), orderDTO.getDirection());
                } else {
                    logger.warn(" DEBUG:   Failed to add restored order to trade plate: orderId={}, direction={}",
                            orderDTO.getOrderId(), orderDTO.getDirection());
                }

                return true;
            } else {
                logger.warn(" DEBUG: Failed to add order {} to order book during restoration",
                        order.getOrderId().getValue());
                return false;
            }

        } catch (Exception e) {
            logger.error(" DEBUG: Error restoring single order to engine", e);
            return false;
        }
    }

//    /**
//     * Check if order should be added to trade plate
//     * Only regular limit orders that are not fully filled should be visible in trade plate
//     * Stop orders should NOT be in trade plate until they are triggered
//     */


    /**
     * Convert OrderDocument back to Order object
     */
    private Order convertOrderDocumentToOrder(Object orderObj) {
        try {
            logger.info(" DEBUG: convertOrderDocumentToOrder called with type: {}",
                    orderObj != null ? orderObj.getClass().getSimpleName() : "null");

            if (orderObj == null) {
                logger.warn(" DEBUG: orderObj is null");
                return null;
            }

            // Handle Map format (from MongoDB OrderDocument)
            if (orderObj instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> orderMap = (Map<String, Object>) orderObj;
                logger.info(" DEBUG: Processing Map with keys: {}", orderMap.keySet());
                return convertMapToOrder(orderMap);
            }

            // Handle direct Order object
            if (orderObj instanceof Order order) {
                logger.info(" DEBUG: Direct Order object, returning as-is");
                return order;
            }

            logger.warn(" DEBUG: Unsupported order object type: {}", orderObj.getClass().getSimpleName());
            return null;

        } catch (Exception e) {
            logger.error(" DEBUG: Error converting order document to Order", e);
            return null;
        }
    }

    /**
     * Convert Map to Order object
     */
    private Order convertMapToOrder(Map<String, Object> orderMap) {
        try {
            // Extract required fields
            String orderId = extractStringField(orderMap, ORDER_ID);
            Long memberId = extractLongField(orderMap, MEMBER_ID);
            String symbol = extractStringField(orderMap, SYMBOL);
            String direction = extractStringField(orderMap, DIRECTION);
            String type = extractStringField(orderMap, TYPE);
            String status = extractStringField(orderMap, STATUS);

            // Extract Money fields
            Money price = extractMoneyField(orderMap, PRICE);
            Money quantity = extractMoneyField(orderMap, QUANTITY);
            Money filledQuantity = extractMoneyField(orderMap, FILLED_QUANTITY);

            // Extract timestamp
            Long createdAtMillis = extractLongField(orderMap, CREATED_TIME);
            Instant createdAt = createdAtMillis != null ?
                    Instant.ofEpochMilli(createdAtMillis) : Instant.now();

            logger.info("convertMapToOrder - Extracted fields: orderId={}, memberId={}, symbol={}, direction={}, type={}, status={}, price={}, quantity={}, filledQuantity={}, createdAt={}",
                    orderId, memberId, symbol, direction, type, status, price, quantity, filledQuantity, createdAt);

            // Validate required fields
            if (orderId == null || memberId == null || symbol == null ||
                    direction == null || type == null || quantity == null) {
                logger.error(" DEBUG: Missing required fields in order map (orderId: {}, memberId: {}, symbol: {}, direction: {}, type: {}, quantity: {}). Full map: {}",
                        orderId, memberId, symbol, direction, type, quantity, orderMap);
                return null;
            }

            logger.info(" DEBUG: All required fields present - orderId: {}, memberId: {}, symbol: {}, direction: {}, type: {}, quantity: {}",
                    orderId, memberId, symbol, direction, type, quantity);

            // Convert using spot enums only for Exchange compatibility
            OrderType orderType;
            SpotOrderType spotType = SpotOrderType.fromExchangeCode(type);
            orderType = spotType.toOrderType();
            logger.info(" DEBUG: Converted spot type {} to domain type {}", type, orderType);


            OrderDirection orderDirection;
            SpotOrderDirection spotDirection = SpotOrderDirection.fromExchangeCode(direction);
            orderDirection = spotDirection.toOrderDirection();
            logger.info(" DEBUG: Converted spot direction {} to domain direction {}", direction, orderDirection);


            OrderStatus orderStatus = OrderStatus.NEW; // Default status
            if (status != null) {
                SpotOrderStatus spotStatus = SpotOrderStatus.fromExchangeCode(status);
                orderStatus = spotStatus.toOrderStatus();
                logger.info(" DEBUG: Converted spot status {} to domain status {}", status, orderStatus);
            }

            // Build Order object
            return Order.builder()
                    .orderId(OrderId.of(orderId))
                    .memberId(memberId)
                    .symbol(Symbol.of(symbol))
                    .direction(orderDirection)
                    .type(orderType)
                    .price(price != null ? price : Money.ZERO)
                    .size(quantity)
                    .filledSize(filledQuantity != null ? filledQuantity : Money.ZERO)
                    .status(orderStatus)
                    .timestamp(createdAt)
                    .build();

        } catch (Exception e) {
            logger.error("Error converting map to Order: {}", orderMap, e);
            return null;
        }
    }

    /**
     * Extract String field from map
     */
    private String extractStringField(Map<String, Object> map, String fieldName) {
        Object value = map.get(fieldName);
        return value != null ? value.toString() : null;
    }

    /**
     * Extract Long field from map
     */
    private Long extractLongField(Map<String, Object> map, String fieldName) {
        Object value = map.get(fieldName);
        if (value == null) return null;

        if (value instanceof Long longValue) return longValue;
        if (value instanceof Integer integer) return integer.longValue();
        if (value instanceof String string) {
            try {
                return Long.parseLong(string);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    /**
     * Extract Money field from map
     */
    private Money extractMoneyField(Map<String, Object> map, String fieldName) {
        Object value = map.get(fieldName);
        if (value == null) return null;

        try {
            if (value instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> moneyMap = (Map<String, Object>) value;
                Object amountObj = moneyMap.get(AMOUNT);
                if (amountObj != null) {
                    BigDecimal amount = new BigDecimal(amountObj.toString());
                    return Money.of(amount);
                }
            } else if (value instanceof Number) {
                BigDecimal amount = new BigDecimal(value.toString());
                return Money.of(amount);
            } else if (value instanceof String string) {
                BigDecimal amount = new BigDecimal(string);
                return Money.of(amount);
            }
        } catch (Exception e) {
            logger.warn("Error extracting Money field {}: {}", fieldName, e.getMessage());
        }

        return null;
    }

    /**
     * Convert Order to ExchangeOrder for new engine
     */
    private ExchangeOrder convertOrderToDTO(Order order) {
        // Map OrderDirection to SpotOrderDirection
        SpotOrderDirection direction;
        if (order.getDirection() == com.icetea.lotus.matching.domain.enums.OrderDirection.BUY) {
            direction = SpotOrderDirection.BUY;
        } else {
            direction = SpotOrderDirection.SELL;
        }

        // Map OrderType to SpotOrderType
        SpotOrderType type;
        if (order.getType() == com.icetea.lotus.matching.domain.enums.OrderType.LIMIT) {
            type = SpotOrderType.LIMIT_PRICE;
        } else {
            type = SpotOrderType.MARKET_PRICE;
        }

        // Map OrderStatus to SpotOrderStatus
        SpotOrderStatus status = SpotOrderStatus.TRADING; // Default for active orders

        return ExchangeOrder.builder()
                .orderId(order.getOrderId().getValue())
                .symbol(order.getSymbol().getValue())
                .memberId(order.getMemberId())
                .direction(direction)
                .type(type)
                .status(status)
                .price(order.getPrice() != null ? order.getPrice().getAmount() : null)
                .stopPrice(order.getStopPrice() != null ? order.getStopPrice().getAmount() : null) //   Preserve stop price
                .amount(order.getSize().getAmount())
                .time(order.getTimestamp().toEpochMilli())
                .build();
    }

    /**
     * Extract BigDecimal field from map
     */
    private BigDecimal extractBigDecimalField(Map<String, Object> map, String fieldName) {
        Object value = map.get(fieldName);
        if (value instanceof BigDecimal bigDecimal) {
            return bigDecimal;
        } else if (value instanceof Number) {
            return new BigDecimal(value.toString());
        } else if (value instanceof String string) {
            return new BigDecimal(string);
        }
        return null;
    }

    /**
     * Extract traded amount from trade object for specific order
     */
    private BigDecimal extractTradedAmountFromTrade(Object tradeObj, String orderId) {
        try {
            if (tradeObj instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> tradeMap = (Map<String, Object>) tradeObj;

                // Check if this trade involves the specified order
                String buyOrderId = extractStringField(tradeMap, "buyOrderId");
                String sellOrderId = extractStringField(tradeMap, "sellOrderId");

                if (orderId.equals(buyOrderId) || orderId.equals(sellOrderId)) {
                    return extractBigDecimalField(tradeMap, AMOUNT);
                }
            }
            return null;
        } catch (Exception e) {
            logger.error("Error extracting traded amount from trade: {}", tradeObj, e);
            return null;
        }
    }

    /**
     * Check if order is fully filled based on trades
     */
    private boolean isOrderFullyFilled(ExchangeOrder orderDTO, List<Object> trades) {
        try {
            BigDecimal totalTradedAmount = BigDecimal.ZERO;

            for (Object tradeObj : trades) {
                BigDecimal tradedAmount = extractTradedAmountFromTrade(tradeObj, orderDTO.getOrderId());
                if (tradedAmount != null) {
                    totalTradedAmount = totalTradedAmount.add(tradedAmount);
                }
            }
            return totalTradedAmount.compareTo(orderDTO.getAmount()) >= 0;
        } catch (Exception e) {
            logger.error("Error checking if order is fully filled: {}", orderDTO.getOrderId(), e);
            return false;
        }
    }

    /**
     * Convert cancel request to ExchangeOrder for direct usage
     */
    private ExchangeOrder convertCancelRequestToExchangeOrder(ExchangeOrderMessage cancelRequest, String orderId, String symbol) {
        try {
            // Create ExchangeOrder for direct usage
            ExchangeOrder exchangeOrder = new ExchangeOrder();
            exchangeOrder.setOrderId(orderId);
            exchangeOrder.setSymbol(symbol);
            exchangeOrder.setMemberId(cancelRequest.getMemberId());
            exchangeOrder.setDirection(cancelRequest.getDirection());
            exchangeOrder.setType(cancelRequest.getType());
            exchangeOrder.setPrice(cancelRequest.getPrice());
            exchangeOrder.setAmount(cancelRequest.getAmount());
            exchangeOrder.setTradedAmount(cancelRequest.getTradedAmount());
            exchangeOrder.setTime(System.currentTimeMillis());

            return exchangeOrder;

        } catch (Exception e) {
            logger.error("Error converting cancel request to ExchangeOrder: orderId={}, symbol={}", orderId, symbol, e);
            return null;
        }
    }

    /**
     * Get trade plate for symbol and direction
     * CRITICAL FIX: Expose trade plates for monitoring APIs with auto-rebuild
     */
    public TradePlate getTradePlate(String symbol, SpotOrderDirection direction) {
        try {
            TradePlate tradePlate = tradePlatePublisher.getTradePlate(symbol, direction);

            logger.info(" DEBUG: getTradePlate() called - symbol: {}, direction: {}, isEmpty: {}, items: {}, instance: {}",
                    symbol, direction, tradePlate.isEmpty(), tradePlate.getItems().size(), System.identityHashCode(tradePlate));

            // CRITICAL FIX: If trade plate is empty, try to rebuild from order book
            if (tradePlate.isEmpty()) {
                logger.info("Trade plate is empty for symbol: {}, direction: {}. Rebuilding from order book.",
                        symbol, direction);
                rebuildTradePlateFromOrderBook(symbol, direction);
                // Get the rebuilt trade plate
                tradePlate = tradePlatePublisher.getTradePlate(symbol, direction);
                logger.info(" DEBUG: After rebuild - symbol: {}, direction: {}, isEmpty: {}, items: {}, instance: {}",
                        symbol, direction, tradePlate.isEmpty(), tradePlate.getItems().size(), System.identityHashCode(tradePlate));
            }

            return tradePlate;
        } catch (Exception e) {
            logger.error("Error getting trade plate for symbol: {}, direction: {}", symbol, direction, e);
            return new TradePlate(symbol, direction); // Return empty trade plate
        }
    }

    /**
     * Get both buy and sell trade plates for symbol
     * CRITICAL FIX: Expose trade plates for monitoring APIs
     */
    public TradePlate[] getTradePlatePair(String symbol) {
        try {
            return tradePlatePublisher.getTradePlatePair(symbol);
        } catch (Exception e) {
            logger.error("Error getting trade plate pair for symbol: {}", symbol, e);
            return new TradePlate[]{
                    new TradePlate(symbol, SpotOrderDirection.BUY),
                    new TradePlate(symbol, SpotOrderDirection.SELL)
            };
        }
    }

    /**
     * Rebuild trade plate from order book
     * CRITICAL FIX: Restore trade plates after service restart
     */
    private void rebuildTradePlateFromOrderBook(String symbol, SpotOrderDirection direction) {
        try {
            logger.info("Rebuilding trade plate from order book for symbol: {}, direction: {}", symbol, direction);

            ExchangeMatchingEngine engine = engineMap.get(symbol);
            if (engine == null) {
                logger.warn("No matching engine found for symbol: {}", symbol);
                return;
            }

            // Get order book data
            Object orderBookData = engine.getOrderBook();
            if (orderBookData == null) {
                logger.warn("No order book data found for symbol: {}", symbol);
                return;
            }

            // Rebuild trade plate using TradePlatePublisher
            tradePlatePublisher.rebuildTradePlateFromOrderBookData(symbol, direction, orderBookData);

            logger.info("Successfully rebuilt trade plate for symbol: {}, direction: {}", symbol, direction);

        } catch (Exception e) {
            logger.error("Failed to rebuild trade plate for symbol: {}, direction: {}", symbol, direction, e);
        }
    }

    /**
     * REFACTOR: Shared helper method to add order to trade plate
     * Reused by both new order processing and restore flows
     */
    private boolean addOrderToTradePlate(ExchangeOrder exchangeOrder, String symbol) {
        try {
            // Get trade plate for the order's direction
            TradePlate orderTradePlate = tradePlatePublisher.getTradePlate(symbol, exchangeOrder.getDirection());

            // Add order to trade plate using standard logic
            boolean added = orderTradePlate.add(exchangeOrder);

            if (added) {
                // During restore, DO NOT send per-order messages to avoid Kafka spam
                if (!restoreInProgress) {
                    tradePlatePublisher.sendTradePlateMessage(orderTradePlate);
                    logger.info("Added order to trade plate and sent update: symbol={}, orderId={}, direction={}",
                            symbol, exchangeOrder.getOrderId(), exchangeOrder.getDirection());
                } else {
                    logger.info("[RESTORE] Added order to trade plate without sending: symbol={}, orderId={}, direction={}",
                            symbol, exchangeOrder.getOrderId(), exchangeOrder.getDirection());
                }
            }

            return added;

        } catch (Exception e) {
            logger.error("Error adding order to trade plate: symbol={}, orderId={}, direction={}",
                    symbol, exchangeOrder.getOrderId(), exchangeOrder.getDirection(), e);
            return false;
        }
    }

    /**
     * Handle stop order processing - add to stop order manager for monitoring
     *
     * @param exchangeOrder Stop order to process
     * @return ExchangeTradeResult indicating success or failure
     */
    private ExchangeTradeResult handleStopOrder(ExchangeOrder exchangeOrder) {
        try {
            logger.info("Processing stop order: {} for symbol: {}, type: {}, stopPrice: {}",
                    exchangeOrder.getOrderId(), exchangeOrder.getSymbol(),
                    exchangeOrder.getType(), exchangeOrder.getStopPrice());

            // Convert ExchangeOrder to SimpleStopOrder for stop order manager
            SimpleStopOrder stopOrder = convertToSimpleStopOrder(exchangeOrder);
            if (stopOrder == null) {
                return ExchangeTradeResult.error("Failed to convert exchange order to stop order: " + exchangeOrder.getOrderId());
            }

            // Get current market price for sign initialization
            BigDecimal currentPrice = getLastTradePriceFromCache(exchangeOrder.getSymbol());

            // 🎯 STRATEGY DETECTION: Automatically detect optimal strategy
            StopOrderStrategy detectedStrategy = StopOrderStrategyDetector.detectOptimalStrategy(
                    stopOrder.getStopOrderType(),
                    stopOrder.getDirection(),
                    stopOrder.getTriggerPrice(),
                    currentPrice != null ? Money.of(currentPrice) : Money.ZERO
            );

            // Log strategy detection reasoning
            String strategyReason = StopOrderStrategyDetector.getStrategyReason(
                    stopOrder.getStopOrderType(),
                    stopOrder.getDirection(),
                    stopOrder.getTriggerPrice(),
                    currentPrice != null ? Money.of(currentPrice) : Money.ZERO, //   FIX: Handle null currentPrice
                    stopOrder.getSymbol(),
                    detectedStrategy
            );
            logger.info("🎯 STRATEGY DETECTED for order {}: {}", exchangeOrder.getOrderId(), strategyReason);

            // Add stop order to manager with detected strategy
            boolean added = stopOrderManager.addStopOrder(stopOrder, currentPrice, detectedStrategy, CommonConstance.SPOT);
            if (!added) {
                return ExchangeTradeResult.error("Failed to add stop order to manager: " + exchangeOrder.getOrderId());
            }

            logger.info("Successfully added stop order to manager: {} for symbol: {}, currentPrice: {}, strategy: {}",
                    exchangeOrder.getOrderId(), exchangeOrder.getSymbol(), currentPrice, detectedStrategy);

            // Return success result with no immediate trades (stop order is waiting)
            return ExchangeTradeResult.builder()
                    .success(true)
                    .orderId(exchangeOrder.getOrderId())
                    .symbol(exchangeOrder.getSymbol())
                    .message("Stop order added to monitoring queue")
                    .trades(List.of())
                    .completedOrders(List.of())
                    .build();

        } catch (Exception e) {
            logger.error("Error handling stop order: {} - {}", exchangeOrder.getOrderId(), e.getMessage(), e);
            return ExchangeTradeResult.error("Error processing stop order: " + e.getMessage());
        }
    }

    /**
     * Convert ExchangeOrder to SimpleStopOrder
     */
    public SimpleStopOrder convertToSimpleStopOrder(ExchangeOrder exchangeOrder) {
        try {
            // Create Member object from memberId
            Member member = Member.builder()
                    .id(exchangeOrder.getMemberId())
                    .build();

            OrderDirection direction = convertToOrderDirection(exchangeOrder.getDirection());
            Money triggerPrice = Money.of(exchangeOrder.getStopPrice());
            Symbol symbol = Symbol.of(exchangeOrder.getSymbol());

            // Get current price for strategy detection
            BigDecimal currentPriceBD = getCurrentMarketPrice(symbol.getValue());
            Money currentPrice = (currentPriceBD != null) ? Money.of(currentPriceBD) : Money.ZERO;

            // Auto-detect strategy based on stop order type and direction (with current price for STOP_MARKET)
            StopOrderType stopOrderType = convertToStopOrderType(
                    exchangeOrder.getType(),
                    exchangeOrder.getDirection(),
                    exchangeOrder.getStopPrice(),
                    currentPriceBD  // Pass current price for STOP_MARKET detection
            );

            // Detect optimal strategy
            StopOrderStrategy strategy = StopOrderStrategyDetector.detectOptimalStrategy(
                    stopOrderType, direction, triggerPrice, currentPrice);

            return SimpleStopOrder.builder()
                    .orderId(OrderId.of(exchangeOrder.getOrderId()))
                    .member(member)
                    .symbol(symbol)
                    .direction(direction)
                    .stopOrderType(stopOrderType)
                    .strategy(strategy)  // Set detected strategy
                    .quantity(Money.of(exchangeOrder.getAmount()))
                    .executionPrice(exchangeOrder.getPrice() != null ? Money.of(exchangeOrder.getPrice()) : null)
                    .triggerPrice(triggerPrice)
                    .timestamp(exchangeOrder.getTime())
                    .status(StopOrderStatus.NEW)
                    .build();

        } catch (Exception e) {
            logger.error("Error converting ExchangeOrder to SimpleStopOrder: {} - {}",
                    exchangeOrder.getOrderId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * Convert SpotOrderDirection to OrderDirection
     */
    private OrderDirection convertToOrderDirection(SpotOrderDirection spotDirection) {
        return switch (spotDirection) {
            case BUY -> OrderDirection.BUY;
            case SELL -> OrderDirection.SELL;
        };
    }

    /**
     * Convert SpotOrderType to StopOrderType with intelligent detection
     * Phân biệt SELL_RALLY_LIMIT, BUY_DIP_LIMIT dựa trên direction và price relationship
     * CRITICAL FIX: Handle triggered stop orders that became LIMIT_PRICE/MARKET_PRICE
     */
    private StopOrderType convertToStopOrderType(SpotOrderType spotType, SpotOrderDirection direction,
                                                 BigDecimal stopPrice,
                                                 BigDecimal currentPrice) {
        return switch (spotType) {
            case STOP_LIMIT -> detectStopLimitVariant(direction, stopPrice);
            case STOP_MARKET -> detectStopMarketVariant(direction, stopPrice, currentPrice);

            //   CRITICAL FIX: Handle triggered stop orders
            case LIMIT_PRICE -> {
                // If this order has a stop price, it's a triggered stop order
                if (stopPrice != null) {
                    logger.info("Converting triggered stop order (LIMIT_PRICE with stop_price) to STOP_LIMIT variant");
                    yield detectStopLimitVariant(direction, stopPrice);
                } else {
                    // This is a regular limit order mistakenly processed as stop order
                    logger.warn("LIMIT_PRICE order without stop_price should not be processed as stop order");
                    throw new IllegalArgumentException("LIMIT_PRICE without stop_price is not a stop order type");
                }
            }

            case MARKET_PRICE -> {
                // If this order has a stop price, it's a triggered stop order
                if (stopPrice != null) {
                    logger.info("Converting triggered stop order (MARKET_PRICE with stop_price) to STOP_MARKET variant");
                    yield detectStopMarketVariant(direction, stopPrice, currentPrice);
                } else {
                    // This is a regular market order mistakenly processed as stop order
                    logger.warn("MARKET_PRICE order without stop_price should not be processed as stop order");
                    throw new IllegalArgumentException("MARKET_PRICE without stop_price is not a stop order type");
                }
            }
        };
    }

    /**
     * Detect specific STOP_LIMIT variant based on price relationship
     * Strategy detection based on stop price vs current price context (not limit price)
     */
    private StopOrderType detectStopLimitVariant(SpotOrderDirection direction,
                                                 BigDecimal stopPrice) {
        if (stopPrice == null) {
            return StopOrderType.STOP_LIMIT; // Fallback to basic STOP_LIMIT
        }

        // 🎯 STRATEGY DETECTION based on stop price vs current price context
        // Get current market price to determine strategy context
        try {
            BigDecimal currentPrice = getCurrentMarketPrice(null); // Get general market context
            if (currentPrice != null) {
                return detectStopLimitVariantByPriceContext(direction, stopPrice, currentPrice);
            }
        } catch (Exception e) {
            logger.info("Could not get current market price for strategy detection, using default STOP_LIMIT");
        }

        // Default fallback: Always return STOP_LIMIT for consistent behavior
        // This ensures all BUY/SELL STOP_LIMIT orders behave the same way when no price context available
        return StopOrderType.STOP_LIMIT;
    }

    /**
     * Detect STOP_LIMIT variant based on stop price vs current price relationship
     */
    private StopOrderType detectStopLimitVariantByPriceContext(SpotOrderDirection direction,
                                                               BigDecimal stopPrice, BigDecimal currentPrice) {
        if (direction == SpotOrderDirection.SELL) {
            if (currentPrice.compareTo(stopPrice) > 0) {
                return StopOrderType.SELL_RALLY_LIMIT;
            }
        } else if (direction == SpotOrderDirection.BUY && currentPrice.compareTo(stopPrice) < 0) {
            return StopOrderType.BUY_DIP_LIMIT;
        }

        return StopOrderType.STOP_LIMIT; // Default traditional stop limit
    }

    /**
     * Detect specific STOP_MARKET variant based on direction and current price context
     * Strategy detection based on stop price vs current price relationship
     */
    private StopOrderType detectStopMarketVariant(SpotOrderDirection direction,
                                                  BigDecimal stopPrice, BigDecimal currentPrice) {
        if (stopPrice == null) {
            return StopOrderType.STOP_MARKET; // Fallback
        }

        // 🎯 STRATEGY DETECTION based on stop price vs current price relationship
        try {
            if (currentPrice != null) {

                if (direction == SpotOrderDirection.SELL) {
                    if (currentPrice.compareTo(stopPrice) > 0) {
                        return StopOrderType.SELL_RALLY_MARKET;
                    }
                } else if (direction == SpotOrderDirection.BUY && currentPrice.compareTo(stopPrice) < 0) {
                    return StopOrderType.BUY_DIP_MARKET;
                }
            }

        } catch (Exception e) {
            logger.warn("Error detecting STOP_MARKET variant for direction: {}, stopPrice: {}, currentPrice: {}",
                    direction, stopPrice, currentPrice, e);
        }

        return StopOrderType.STOP_MARKET; // Default traditional stop market
    }

    /**
     * Get current market price for symbol from cache
     * Returns cached last trade price or null if no trades yet
     */
    private BigDecimal getCurrentMarketPrice(String symbol) {
        try {
            // Get cached last trade price for symbol
            BigDecimal cachedPrice = lastTradePriceCache.get(symbol);
            if (cachedPrice != null) {
                logger.info("Using cached last trade price for symbol: {} = {}", symbol, cachedPrice);
                return cachedPrice;
            }

            logger.info("No cached price found for symbol: {}, will use stop price as fallback", symbol);
            return null;

        } catch (Exception e) {
            logger.warn("Error getting current market price for symbol: {} - {}", symbol, e.getMessage());
            return null;
        }
    }

    /**
     * Update last trade price cache for symbol
     * CRITICAL FIX: Made public để có thể gọi từ ExchangeMatchingEngine
     */
    public void updateLastTradePriceCache(String symbol, BigDecimal price) {
        if (symbol != null && price != null && price.compareTo(BigDecimal.ZERO) > 0) {
            // Update in-memory cache
            lastTradePriceCache.put(symbol, price);
            logger.info("Updated last trade price cache for symbol: {} = {}", symbol, price);
        }
    }

    /**
     * CRITICAL FIX: Khôi phục last trade price từ database khi restart
     * Tìm trade gần nhất để khôi phục cache với multiple sources
     */
    public void restoreLastTradePriceFromDatabase(String symbol) {
        try {
            logger.info("Restoring last trade price from database for symbol: {}", symbol);

            BigDecimal lastPrice;

            //   CRITICAL FIX: Try last_price collection first (faster)
            lastPrice = queryLastPriceFromCollection(symbol);

            if (lastPrice != null) {
                // Khôi phục vào cache
                lastTradePriceCache.put(symbol, lastPrice);

                //   CRITICAL FIX: Sync last price to LastPriceService for stop order initialization
                lastPriceService.updateLastPrice(symbol, lastPrice, CommonConstance.SPOT);

                logger.info("Restored last trade price for symbol: {} = {}", symbol, lastPrice);
            } else {
                logger.info("No trade history found for symbol: {}, cache will remain empty", symbol);
            }

        } catch (Exception e) {
            logger.error("Error restoring last trade price for symbol: {}", symbol, e);
        }
    }

    /**
     * CRITICAL FIX: Query last price từ last_price collection (faster)
     * Ưu tiên query từ collection riêng trước khi fallback sang trade history
     */
    private BigDecimal queryLastPriceFromCollection(String symbol) {
        try {
            String collectionName = "spot_last_price";

            Query query = new Query(Criteria.where("symbol").is(symbol));
            Map<String, Object> lastPriceDoc = mongoTemplate.findOne(query, Map.class, collectionName);

            if (lastPriceDoc != null) {
                Object priceObj = lastPriceDoc.get(PRICE);
                if (priceObj != null) {
                    BigDecimal price = new BigDecimal(priceObj.toString());
                    logger.info("Found last price in collection for symbol: {} = {}", symbol, price);
                    return price;
                }
            }

            logger.info("No last price found in collection for symbol: {}", symbol);
            return null;

        } catch (Exception e) {
            logger.warn("Error querying last price from collection for symbol: {} - {}", symbol, e.getMessage());
            return null;
        }
    }

    /**
     * CRITICAL FIX: Khôi phục stop order từ snapshot
     * Convert OrderDocument từ snapshot thành SimpleStopOrder và add vào MatchingEngineStopOrderManager
     */
    private boolean restoreStopOrderFromSnapshot(Object orderObj, String symbol) {
        try {
            if (orderObj == null) {
                return false;
            }

            // Convert OrderDocument to Map
            Map<String, Object> orderMap;
            if (orderObj instanceof Map) {
                orderMap = (Map<String, Object>) orderObj;
            } else {
                logger.warn("Stop order object is not a Map: {}", orderObj.getClass().getSimpleName());
                return false;
            }

            //   FIXED: Extract basic order information with correct field names
            String orderId = (String) orderMap.get(ORDER_ID);  // Fixed: use "order_id" not "orderId"
            if (orderId == null || orderId.trim().isEmpty()) {
                logger.warn("Stop order missing order_id");
                return false;
            }

            Long memberId = null;
            Object memberIdObj = orderMap.get(MEMBER_ID);  // Fixed: use "member_id" not "memberId"
            if (memberIdObj != null) {
                memberId = Long.parseLong(memberIdObj.toString());
            }

            String direction = (String) orderMap.get(DIRECTION);
            String type = (String) orderMap.get(TYPE);
            String status = (String) orderMap.get(STATUS);

            //   FIXED: Extract price information with correct field names
            BigDecimal price = extractPriceFromOrderMap(orderMap, PRICE);
            BigDecimal quantity = extractPriceFromOrderMap(orderMap, QUANTITY);
            BigDecimal triggerPrice = extractPriceFromOrderMap(orderMap, STOP_PRICE);  // Fixed: use "stop_price" not "triggerPrice"

            // Validate required fields for stop orders
            if (direction == null || type == null || quantity == null) {
                logger.warn("Stop order missing required fields: orderId={}, direction={}, type={}, quantity={}",
                        orderId, direction, type, quantity);
                return false;
            }

            //   CRITICAL FIX: Enhanced stop order detection
            if (!isStopOrderType(type, triggerPrice)) {
                logger.info("Order {} is not a stop order (type={}, triggerPrice={}), skipping", orderId, type, triggerPrice);
                return false;
            }

            //   CRITICAL FIX: Handle both SpotOrderType and StopOrderType in snapshot
            SpotOrderType spotOrderType = convertStopOrderTypeToSpotOrderType(type);
            if (spotOrderType == null) {
                logger.warn("Cannot convert stop order type '{}' to SpotOrderType for order: {}", type, orderId);
                return false;
            }

            //   CRITICAL FIX: Parse original StopOrderType for sign recovery
            StopOrderType originalStopOrderType = parseOriginalStopOrderType(type);

            // Create ExchangeOrder for conversion
            ExchangeOrder exchangeOrder = ExchangeOrder.builder()
                    .orderId(orderId)
                    .symbol(symbol)
                    .memberId(memberId)
                    .direction(SpotOrderDirection.valueOf(direction))
                    .type(spotOrderType)
                    .status(convertStatusToSpotOrderStatus(status))
                    .price(price)
                    .amount(quantity)
                    .time(System.currentTimeMillis())
                    .build();

            // Convert to SimpleStopOrder manually (since convertExchangeOrderToStopOrder expects different format)
            String strategyStr = (String) orderMap.get("strategy");
            StopOrderStrategy strategy = strategyStr != null ? StopOrderStrategy.valueOf(strategyStr) : StopOrderStrategy.TRADITIONAL;

            logger.info(" DEBUG: Creating stop order from snapshot - orderId={}, triggerPrice={}, strategy={}",
                    orderId, triggerPrice, strategy);

            SimpleStopOrder stopOrder = createStopOrderFromSnapshot(exchangeOrder, triggerPrice, symbol, strategy, originalStopOrderType);
            if (stopOrder == null) {
                logger.warn("Failed to convert ExchangeOrder to SimpleStopOrder: {}", orderId);
                return false;
            }

            logger.info(" DEBUG: Created SimpleStopOrder successfully: {}", stopOrder);

            // Add to stop order manager
            boolean added = stopOrderManager.addStopOrder(stopOrder, null, CommonConstance.SPOT); // No current price during restore
            if (added) {
                logger.info("Restored stop order from snapshot: {} for symbol: {}", orderId, symbol);
                return true;
            } else {
                logger.warn("Failed to add restored stop order to manager: {}", orderId);
                return false;
            }

        } catch (Exception e) {
            logger.error("Error restoring stop order from snapshot", e);
            return false;
        }
    }

    /**
     * CRITICAL FIX: Convert StopOrderType string to SpotOrderType
     * Handle both SpotOrderType and StopOrderType values in snapshot
     * Preserve original StopOrderType for sign recovery
     */
    private SpotOrderType convertStopOrderTypeToSpotOrderType(String type) {
        if (type == null) {
            return null;
        }

        try {
            // First try direct SpotOrderType conversion
            return SpotOrderType.valueOf(type);
        } catch (IllegalArgumentException e) {
            // If not a SpotOrderType, try to map from StopOrderType
            logger.info("Type '{}' is not a SpotOrderType, attempting StopOrderType mapping", type);

            return switch (type) {
                //   CRITICAL: Map StopOrderType values to base SpotOrderType
                // BUY_DIP_* and SELL_RALLY_* are signs, base type determines execution
                case "BUY_DIP_LIMIT", "SELL_RALLY_LIMIT", "STOP_LOSS", "TAKE_PROFIT" -> SpotOrderType.STOP_LIMIT;
                case "BUY_DIP_MARKET", "SELL_RALLY_MARKET" -> SpotOrderType.STOP_MARKET;
                case "STOP_LIMIT" -> SpotOrderType.STOP_LIMIT;
                case "STOP_MARKET" -> SpotOrderType.STOP_MARKET;

                // Handle triggered stop orders
                case LIMIT_PRICE -> SpotOrderType.LIMIT_PRICE;
                case MARKET_PRICE -> SpotOrderType.MARKET_PRICE;

                default -> {
                    logger.warn("Unknown stop order type: {}", type);
                    yield null;
                }
            };
        }
    }

    /**
     * CRITICAL FIX: Parse original StopOrderType from snapshot
     * Recover the exact StopOrderType (including sign) from snapshot data
     */
    private StopOrderType parseOriginalStopOrderType(String type) {
        if (type == null) {
            return StopOrderType.STOP_LIMIT; // Default fallback
        }

        try {
            // Try to parse as StopOrderType directly
            return StopOrderType.valueOf(type);
        } catch (IllegalArgumentException e) {
            // If not a StopOrderType, map from SpotOrderType to default StopOrderType
            logger.info("Type '{}' is not a StopOrderType, mapping to default", type);

            return switch (type) {
                case "STOP_LIMIT", LIMIT_PRICE -> StopOrderType.STOP_LIMIT;
                case "STOP_MARKET", MARKET_PRICE -> StopOrderType.STOP_MARKET;
                default -> StopOrderType.STOP_LIMIT; // Safe default
            };
        }
    }

    /**
     * CRITICAL FIX: Enhanced stop order type detection
     * Check if order type and trigger price indicate this is a stop order
     */
    private boolean isStopOrderType(String type, BigDecimal triggerPrice) {
        if (type == null) {
            return false;
        }

        // Explicit stop order types (both SpotOrderType and StopOrderType)
        if (type.contains("STOP") || type.contains("DIP") || type.contains("RALLY")) {
            return true;
        }

        // Triggered stop orders (LIMIT_PRICE or MARKET_PRICE with stop_price)
        if ((LIMIT_PRICE.equals(type) || MARKET_PRICE.equals(type)) && triggerPrice != null) {
            logger.info("Detected triggered stop order: type={}, triggerPrice={}", type, triggerPrice);
            return true;
        }

        return false;
    }

    /**
     * Extract price from order map (handle both MoneyDocument and direct BigDecimal)
     */
    private BigDecimal extractPriceFromOrderMap(Map<String, Object> orderMap, String fieldName) {
        try {
            Object priceObj = orderMap.get(fieldName);
            if (priceObj == null) {
                return null;
            }

            if (priceObj instanceof BigDecimal bigDecimal) {
                return bigDecimal;
            }

            if (priceObj instanceof Map) {
                //   FIXED: Handle both MoneyDocument formats
                Map<String, Object> moneyDoc = (Map<String, Object>) priceObj;

                // Try "amount" field first (new format)
                Object amountObj = moneyDoc.get(AMOUNT);
                if (amountObj != null) {
                    return new BigDecimal(amountObj.toString());
                }

                // Fallback to "value" field (old format)
                Object valueObj = moneyDoc.get("value");
                if (valueObj != null) {
                    return new BigDecimal(valueObj.toString());
                }
            }

            if (priceObj instanceof String string) {
                return new BigDecimal(string);
            }

            if (priceObj instanceof Number) {
                return new BigDecimal(priceObj.toString());
            }

            logger.warn("Unknown price format for field {}: {}", fieldName, priceObj.getClass().getSimpleName());
            return null;

        } catch (Exception e) {
            logger.warn("Error extracting price from field {}: {}", fieldName, e.getMessage());
            return null;
        }
    }

    /**
     * FIXED: Convert status string to SpotOrderStatus with proper mapping
     */
    private SpotOrderStatus convertStatusToSpotOrderStatus(String status) {
        if (status == null || status.trim().isEmpty()) {
            return SpotOrderStatus.TRADING; // Default status
        }

        String statusUpper = status.trim().toUpperCase();

        // Handle special status mappings for stop orders
        return switch (statusUpper) {
            case "ACTIVE" -> SpotOrderStatus.TRADING; // ACTIVE stop orders are TRADING
            case "INACTIVE" -> SpotOrderStatus.CANCELED; // INACTIVE stop orders are CANCELED
            case "TRIGGERED" -> SpotOrderStatus.TRIGGER; // TRIGGERED stop orders
            case "NEW" -> SpotOrderStatus.TRADING; // NEW orders are TRADING
            case "PENDING" -> SpotOrderStatus.TRADING; // PENDING orders are TRADING
            case "FILLED" -> SpotOrderStatus.COMPLETED; // FILLED orders are COMPLETED
            case "CANCELLED" -> SpotOrderStatus.CANCELED; // CANCELLED orders are CANCELED
            case "EXPIRED" -> SpotOrderStatus.OVERTIMED; // EXPIRED orders are OVERTIMED
            default -> {
                try {
                    // Try direct enum conversion
                    yield SpotOrderStatus.valueOf(statusUpper);
                } catch (IllegalArgumentException e) {
                    logger.warn("Unknown status '{}', defaulting to TRADING", status);
                    yield SpotOrderStatus.TRADING;
                }
            }
        };
    }

    /**
     * CRITICAL FIX: Get stop orders for snapshot
     * Lấy stop orders từ MatchingEngineStopOrderManager để include vào snapshot
     */
    public List<Object> getStopOrdersForSnapshot(String symbol) {
        try {
            List<Object> stopOrdersForSnapshot = new ArrayList<>();

            // Get stop orders from stop order manager
            List<SimpleStopOrder> stopOrders = stopOrderManager.getStopOrdersForSymbol(symbol, CommonConstance.SPOT);

            if (stopOrders != null && !stopOrders.isEmpty()) {
                for (SimpleStopOrder stopOrder : stopOrders) {
                    // Convert SimpleStopOrder to Map for snapshot
                    Map<String, Object> stopOrderMap = convertStopOrderToMap(stopOrder);
                    if (stopOrderMap != null) {
                        stopOrdersForSnapshot.add(stopOrderMap);
                    }
                }
            }

            logger.info("Converted {} stop orders to snapshot format for symbol: {}",
                    stopOrdersForSnapshot.size(), symbol);
            return stopOrdersForSnapshot;

        } catch (Exception e) {
            logger.error("Error getting stop orders for snapshot for symbol: {}", symbol, e);
            return new ArrayList<>();
        }
    }

    /**
     * Convert SimpleStopOrder to Map for snapshot storage
     * CRITICAL FIX: Use field names that match restore logic expectations
     */
    private Map<String, Object> convertStopOrderToMap(SimpleStopOrder stopOrder) {
        try {
            Map<String, Object> orderMap = new HashMap<>();

            //   CRITICAL FIX: Use field names that restore logic expects
            orderMap.put("order_id", stopOrder.getOrderId().getValue());  // order_id not orderId
            orderMap.put("member_id", stopOrder.getMember().getId());     // member_id not memberId
            orderMap.put("direction", stopOrder.getDirection().name());
            orderMap.put("type", stopOrder.getStopOrderType().name());
            orderMap.put("status", stopOrder.getStatus().name());

            //   CRITICAL FIX: Convert Money objects to MoneyDocument format with correct field names
            if (stopOrder.getTriggerPrice() != null) {
                // Use both field names for compatibility
                orderMap.put("stop_price", createMoneyDocument(stopOrder.getTriggerPrice()));  // Primary field for restore
                orderMap.put("triggerPrice", createMoneyDocument(stopOrder.getTriggerPrice())); // Backup field
            }
            if (stopOrder.getExecutionPrice() != null) {
                orderMap.put("price", createMoneyDocument(stopOrder.getExecutionPrice()));
            }
            if (stopOrder.getQuantity() != null) {
                orderMap.put("quantity", createMoneyDocument(stopOrder.getQuantity()));
            }

            orderMap.put("strategy", stopOrder.getStrategy().name());
            orderMap.put("timestamp", stopOrder.getTimestamp());
            orderMap.put("signInitialized", stopOrder.isSignInitialized());

            return orderMap;

        } catch (Exception e) {
            logger.error("Error converting stop order to map: {}", stopOrder.getOrderId().getValue(), e);
            return Map.of();
        }
    }

    /**
     * CRITICAL FIX: Restore stop order từ ExchangeOrder vào MatchingEngineStopOrderManager
     * Convert ExchangeOrder (từ database) thành SimpleStopOrder và add vào stop order manager
     */
    private boolean restoreStopOrderToManager(ExchangeOrder exchangeOrder, String symbol) {
        try {
            if (exchangeOrder == null) {
                logger.warn("Cannot restore null ExchangeOrder to stop order manager");
                return false;
            }

            // Validate stop order types
            if (!isStopOrderType(exchangeOrder.getType())) {
                logger.warn("ExchangeOrder {} is not a stop order type: {}",
                        exchangeOrder.getOrderId(), exchangeOrder.getType());
                return false;
            }

            logger.info("Restoring stop order from database: orderId={}, type={}, status={}",
                    exchangeOrder.getOrderId(), exchangeOrder.getType(), exchangeOrder.getStatus());

            // Convert ExchangeOrder to SimpleStopOrder
            SimpleStopOrder stopOrder = convertExchangeOrderToSimpleStopOrder(exchangeOrder, symbol);
            if (stopOrder == null) {
                logger.warn("Failed to convert ExchangeOrder to SimpleStopOrder: {}", exchangeOrder.getOrderId());
                return false;
            }

            // Add to stop order manager (no current price during restore)
            boolean added = stopOrderManager.addStopOrder(stopOrder, null, CommonConstance.SPOT);
            if (added) {
                logger.info("Restored stop order from snapshot: {} for symbol: {}", exchangeOrder.getOrderId(), symbol);
                return true;
            } else {
                logger.warn("Failed to add restored stop order to manager: {}", exchangeOrder.getOrderId());
                return false;
            }

        } catch (Exception e) {
            logger.error("Error restoring stop order to manager: orderId={}",
                    exchangeOrder != null ? exchangeOrder.getOrderId() : "null", e);
            return false;
        }
    }

    /**
     * Check if SpotOrderType is a stop order type
     */
    private boolean isStopOrderType(SpotOrderType type) {
        return type == SpotOrderType.STOP_LIMIT || type == SpotOrderType.STOP_MARKET;
    }

    /**
     * Convert ExchangeOrder to SimpleStopOrder for stop order manager
     */
    private SimpleStopOrder convertExchangeOrderToSimpleStopOrder(ExchangeOrder exchangeOrder, String symbol) {
        try {
            // Create Member object
            Member member = Member.builder()
                    .id(exchangeOrder.getMemberId())
                    .build();

            // Convert direction (ExchangeOrder already uses SpotOrderDirection)
            SpotOrderDirection spotDirection = exchangeOrder.getDirection();
            OrderDirection direction = spotDirection.toOrderDirection(); // Convert to OrderDirection

            // Convert stop order type
            StopOrderType stopOrderType = convertToStopOrderType(exchangeOrder.getType());

            // Convert status to StopOrderStatus
            StopOrderStatus status = convertToStopOrderStatus(exchangeOrder.getStatus());

            // Create SimpleStopOrder
            SimpleStopOrder.SimpleStopOrderBuilder builder = SimpleStopOrder.builder()
                    .orderId(OrderId.of(exchangeOrder.getOrderId()))
                    .member(member)
                    .symbol(Symbol.of(symbol))
                    .direction(direction)
                    .stopOrderType(stopOrderType)
                    .status(status)
                    .quantity(Money.of(exchangeOrder.getAmount()))
                    .timestamp(exchangeOrder.getTime());

            // Set trigger price (stop price)
            if (exchangeOrder.getStopPrice() != null) {
                builder.triggerPrice(Money.of(exchangeOrder.getStopPrice()));
            }

            // Set execution price (limit price for STOP_LIMIT orders)
            if (exchangeOrder.getPrice() != null && stopOrderType == StopOrderType.STOP_LIMIT) {
                builder.executionPrice(Money.of(exchangeOrder.getPrice()));
            }

            // Set strategy based on order type and direction
            StopOrderStrategy strategy = StopOrderStrategy.TRADITIONAL;
            builder.strategy(strategy);

            SimpleStopOrder stopOrder = builder.build();

            logger.info("Converted ExchangeOrder to SimpleStopOrder: orderId={}, triggerPrice={}, strategy={}",
                    exchangeOrder.getOrderId(), stopOrder.getTriggerPrice(), strategy);

            return stopOrder;

        } catch (Exception e) {
            logger.error("Error converting ExchangeOrder to SimpleStopOrder: orderId={}",
                    exchangeOrder.getOrderId(), e);
            return null;
        }
    }

    /**
     * Convert SpotOrderType to StopOrderType
     */
    private StopOrderType convertToStopOrderType(SpotOrderType spotType) {
        switch (spotType) {
            case STOP_LIMIT:
                return StopOrderType.STOP_LIMIT;
            case STOP_MARKET:
                return StopOrderType.STOP_MARKET;
            default:
                throw new IllegalArgumentException("Invalid stop order type: " + spotType);
        }
    }

    /**
     * Convert SpotOrderStatus to StopOrderStatus for SimpleStopOrder
     */
    private StopOrderStatus convertToStopOrderStatus(SpotOrderStatus spotStatus) {
        if (spotStatus == null) {
            return StopOrderStatus.ACTIVE; // Default for stop orders
        }

        return switch (spotStatus) {
            case TRADING, TRIGGER -> StopOrderStatus.ACTIVE;
            case COMPLETED -> StopOrderStatus.TRIGGERED;
            case CANCELED -> StopOrderStatus.CANCELLED;
            case PARTIAL_FILLED -> StopOrderStatus.ACTIVE; // Partial filled stop orders remain active
            default -> StopOrderStatus.ACTIVE; // Default fallback
        };
    }

    /**
     * Create MoneyDocument format for snapshot
     */
    private Map<String, Object> createMoneyDocument(Money money) {
        Map<String, Object> moneyDoc = new HashMap<>();
        // CRITICAL FIX: Ensure 'amount' field is correctly populated from Money object
        // The 'amount' field in RedisSnapshotDocument.MoneyDocument expects a String representation of the amount
        moneyDoc.put("amount", money.getAmount() != null ? money.getAmount().toPlainString() : "0");
        moneyDoc.put("scale", 8); // Use actual scale from Money object
        moneyDoc.put("currency", "USD"); // Default currency, assuming Money object doesn't store it
        return moneyDoc;
    }

    /**
     * Create SimpleStopOrder from snapshot data
     * CRITICAL FIX: Accept original StopOrderType to preserve sign
     */
    private SimpleStopOrder createStopOrderFromSnapshot(ExchangeOrder exchangeOrder, BigDecimal triggerPrice, String symbol, StopOrderStrategy strategy, StopOrderType originalStopOrderType) {
        try {
            // Create Member
            Member member = Member.builder()
                    .id(exchangeOrder.getMemberId())
                    .username("restored-user")
                    .build();

            // Convert direction
            OrderDirection direction = convertToOrderDirection(exchangeOrder.getDirection());

            //   CRITICAL FIX: Use original StopOrderType to preserve sign
            // This preserves BUY_DIP_MARKET, SELL_RALLY_LIMIT, etc. from snapshot
            StopOrderType stopOrderType = originalStopOrderType;

            // Create SimpleStopOrder
            return SimpleStopOrder.builder()
                    .orderId(OrderId.of(exchangeOrder.getOrderId()))
                    .member(member)
                    .symbol(Symbol.of(symbol))
                    .direction(direction)
                    .stopOrderType(stopOrderType)
                    .strategy(strategy)
                    .quantity(Money.of(exchangeOrder.getAmount()))
                    .executionPrice(exchangeOrder.getPrice() != null ? Money.of(exchangeOrder.getPrice()) : null)
                    .triggerPrice(triggerPrice != null ? Money.of(triggerPrice) : null)
                    .timestamp(exchangeOrder.getTime())
                    .status(StopOrderStatus.NEW)
                    .build();

        } catch (Exception e) {
            logger.error("Error creating stop order from snapshot: {}", exchangeOrder.getOrderId(), e);
            return null;
        }
    }

    /**
     * Get cached last trade price for symbol (public method for external access)
     */
    public BigDecimal getLastTradePriceFromCache(String symbol) {
        return lastTradePriceCache.get(symbol);
    }

    /**
     * Convert Redis orders map to format expected by restore methods
     */
    private Map<String, Object> convertRedisOrdersToMap(Map<String, List<RedisSnapshotDocument.OrderDocument>> redisOrdersMap, String symbol) {
        if (redisOrdersMap == null) {
            return new HashMap<>();
        }

        Map<String, Object> result = new HashMap<>();
        for (Map.Entry<String, List<RedisSnapshotDocument.OrderDocument>> entry : redisOrdersMap.entrySet()) {
            String priceLevel = convertSafePriceLevelToOriginal(entry.getKey());
            List<Map<String, Object>> orderMaps = convertRedisOrdersToList(entry.getValue(), symbol);
            result.put(priceLevel, orderMaps);
        }
        return result;
    }

    /**
     * CRITICAL FIX: Convert Redis stop orders list to map format grouped by trigger price
     * This allows stop orders to be processed like BUY/SELL orders with price level grouping
     */
    private Map<String, Object> convertRedisStopOrdersToMap(List<RedisSnapshotDocument.OrderDocument> stopOrders, String symbol) {
        if (stopOrders == null || stopOrders.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, List<Map<String, Object>>> groupedByTriggerPrice = new HashMap<>();

        // Group stop orders by trigger price (stop_price)
        for (RedisSnapshotDocument.OrderDocument orderDoc : stopOrders) {
            // Skip null orderDoc to satisfy SonarQube
            if (orderDoc == null) {
                continue;
            }

            Map<String, Object> orderMap = convertRedisOrderToMap(orderDoc, symbol);
            // Group by trigger price
            groupedByTriggerPrice.computeIfAbsent(orderDoc.getStopPrice().getAmount(), k -> new ArrayList<>()).add(orderMap);
        }

        // Convert to final format
        Map<String, Object> result = new HashMap<>(groupedByTriggerPrice);

        logger.info(" DEBUG: Converted {} stop orders into {} trigger price levels",
                stopOrders.size(), result.size());
        return result;
    }

    /**
     * Convert Redis orders list to format expected by restore methods
     */
    private List<Map<String, Object>> convertRedisOrdersToList(List<RedisSnapshotDocument.OrderDocument> redisOrders, String symbol) {
        if (redisOrders == null) {
            return new ArrayList<>();
        }

        List<Map<String, Object>> result = new ArrayList<>();
        for (RedisSnapshotDocument.OrderDocument orderDoc : redisOrders) {
            Map<String, Object> orderMap = convertRedisOrderToMap(orderDoc, symbol);
            if (orderMap != null) {
                result.add(orderMap);
            }
        }
        return result;
    }

    /**
     * Convert single Redis order document to map
     */
    private Map<String, Object> convertRedisOrderToMap(RedisSnapshotDocument.OrderDocument orderDoc, String symbol) {
        if (orderDoc == null) {
            return Map.of();
        }

        Map<String, Object> orderMap = new HashMap<>();
        //   CRITICAL FIX: Use snake_case field names to match restoreStopOrderFromSnapshot expectations
        orderMap.put("order_id", orderDoc.getOrderId());  // Changed from "orderId" to "order_id"
        orderMap.put("member_id", orderDoc.getMemberId()); // Changed from "memberId" to "member_id"
        orderMap.put("symbol", symbol); //   FIX: Add missing symbol field
        orderMap.put("direction", orderDoc.getDirection());
        orderMap.put("type", orderDoc.getType());
        orderMap.put("status", orderDoc.getStatus());

        if (orderDoc.getPrice() != null) {
            orderMap.put("price", convertRedisMoneyToMap(orderDoc.getPrice()));
        }
        if (orderDoc.getQuantity() != null) {
            orderMap.put("quantity", convertRedisMoneyToMap(orderDoc.getQuantity()));
        }
        if (orderDoc.getFilledQuantity() != null) {
            orderMap.put("filled_quantity", convertRedisMoneyToMap(orderDoc.getFilledQuantity())); // Changed from "filledQuantity" to "filled_quantity"
        }

        orderMap.put("turnover", orderDoc.getTurnover());
        orderMap.put("fee", orderDoc.getFee());
        orderMap.put("leverage", orderDoc.getLeverage());
        orderMap.put("margin_trade", orderDoc.getMarginTrade()); // Changed from "marginTrade" to "margin_trade"

        if (orderDoc.getStopPrice() != null) {
            orderMap.put("stop_price", convertRedisMoneyToMap(orderDoc.getStopPrice())); // Changed from "stopPrice" to "stop_price"
        }

        orderMap.put("trigger_condition", orderDoc.getTriggerCondition()); // Changed from "triggerCondition" to "trigger_condition"
        orderMap.put("time_in_force", orderDoc.getTimeInForce()); // Changed from "timeInForce" to "time_in_force"
        orderMap.put("created_time", orderDoc.getCreatedTime() != null ? orderDoc.getCreatedTime().toString() : null); // Changed from "createdAt" to "created_time" and use ISO format
        orderMap.put("updated_time", orderDoc.getUpdatedTime() != null ? orderDoc.getUpdatedTime().toString() : null); // Changed from "updatedTime" to "updated_time"
        orderMap.put("completed_time", orderDoc.getCompletedTime() != null ? orderDoc.getCompletedTime().toString() : null); // Changed from "completedTime" to "completed_time"
        orderMap.put("canceled_time", orderDoc.getCanceledTime() != null ? orderDoc.getCanceledTime().toString() : null); // Changed from "canceledTime" to "canceled_time"

        return orderMap;
    }

    /**
     * Convert Redis money document to map
     */
    private Map<String, Object> convertRedisMoneyToMap(RedisSnapshotDocument.MoneyDocument moneyDoc) {
        if (moneyDoc == null) {
            return Map.of();
        }

        Map<String, Object> moneyMap = new HashMap<>();
        moneyMap.put("amount", moneyDoc.getAmount());
        moneyMap.put("scale", moneyDoc.getScale());
        moneyMap.put("currency", moneyDoc.getCurrency());
        return moneyMap;
    }

    /**
     * Convert safe price level back to original format (underscores to dots)
     */
    private String convertSafePriceLevelToOriginal(String safePriceLevel) {
        if (safePriceLevel == null) {
            return null;
        }
        return safePriceLevel.replace("_", ".");
    }
}
