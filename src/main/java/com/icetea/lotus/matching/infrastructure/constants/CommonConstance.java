package com.icetea.lotus.matching.infrastructure.constants;

public class CommonConstance {

    public static final String ORDER_PLACED = "ORDER_PLACED";
    public static final String ORDER_CLOSE_POSITION = "ORDER_CLOSE_POSITION";
    public static final String ORDER_LIST = "orderList";
    public static final String SUCCESS = "SUCCESS";

    private CommonConstance() {
        throw new IllegalStateException("Utility class");
    }

    public static final String ORDER_ID = "order_id";
    public static final String ORDERID = "orderId";
    public static final String SYMBOL = "symbol";
    public static final String MEMBER_ID = "member_id";
    public static final String QUANTITY = "quantity";
    public static final String DIRECTION = "direction";
    public static final String TYPE = "type";
    public static final String STATUS = "status";
    public static final String PRICE = "price";
    public static final String FILLED_QUANTITY = "filled_quantity";
    public static final String CREATED_TIME = "created_time";
    public static final String STOP_PRICE = "stop_price";
    public static final String MARKET_PRICE = "MARKET_PRICE";
    public static final String LIMIT_PRICE = "LIMIT_PRICE";
    public static final String AMOUNT = "amount";
    public static final String BUY_TURNOVER = "buyTurnover";
    public static final String SELL_TURNOVER = "sellTurnover";
    public static final String BUY_ORDER_ID = "buyOrderId";
    public static final String SELL_ORDER_ID = "sellOrderId";
    public static final String TIME = "time";
    public static final String TIMESTAMP = "timestamp";
    public static final String IS_PARTIALLY_FILL = "isPartiallyFilled";
    public static final String TRADED_AMOUNT = "tradedAmount";
    public static final String FUTURE = "future";
    public static final String SPOT = "spot";
    public static final String ITEMS = "items";
    public static final String MATCHING_ENGINE_SNAPSHOT_VERSION = "matching-engine:snapshot:versions:%s:%s";
    public static final String GET_AMOUNT = "getAmount";
    public static final String BUY_ORDERS = "buyOrders";
    public static final String SELL_ORDERS = "sellOrders";
    public static final String ALL_ORDERS = "allOrders";
    public static final String STOP_ORDERS = "stopOrders";
    public static final String POD_PATTERN = ".*pod[-_]?(\\d+).*";

}
