package com.icetea.lotus.matching.infrastructure.messaging.filter.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.matching.domain.entity.ExchangeOrder;
import com.icetea.lotus.matching.infrastructure.exchange.ExchangeCompatibilityService;
import com.icetea.lotus.matching.infrastructure.exchange.ExchangeTradeResult;
import com.icetea.lotus.matching.infrastructure.messaging.dto.ExchangeOrderMessage;
import com.icetea.lotus.matching.infrastructure.messaging.producer.ExchangeKafkaProducer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Exchange Order Consumer - Copy từ Exchange module
 * Processes spot trading orders from Kafka
 *
 * <AUTHOR> nguyen
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ExchangeOrderConsumer {

    private final ExchangeCompatibilityService exchangeService;
    private final ExchangeKafkaProducer exchangeKafkaProducer;
    private final ObjectMapper objectMapper = new ObjectMapper()
            .configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    // Executor for async processing
    private final ExecutorService processingExecutor = Executors.newFixedThreadPool(20);

    /**
     * Process submitted orders from Kafka - Migrated từ Exchange module
     * Sử dụng spotKafkaListenerContainerFactory cho spot trading
     */
    @KafkaListener(
            topics = "${topic-kafka.exchange.order:exchange-order}",
            containerFactory = "spotKafkaListenerContainerFactory",
            groupId = "matching-engine-exchange-orders")
    public void onOrderSubmitted(List<ConsumerRecord<String, String>> records, Acknowledgment ack) {
        log.info("Received {} exchange order records", records.size());

        try {
            for (ConsumerRecord<String, String> consumerRecord : records) {
                processingExecutor.submit(() -> processExchangeOrder(consumerRecord));
            }

            // Manual acknowledgment sau khi submit tất cả tasks
            ack.acknowledge();

        } catch (Exception e) {
            log.error("Error processing exchange order batch", e);
            // Không acknowledge nếu có lỗi
        }
    }

    /**
     * Process order cancellations from Kafka - Migrated từ Exchange module
     * Sử dụng spotKafkaListenerContainerFactory cho spot trading
     */
    @KafkaListener(
            topics = "${topic-kafka.exchange.order-cancel:exchange-order-cancel}",
            containerFactory = "spotKafkaListenerContainerFactory",
            groupId = "matching-engine-exchange-cancels")
    public void onOrderCancel(List<ConsumerRecord<String, String>> records, Acknowledgment ack) {
        log.info("Received {} exchange order cancel records", records.size());

        try {
            for (ConsumerRecord<String, String> consumerRecord : records) {
                processingExecutor.submit(() -> processOrderCancel(consumerRecord));
            }

            // Manual acknowledgment sau khi submit tất cả tasks
            ack.acknowledge();

        } catch (Exception e) {
            log.error("Error processing exchange order cancel batch", e);
            // Không acknowledge nếu có lỗi
        }
    }

    /**
     * Process individual exchange order
     */
    private void processExchangeOrder(ConsumerRecord<String, String> consumerRecord) {
        try {
            // Records are pre-filtered by SymbolOwnershipFilterStrategy, so we can process them directly.
            String orderJson = consumerRecord.value();
            ExchangeOrder exchangeOrder = parseExchangeOrderFromJson(orderJson);
            String orderSymbol = exchangeOrder.getSymbol();

            log.info("Processing pre-filtered exchange order for symbol: {}, JSON: {}", orderSymbol, orderJson);

            // Process through Exchange compatibility service using DTO
            ExchangeTradeResult result = exchangeService.processExchangeOrderDTO(exchangeOrder);

            // Publish results
            if (result.isSuccess()) {
                // Publish completed orders - Convert to exchange-core format
                if (!result.getCompletedOrders().isEmpty()) {
                    List<Object> exchangeCoreOrders = convertToExchangeCoreOrders(result.getCompletedOrders());
                    exchangeKafkaProducer.publishExchangeOrderCompleted(orderSymbol, exchangeCoreOrders);
                }
                log.info("Successfully processed exchange order for symbol: {} with {} trades",
                        orderSymbol, result.getTrades().size());
            } else {
                log.warn("Failed to process exchange order for symbol: {} - {}",
                        orderSymbol, result.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("Error processing exchange order from record: {}", consumerRecord, e);
        }
    }

    /**
     * Process individual order cancellation
     */
    private void processOrderCancel(ConsumerRecord<String, String> consumerRecord) {
        try {
            String symbol = consumerRecord.key();
            String cancelJson = consumerRecord.value();

            log.info("Processing exchange order cancel for symbol: {}", symbol);
            // Parse cancel request from JSON
            ExchangeOrderMessage cancelRequest = parseCancelRequestFromJson(cancelJson);

            // FLOW 1: Process through Exchange compatibility service
            ExchangeTradeResult result = exchangeService.processOrderCancel(cancelRequest);

            // FLOW 2: Publish results, handle in market service
            if (result.isSuccess()) {
                // Publish cancel success - ĐÚNG method như Exchange module
                exchangeKafkaProducer.publishExchangeOrderCancelSuccess(symbol, result.getCancelResult());

                // Trade plate đã được gửi trong ExchangeCompatibilityService
                // Không cần gửi lại ở đây để tránh duplicate message

                log.info("Successfully processed exchange order cancel for symbol: {}", symbol);
            } else {
                log.warn("Failed to process exchange order cancel for symbol: {} - {}",
                        symbol, result.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("Error processing exchange order cancel from record: {}", consumerRecord, e);
        }
    }

    /**
     * Parse exchange order from JSON directly to ExchangeOrderDTO
     */
    private ExchangeOrder parseExchangeOrderFromJson(String orderJson) throws JsonProcessingException {
        try {
            // Parse directly to ExchangeOrderDTO - no intermediate mapping
            ExchangeOrder result = objectMapper.readValue(orderJson, ExchangeOrder.class);
            log.info("Parsed exchange order JSON to DTO: {} -> {}", orderJson, result.getSummary());

            // Validate DTO
            if (!result.isValid()) {
                throw new IllegalArgumentException("Invalid exchange order DTO: " + result.getSummary());
            }

            return result;
        } catch (Exception e) {
            log.error("Failed to parse exchange order from JSON to DTO: {}", orderJson, e);
            throw e;
        }
    }

    /**
     * Parse cancel request from JSON
     */
    private ExchangeOrderMessage parseCancelRequestFromJson(String cancelJson) throws JsonProcessingException {
        try {
            // Parse as generic object first, then determine specific type
            return objectMapper.readValue(cancelJson, ExchangeOrderMessage.class);
        } catch (Exception e) {
            log.error("Failed to parse cancel request from JSON: {}", cancelJson, e);
            throw e;
        }
    }

    /**
     * Convert matching engine ExchangeOrder to exchange-core format
     * Đảm bảo tương thích với module market
     */
    @SuppressWarnings("java:S3776")
    private List<Object> convertToExchangeCoreOrders(List<Object> completedOrders) {
        List<Object> exchangeCoreOrders = new ArrayList<>();

        for (Object orderObj : completedOrders) {
            if (orderObj instanceof ExchangeOrder matchingOrder) {

                // Create exchange-core compatible order using Map to avoid enum conflicts
                Map<String, Object> exchangeCoreOrder = new HashMap<>();
                exchangeCoreOrder.put("orderId", matchingOrder.getOrderId());
                exchangeCoreOrder.put("memberId", matchingOrder.getMemberId());
                exchangeCoreOrder.put("symbol", matchingOrder.getSymbol());
                exchangeCoreOrder.put("amount", matchingOrder.getAmount());
                exchangeCoreOrder.put("tradedAmount", matchingOrder.getTradedAmount());
                exchangeCoreOrder.put("turnover", matchingOrder.getTurnover());
                exchangeCoreOrder.put("price", matchingOrder.getPrice());
                exchangeCoreOrder.put("coinSymbol", matchingOrder.getCoinSymbol());
                exchangeCoreOrder.put("baseSymbol", matchingOrder.getBaseSymbol());
                exchangeCoreOrder.put("time", matchingOrder.getTime());
                exchangeCoreOrder.put("completedTime", matchingOrder.getCompletedTime());
                exchangeCoreOrder.put("canceledTime", matchingOrder.getCanceledTime());
                exchangeCoreOrder.put("useDiscount", matchingOrder.getUseDiscount());
                exchangeCoreOrder.put("stopPrice", matchingOrder.getStopPrice());

                //   Add triggered and triggerTime fields for stop orders
                exchangeCoreOrder.put("triggered", matchingOrder.getTriggered());
                exchangeCoreOrder.put("triggerTime", matchingOrder.getTriggerTime());

                // Convert enums to String values that exchange-core can understand
                if (matchingOrder.getType() != null) {
                    exchangeCoreOrder.put("type", matchingOrder.getType().getExchangeCode());
                }
                if (matchingOrder.getDirection() != null) {
                    exchangeCoreOrder.put("direction", matchingOrder.getDirection().getExchangeCode());
                }
                if (matchingOrder.getStatus() != null) {
                    exchangeCoreOrder.put("status", matchingOrder.getStatus().getExchangeCode());
                }
                if (matchingOrder.getSelfTradePreventionMode() != null) {
                    exchangeCoreOrder.put("selfTradePreventionMode", matchingOrder.getSelfTradePreventionMode().name());
                }

                exchangeCoreOrders.add(exchangeCoreOrder);
            } else {
                // If already in correct format, keep as is
                exchangeCoreOrders.add(orderObj);
            }
        }

        return exchangeCoreOrders;
    }

    /**
     * Shutdown executor
     */
    public void shutdown() {
        processingExecutor.shutdown();
    }
}
