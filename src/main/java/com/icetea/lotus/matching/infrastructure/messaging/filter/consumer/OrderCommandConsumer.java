package com.icetea.lotus.matching.infrastructure.messaging.filter.consumer;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.matching.infrastructure.constants.CommonConstance;
import com.icetea.lotus.matching.infrastructure.constants.OrderEventType;
import com.icetea.lotus.matching.infrastructure.futurecore.FutureCoreCompatibilityService;
import com.icetea.lotus.matching.infrastructure.futurecore.FutureCoreTradeResult;
import com.icetea.lotus.matching.infrastructure.messaging.dto.ClosePositionMessage;
import com.icetea.lotus.matching.infrastructure.messaging.dto.FutureTradePlateMessage;
import com.icetea.lotus.matching.infrastructure.messaging.dto.OrderEventMessage;
import com.icetea.lotus.matching.infrastructure.messaging.producer.FutureCoreKafkaProducer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Order Command Consumer - INPUT topic consumer
 * Handles order commands (PLACE_ORDER, CANCEL_ORDER, UPDATE_ORDER)
 * Migrated từ Future-Core OrderCommandConsumer
 *
 * <AUTHOR> nguyen
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderCommandConsumer {

    public static final String ORDER = "order";
    private final FutureCoreCompatibilityService futureCoreService;
    private final FutureCoreKafkaProducer futureCoreKafkaProducer;
    private final ObjectMapper objectMapper;

    // Executor for async processing
    private final ExecutorService processingExecutor = Executors.newFixedThreadPool(15);

    /**
     * Handle order commands - INPUT topic consumer
     * Sử dụng futuresKafkaListenerContainerFactory cho futures trading
     */
    @KafkaListener(
            topics = "${topic-kafka.contract.order-commands:contract-order-commands}",
            containerFactory = "futuresKafkaListenerContainerFactory",
            groupId = "matching-engine-order-commands")
    public void handleOrderCommand(List<ConsumerRecord<String, String>> records, Acknowledgment ack) {
        log.info("Received {} order command records", records.size());

        try {
            for (ConsumerRecord<String, String> consumerRecord : records) {
                processingExecutor.submit(() -> processOrderCommand(consumerRecord));
            }

            // Manual acknowledgment sau khi submit tất cả tasks
            ack.acknowledge();

        } catch (Exception e) {
            log.error("Error processing order command batch", e);
            // Không acknowledge nếu có lỗi
        }
    }

    /**
     * Process individual order command
     */
    private void processOrderCommand(ConsumerRecord<String, String> consumerRecord) {
        String symbol = consumerRecord.key();
        String value = consumerRecord.value();

        // Enhanced validation
        if (symbol == null || symbol.trim().isEmpty()) {
            log.error("Invalid symbol in order command: {}", symbol);
            return;
        }

        if (value == null || value.trim().isEmpty()) {
            log.error("Empty command JSON for symbol: {}", symbol);
            return;
        }

        log.info("Processing order command for symbol: {}", symbol);

        try {
            // Parse command from JSON with validation
            JsonNode commandJson;
            commandJson = objectMapper.readTree(value);
            // Validate command structure
            if (!commandJson.has("type") || !commandJson.has("commandId")) {
                log.error("Missing required fields (type/commandId) in command for symbol: {}", symbol);
                return;
            }

            String commandType = commandJson.get("type").asText();
            String commandId = commandJson.get("commandId").asText();

            if (commandType == null || commandType.trim().isEmpty()) {
                log.error("Empty commandType for symbol: {}", symbol);
                return;
            }

            log.info("Processing command type: {} with ID: {} for symbol: {}", commandType, commandId, symbol);

            // Process command theo loại
            switch (commandType) {
                case "PLACE_ORDER":
                    log.info("Processing PLACE_ORDER command for symbol: {}", symbol);
                    handlePlaceOrderCommand(commandJson, symbol, commandType);
                    break;

                case "CANCEL_ORDER":
                    log.info("Processing CANCEL_ORDER command for symbol: {}", symbol);
                    handleCancelOrderCommand(commandJson, symbol);
                    break;

                case "CANCEL_ALL_ORDER":
                    log.info("Processing CANCEL_ALL_ORDER command for symbol: {}", symbol);
                    handleCancelOrderCommandAll(commandJson, symbol);
                    break;

                case "UPDATE_ORDER":
                    log.info("Processing UPDATE_ORDER command for symbol: {}", symbol);
                    handleUpdateOrderCommand(commandJson, symbol, commandType);
                    break;
                case "ORDER_CLOSE_POSITION":
                    log.info("Processing ORDER_CLOSE_POSITION command for symbol: {}", symbol);
                    handleOrderClosePositionCommand(commandJson, symbol, commandType);
                    break;
                default:
                    log.warn("Unsupported command type: {} for symbol: {}", commandType, symbol);
                    break;
            }

            log.info("Successfully processed command type: {} for symbol: {}", commandType, symbol);

        } catch (Exception e) {
            log.error("Unexpected error processing order command for symbol: {}", symbol, e);
        }
    }

    private void handleOrderClosePositionCommand(JsonNode commandJson, String symbol, String commandType) {
        try {
            JsonNode orderJson = commandJson.get(CommonConstance.ORDER_LIST);
            if (orderJson == null || !orderJson.isArray()) {
                log.info("Missing order list in command for symbol: {}", symbol);
                return;
            }
            List<Object> orderList = objectMapper.readValue(orderJson.traverse(), new TypeReference<>() {
            });
            log.info("Processing order list in command for symbol: {}", symbol);
            List<OrderEventMessage> orderEventMessages = new ArrayList<>();
            int totalClosePositions = 0;
            for (Object orderListItem : orderList) {
                FutureCoreTradeResult result = futureCoreService.processContractOrderInternal(orderListItem, commandType);
                if (result.isSuccess()) {
                    if (result.getTradePlate() != null) {
                        FutureTradePlateMessage message = FutureTradePlateMessage.createTradePlateMessage(result.getSymbol(), result.getTradePlate());
                        futureCoreKafkaProducer.publishContractTradePlate(result.getSymbol(), message);
                    }
                    if (result.getTrades() != null && !result.getTrades().isEmpty()) {
                        OrderEventMessage orderEventMessage = futureCoreKafkaProducer.orderEventMessageBuilder(OrderEventType.ORDER_CLOSE_POSITION, result.getTrades(), result.getSymbol(), result.getOrderId());
                        log.info("OrderEventMessage with trades: {}", orderEventMessage);
                        orderEventMessages.add(orderEventMessage);
                        futureCoreKafkaProducer.publishContractTradeBatch(result.getTrades(), symbol);
                        totalClosePositions++;
                    } else {
                        OrderEventMessage orderEventMessage = futureCoreKafkaProducer.orderEventMessageBuilder(OrderEventType.ORDER_CLOSE_POSITION, List.of(), result.getSymbol(), result.getOrderId());
                        log.info("OrderEventMessage without trade: {}", orderEventMessage);
                        orderEventMessages.add(orderEventMessage);
                    }
                } else {
                    log.error("Error processing order list in command for symbol: {}", symbol);
                }
            }
            ClosePositionMessage closePositionMessage = ClosePositionMessage.builder()
                    .memberId(extractMemberIdFromOrder(orderList.get(0)))
                    .totalClosedPositions(totalClosePositions)
                    .orderEventMessages(orderEventMessages)
                    .build();
            futureCoreKafkaProducer.publishOrderClosePositionEvent(symbol, closePositionMessage);
        } catch (Exception e) {
            log.error("Unexpected error processing order list in command for symbol: {}", symbol, e);
        }
    }

    private Long extractMemberIdFromOrder(Object order) {
        try {
            if (order instanceof Map) {
                Map<String, Object> orderMap = (Map<String, Object>) order;
                return ((Number) orderMap.get("memberId")).longValue();
            }
        } catch (Exception ex) {
            log.error("Unexpected error processing order list in command for symbol: {}", order, ex);
        }
        return 0L;
    }

    /**
     * Handle PLACE_ORDER command with enhanced validation
     */
    private void handlePlaceOrderCommand(JsonNode commandJson, String symbol, String commandType) {
        try {
            // Validate order data exists
            if (!commandJson.has(ORDER)) {
                log.error("Missing 'order' field in PLACE_ORDER command for symbol: {}", symbol);
                return;
            }

            JsonNode orderJson = commandJson.get(ORDER);
            if (orderJson == null || orderJson.isNull()) {
                log.error("Null order data in PLACE_ORDER command for symbol: {}", symbol);
                return;
            }

            Object order;
            order = objectMapper.treeToValue(orderJson, Object.class);

            // Process order through Future-Core compatibility service (internal method)
            FutureCoreTradeResult result = futureCoreService.processContractOrderInternal(order, commandType);

            if (result.isSuccess()) {
                if (result.getTradePlate() != null) {
                    FutureTradePlateMessage message = FutureTradePlateMessage.createTradePlateMessage(symbol, result.getTradePlate());
                    futureCoreKafkaProducer.publishContractTradePlate(symbol, message);
                }
                // Publish OUTPUT: completed orders if any
                if (result.getCompletedOrder() != null) {
                    futureCoreKafkaProducer.publishContractOrderCompleted(symbol, result.getCompletedOrder());
                }
                // PERFORMANCE OPTIMIZATION: Publish trades in batch instead of individually
                if (result.getTrades() != null && !result.getTrades().isEmpty()) {
                    // Publish OUTPUT: order placed event
                    futureCoreKafkaProducer.publishOrderPlacedEvent(result.getTrades(), OrderEventType.ORDER_PLACED, symbol, result.getOrderId());
                    futureCoreKafkaProducer.publishContractTradeBatch(result.getTrades(), result.getSymbol());
                }
                log.info("Successfully processed PLACE_ORDER command for symbol: {} with {} trades",
                        symbol, result.getTrades() != null ? result.getTrades().size() : 0);
            } else {
                log.warn("Failed to process PLACE_ORDER command for symbol: {} - {}",
                        symbol, result.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("Unexpected error handling PLACE_ORDER command for symbol: {}", symbol, e);
        }
    }

    /**
     * Handle CANCEL_ORDER command
     */
    private void handleCancelOrderCommand(JsonNode commandJson, String symbol) {
        try {
            JsonNode orderJson = commandJson.get(ORDER);
            Object order = objectMapper.treeToValue(orderJson, Object.class);
            String type = Optional.ofNullable(commandJson.get("type"))
                    .filter(node -> !node.isNull())
                    .map(JsonNode::asText)
                    .orElse(null);

            // Process order cancellation through Future-Core compatibility service (internal method)
            FutureCoreTradeResult result = futureCoreService.processContractOrderCancelInternal(order);
            result.setType(type);

            // Publish OUTPUT: cancel message (consistent with FutureCoreOrderConsumer)
            futureCoreKafkaProducer.publishContractOrderCancelResult(symbol, result);
            processCancelOrderResult(result);

        } catch (Exception e) {
            log.error("Error handling CANCEL_ORDER command for symbol: {}", symbol, e);
        }
    }

    /**
     * Handles the "CANCEL_ALL_ORDER" command for a given symbol by processing
     * all specified orders in the command JSON. The method parses the orders,
     * cancels each one, and publishes the cancellation results.
     *
     * @param commandJson The JSON payload containing the "CANCEL_ALL_ORDER" command
     *                    and the list of orders to be canceled.
     * @param symbol      The symbol for which the orders need to be canceled.
     */
    public void handleCancelOrderCommandAll(JsonNode commandJson, String symbol) {
        try {
            log.info("[Matching-Cancel-All] Start handling CANCEL_ALL_ORDER command for symbol: {}", symbol);

            JsonNode orderJson = commandJson.get(CommonConstance.ORDER_LIST);
            if (orderJson == null || !orderJson.isArray()) {
                log.warn("[Matching-Cancel-All] No valid 'orderList' field found in commandJson for symbol: {}", symbol);
                return;
            }

            List<Object> orderList = objectMapper.readValue(
                    orderJson.traverse(),
                    new TypeReference<>() {
                    }
            );

            log.info("[Matching-Cancel-All] Received {} orderList to cancel for symbol: {}", orderList.size(), symbol);

            List<FutureCoreTradeResult> tradeResults = new ArrayList<>();
            for (Object order : orderList) {
                log.info("[Matching-Cancel-All] Processing cancel for order: {}", order);
                FutureCoreTradeResult result = futureCoreService.processContractOrderCancelInternal(order);
                result.setType("CANCEL_ALL_ORDER");
                tradeResults.add(result);
                processCancelOrderResult(result);
            }

            if (tradeResults.isEmpty()) {
                log.warn("[Matching-Cancel-All] No trade results found after processing cancel orderList for symbol: {}", symbol);
                return;
            }

            log.info("[Matching-Cancel-All] Publishing {} cancel results to Kafka for symbol: {}", tradeResults.size(), symbol);
            futureCoreKafkaProducer.publishContractOrderCancelAllResult(symbol, tradeResults);

            log.info("[Matching-Cancel-All] Finished processing CANCEL_ORDER_ALL for symbol: {}", symbol);

        } catch (Exception e) {
            log.error("[Matching-Cancel-All] Error handling CANCEL_ORDER_ALL command for symbol: {}", symbol, e);
        }
    }

    /**
     * Processes the result of a cancel order operation and handles subsequent actions such as
     * publishing order cancellation events and potentially updating trade plate information.
     *
     * @param result the result of the cancel order operation containing success status and additional details
     */
    private void processCancelOrderResult(FutureCoreTradeResult result) {
        if (result.isSuccess()) {

            // Publish OUTPUT: trade plate update if needed
            if (result.getTradePlate() != null) {
                FutureTradePlateMessage message = FutureTradePlateMessage.createTradePlateMessage(result.getSymbol(), result.getTradePlate());
                futureCoreKafkaProducer.publishContractTradePlate(result.getSymbol(), message);
            }

            log.info("Successfully processed CANCEL_ORDER command for symbol: {}", result.getSymbol());
        } else {
            log.warn("Failed to process CANCEL_ORDER command for symbol: {} - {}",
                    result.getSymbol(), result.getErrorMessage());

        }
    }


    /**
     * Handle UPDATE_ORDER command
     */
    private void handleUpdateOrderCommand(JsonNode commandJson, String symbol, String commandType) {
        try {
            JsonNode orderJson = commandJson.get(ORDER);
            Object order = objectMapper.treeToValue(orderJson, Object.class);

            // Process order update through Future-Core compatibility service (internal methods)
            // Hiện tại implement như cancel + place order mới
            FutureCoreTradeResult cancelResult = futureCoreService.processContractOrderCancelInternal(order);

            if (cancelResult.isSuccess()) {
                // Place order mới
                FutureCoreTradeResult placeResult = futureCoreService.processContractOrderInternal(order, commandType);

                if (placeResult.isSuccess()) {
                    // Publish OUTPUT: order updated event
                    futureCoreKafkaProducer.publishOrderUpdatedEvent(order);

                    // PERFORMANCE OPTIMIZATION: Publish trades in batch
                    if (placeResult.getTrades() != null && !placeResult.getTrades().isEmpty()) {
                        futureCoreKafkaProducer.publishContractTradeBatch(placeResult.getTrades(), placeResult.getSymbol());
                    }

                    log.info("Successfully processed UPDATE_ORDER command for symbol: {}", symbol);
                } else {
                    log.warn("Failed to place new order in UPDATE_ORDER command for symbol: {} - {}",
                            symbol, placeResult.getErrorMessage());
                }
            } else {
                log.warn("Failed to cancel old order in UPDATE_ORDER command for symbol: {} - {}",
                        symbol, cancelResult.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("Error handling UPDATE_ORDER command for symbol: {}", symbol, e);
        }
    }

    /**
     * Shutdown executor
     */
    public void shutdown() {
        processingExecutor.shutdown();
    }
}
