package com.icetea.lotus.matching.infrastructure.engine;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * Key cho order trong ConcurrentSkipListMap với timestamp để đảm bảo thứ tự FIFO
 * Sử dụng timestamp + sequence number để đảm bảo tính duy nhất và thứ tự
 * 
 * <AUTHOR> nguyen
 */
@Getter
@ToString
@EqualsAndHashCode
public class TimestampedOrderKey implements Comparable<TimestampedOrderKey> {
    
    private final String orderId;
    private final long timestamp;
    private final long sequenceNumber;
    
    /**
     * Constructor tạo key với timestamp hiện tại
     * @param orderId ID của order
     * @param sequenceNumber Sequence number để đảm bảo tính duy nhất
     */
    public TimestampedOrderKey(String orderId, long sequenceNumber) {
        this.orderId = Objects.requireNonNull(orderId, "OrderId không được null");
        this.timestamp = System.nanoTime(); // Sử dụng nanoTime để độ chính xác cao hơn
        this.sequenceNumber = sequenceNumber;
    }
    
    /**
     * Constructor đầy đủ (dùng cho testing hoặc khôi phục)
     * @param orderId ID của order
     * @param timestamp Timestamp
     * @param sequenceNumber Sequence number
     */
    public TimestampedOrderKey(String orderId, long timestamp, long sequenceNumber) {
        this.orderId = Objects.requireNonNull(orderId, "OrderId không được null");
        this.timestamp = timestamp;
        this.sequenceNumber = sequenceNumber;
    }
    
    /**
     * So sánh để duy trì thứ tự FIFO trong ConcurrentSkipListMap
     * Thứ tự ưu tiên:
     * 1. Timestamp (sớm hơn = nhỏ hơn) - FIFO
     * 2. Sequence number (nếu timestamp giống nhau)
     * 3. OrderId (nếu cả timestamp và sequence giống nhau)
     * 
     * @param other Key khác để so sánh
     * @return -1 nếu nhỏ hơn, 0 nếu bằng, 1 nếu lớn hơn
     */
    @Override
    public int compareTo(TimestampedOrderKey other) {
        if (other == null) {
            return 1;
        }
        
        // So sánh timestamp trước (FIFO - sớm hơn = nhỏ hơn)
        int timestampComparison = Long.compare(this.timestamp, other.timestamp);
        if (timestampComparison != 0) {
            return timestampComparison;
        }
        
        // Nếu timestamp giống nhau, so sánh sequence number
        int sequenceComparison = Long.compare(this.sequenceNumber, other.sequenceNumber);
        if (sequenceComparison != 0) {
            return sequenceComparison;
        }
        
        // Cuối cùng so sánh orderId để đảm bảo tính nhất quán
        return this.orderId.compareTo(other.orderId);
    }
    
    /**
     * Kiểm tra xem key này có cùng orderId với key khác không
     * @param other Key khác
     * @return true nếu cùng orderId
     */
    public boolean hasSameOrderId(TimestampedOrderKey other) {
        return other != null && this.orderId.equals(other.orderId);
    }
    
    /**
     * Kiểm tra xem key này có cùng orderId với string orderId không
     * @param orderIdStr String orderId
     * @return true nếu cùng orderId
     */
    public boolean hasSameOrderId(String orderIdStr) {
        return this.orderId.equals(orderIdStr);
    }
    
    /**
     * Lấy thời gian tạo dưới dạng milliseconds
     * @return timestamp in milliseconds
     */
    public long getTimestampMillis() {
        return timestamp / 1_000_000; // Convert nanoseconds to milliseconds
    }
    
    /**
     * Kiểm tra xem key này có cũ hơn thời gian cho trước không
     * @param ageInNanos Thời gian tính bằng nanoseconds
     * @return true nếu cũ hơn
     */
    public boolean isOlderThan(long ageInNanos) {
        return (System.nanoTime() - timestamp) > ageInNanos;
    }
    
    /**
     * Tạo key cho việc tìm kiếm theo orderId
     * Key này sẽ có timestamp = 0 để luôn đứng đầu khi tìm kiếm
     * @param orderId OrderId cần tìm
     * @return Key tìm kiếm
     */
    public static TimestampedOrderKey searchKey(String orderId) {
        return new TimestampedOrderKey(orderId, 0L, 0L);
    }
    
    /**
     * Tạo key cho việc tìm kiếm cuối theo orderId
     * Key này sẽ có timestamp = Long.MAX_VALUE để luôn đứng cuối khi tìm kiếm
     * @param orderId OrderId cần tìm
     * @return Key tìm kiếm cuối
     */
    public static TimestampedOrderKey searchKeyEnd(String orderId) {
        return new TimestampedOrderKey(orderId, Long.MAX_VALUE, Long.MAX_VALUE);
    }
}
