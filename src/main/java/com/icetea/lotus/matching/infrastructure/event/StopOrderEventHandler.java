package com.icetea.lotus.matching.infrastructure.event;

import com.icetea.lotus.matching.domain.event.StopOrderTriggeredEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * Event Handler for Stop Order Domain Events
 * Handles infrastructure concerns triggered by domain events
 * 
 * <AUTHOR> nguyen
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StopOrderEventHandler {
    
    /**
     * Handle stop order triggered event
     * This runs asynchronously to not block the main trigger flow
     * 
     * @param event The stop order triggered event
     */
    @Async
    @EventListener
    public void handleStopOrderTriggered(StopOrderTriggeredEvent event) {
        log.info("Handling stop order triggered event: {}", event);
        
        try {
            // Log the event for audit purposes
            logStopOrderTrigger(event);
            
            // Additional infrastructure concerns can be added here:
            // - Metrics collection
            // - Audit logging
            // - Notifications
            // - External system integrations
            
        } catch (Exception e) {
            log.error("Error handling stop order triggered event: {}", event.getEventId(), e);
            // Don't rethrow to avoid affecting the main flow
        }
    }
    
    /**
     * Log stop order trigger for audit purposes
     * 
     * @param event The stop order triggered event
     */
    private void logStopOrderTrigger(StopOrderTriggeredEvent event) {
        log.info("STOP_ORDER_AUDIT: stopOrderId={}, symbol={}, memberId={}, " +
                "triggerPrice={}, currentPrice={}, reason={}, triggeredOrderId={}, occurredAt={}",
                event.getStopOrderId(),
                event.getSymbol(),
                event.getMemberId(),
                event.getStopOrder().getTriggerPrice().getAmount(),
                event.getCurrentMarketPrice().getAmount(),
                event.getTriggerReason(),
                event.getTriggeredOrderId(),
                event.getOccurredAt());
    }
}
