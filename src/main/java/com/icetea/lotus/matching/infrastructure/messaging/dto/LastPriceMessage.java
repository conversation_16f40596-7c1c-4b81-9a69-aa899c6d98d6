package com.icetea.lotus.matching.infrastructure.messaging.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Last Price Message DTO
 * Extracted từ ExchangeKafkaProducer và FutureCoreKafkaProducer
 * 
 * <AUTHOR> nguyen
 */
@Data
@Builder
public class LastPriceMessage {
    private String symbol;
    private BigDecimal price;
    private BigDecimal volume;
    private LocalDateTime timestamp;
}
