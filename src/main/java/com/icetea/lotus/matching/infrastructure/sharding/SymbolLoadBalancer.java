package com.icetea.lotus.matching.infrastructure.sharding;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Symbol Load Balancer for Distributed Matching Engine
 * Quản lý cân bằng tải symbols giữa các matching engine pods
 * 
 * <AUTHOR> nguyen
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SymbolLoadBalancer {

    private final RedissonClient redissonClient;
    private final SymbolShardingManager shardingManager;
    
    // Constants
    private static final String SYMBOL_TO_POD_MAP = "matching-engine:symbol-to-pod-map";
    private static final String POD_HEARTBEAT_MAP = "matching-engine:pod-heartbeat";
    private static final String LOAD_BALANCING_LOCK = "matching-engine:load-balancing-lock";
    private static final String POD_LOAD_MAP = "matching-engine:pod-load";
    
    // Configuration
    private static final long HEARTBEAT_TIMEOUT = 60000; // 1 minute
    private static final long LOAD_BALANCE_INTERVAL = 300000; // 5 minutes
    private static final double LOAD_IMBALANCE_THRESHOLD = 0.3; // 30% imbalance
    
    /**
     * OPTIMIZATION: Periodic load balancing
     * Runs every 5 minutes to rebalance symbols across pods
     */
    @Scheduled(fixedRate = LOAD_BALANCE_INTERVAL)
    public void performLoadBalancing() {
        String podName = shardingManager.getPodName();
        RLock lock = redissonClient.getLock(LOAD_BALANCING_LOCK);
        
        try {
            // Only one pod should perform load balancing at a time
            if (lock.tryLock(10, 30, TimeUnit.SECONDS)) {
                try {
                    log.info("Starting load balancing process from pod: {}", podName);
                    
                    // Get active pods
                    Set<String> activePods = getActivePods();
                    if (activePods.size() < 2) {
                        log.info("Not enough active pods for load balancing: {}", activePods.size());
                        return;
                    }
                    
                    // Calculate current load distribution
                    Map<String, Integer> podLoadMap = calculatePodLoads(activePods);
                    
                    // Check if rebalancing is needed
                    if (isRebalancingNeeded(podLoadMap)) {
                        performRebalancing(podLoadMap, activePods);
                    } else {
                        log.info("Load is balanced, no rebalancing needed");
                    }
                    
                } finally {
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            } else {
                log.info("Could not acquire load balancing lock, skipping this cycle");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Interrupted during load balancing", e);
        } catch (Exception e) {
            log.error("Error during load balancing", e);
        }
    }
    
    /**
     * OPTIMIZATION: Update pod heartbeat
     * Called periodically to indicate pod is alive
     */
    @Scheduled(fixedRate = 30000) // Every 30 seconds
    public void updateHeartbeat() {
        try {
            String podName = shardingManager.getPodName();
            RMap<String, Long> heartbeatMap = redissonClient.getMap(POD_HEARTBEAT_MAP);
            heartbeatMap.put(podName, System.currentTimeMillis());
            
            log.info("Updated heartbeat for pod: {}", podName);
        } catch (Exception e) {
            log.error("Error updating heartbeat", e);
        }
    }
    
    /**
     * Get active pods based on heartbeat
     * @return Set of active pod names
     */
    public Set<String> getActivePods() {
        try {
            RMap<String, Long> heartbeatMap = redissonClient.getMap(POD_HEARTBEAT_MAP);
            long currentTime = System.currentTimeMillis();
            
            return heartbeatMap.entrySet().stream()
                    .filter(entry -> (currentTime - entry.getValue()) < HEARTBEAT_TIMEOUT)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toSet());
                    
        } catch (Exception e) {
            log.error("Error getting active pods", e);
            return Collections.emptySet();
        }
    }
    
    /**
     * Calculate load for each pod
     * @param activePods Set of active pods
     * @return Map of pod -> symbol count
     */
    private Map<String, Integer> calculatePodLoads(Set<String> activePods) {
        try {
            RMap<String, String> symbolToPodMap = redissonClient.getMap(SYMBOL_TO_POD_MAP);
            Map<String, Integer> loadMap = new HashMap<>();
            
            // Initialize with 0 for all active pods
            for (String pod : activePods) {
                loadMap.put(pod, 0);
            }
            
            // Count symbols per pod
            for (String pod : symbolToPodMap.values()) {
                if (activePods.contains(pod)) {
                    loadMap.put(pod, loadMap.getOrDefault(pod, 0) + 1);
                }
            }
            
            // Store load information in Redis for monitoring
            RMap<String, Integer> podLoadMap = redissonClient.getMap(POD_LOAD_MAP);
            podLoadMap.putAll(loadMap);
            
            return loadMap;
            
        } catch (Exception e) {
            log.error("Error calculating pod loads", e);
            return Collections.emptyMap();
        }
    }
    
    /**
     * Check if rebalancing is needed based on load distribution
     * @param podLoadMap Current load distribution
     * @return true if rebalancing is needed
     */
    private boolean isRebalancingNeeded(Map<String, Integer> podLoadMap) {
        if (podLoadMap.isEmpty()) {
            return false;
        }
        
        int totalSymbols = podLoadMap.values().stream().mapToInt(Integer::intValue).sum();
        int podCount = podLoadMap.size();
        
        if (totalSymbols == 0 || podCount == 0) {
            return false;
        }
        
        double averageLoad = (double) totalSymbols / podCount;
        
        // Check if any pod is significantly over or under loaded
        for (int load : podLoadMap.values()) {
            double deviation = Math.abs(load - averageLoad) / averageLoad;
            if (deviation > LOAD_IMBALANCE_THRESHOLD) {
                log.info("Load imbalance detected: average={}, deviation={}", averageLoad, deviation);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Perform actual rebalancing of symbols
     * @param podLoadMap Current load distribution
     * @param activePods Set of active pods
     */
    @SuppressWarnings("java:S3776")
    private void performRebalancing(Map<String, Integer> podLoadMap, Set<String> activePods) {
        try {
            RMap<String, String> symbolToPodMap = redissonClient.getMap(SYMBOL_TO_POD_MAP);
            
            int totalSymbols = podLoadMap.values().stream().mapToInt(Integer::intValue).sum();
            int targetLoad = totalSymbols / activePods.size();
            
            // Find overloaded and underloaded pods
            List<String> overloadedPods = new ArrayList<>();
            List<String> underloadedPods = new ArrayList<>();
            
            for (Map.Entry<String, Integer> entry : podLoadMap.entrySet()) {
                if (entry.getValue() > targetLoad + 1) {
                    overloadedPods.add(entry.getKey());
                } else if (entry.getValue() < targetLoad) {
                    underloadedPods.add(entry.getKey());
                }
            }
            
            // Move symbols from overloaded to underloaded pods
            for (String overloadedPod : overloadedPods) {
                if (underloadedPods.isEmpty()) {
                    break;
                }
                
                // Find symbols owned by overloaded pod
                List<String> symbolsToMove = symbolToPodMap.entrySet().stream()
                        .filter(entry -> overloadedPod.equals(entry.getValue()))
                        .map(Map.Entry::getKey)
                        .limit(Math.max(0, podLoadMap.get(overloadedPod) - targetLoad))
                        .toList();
                
                // Move symbols to underloaded pods
                for (String symbol : symbolsToMove) {
                    if (underloadedPods.isEmpty()) {
                        break;
                    }
                    
                    String underloadedPod = underloadedPods.get(0);
                    symbolToPodMap.put(symbol, underloadedPod);
                    
                    // Update load counts
                    podLoadMap.put(overloadedPod, podLoadMap.get(overloadedPod) - 1);
                    podLoadMap.put(underloadedPod, podLoadMap.get(underloadedPod) + 1);
                    
                    log.info("Moved symbol {} from {} to {}", symbol, overloadedPod, underloadedPod);
                    
                    // Remove underloaded pod if it reaches target
                    if (podLoadMap.get(underloadedPod) >= targetLoad) {
                        underloadedPods.remove(0);
                    }
                }
            }
            
            log.info("Load balancing completed. New distribution: {}", podLoadMap);
            
        } catch (Exception e) {
            log.error("Error performing rebalancing", e);
        }
    }
    
    /**
     * Get current load distribution for monitoring
     * @return Map of pod -> symbol count
     */
    public Map<String, Integer> getCurrentLoadDistribution() {
        Set<String> activePods = getActivePods();
        return calculatePodLoads(activePods);
    }
    
    /**
     * Force rebalancing (for admin operations)
     */
    public void forceRebalancing() {
        log.info("Force rebalancing triggered");
        performLoadBalancing();
    }

    // ===== ADVANCED LOAD BALANCING FEATURES =====

    /**
     * OPTIMIZATION: Hash-based symbol distribution for consistent hashing
     * @param symbol Symbol to get preferred pod
     * @param activePods Set of active pods
     * @return Preferred pod for symbol
     */
    public String getPreferredPodForSymbol(String symbol, Set<String> activePods) {
        if (activePods.isEmpty()) {
            return null;
        }

        // Use consistent hashing for symbol distribution
        int hash = Math.abs(Integer.parseInt(symbol));
        List<String> sortedPods = new ArrayList<>(activePods);
        Collections.sort(sortedPods);

        int podIndex = hash % sortedPods.size();
        return sortedPods.get(podIndex);
    }

    /**
     * OPTIMIZATION: Migrate symbol to preferred pod if needed
     * @param symbol Symbol to migrate
     * @return true if migration was performed
     */
    public boolean migrateSymbolToPreferredPod(String symbol) {
        try {
            Set<String> activePods = getActivePods();
            String preferredPod = getPreferredPodForSymbol(symbol, activePods);
            String currentOwner = shardingManager.getSymbolOwner(symbol);

            if (preferredPod != null && !preferredPod.equals(currentOwner)) {
                RMap<String, String> symbolToPodMap = redissonClient.getMap(SYMBOL_TO_POD_MAP);
                symbolToPodMap.put(symbol, preferredPod);

                log.info("Migrated symbol {} from {} to preferred pod {}",
                        symbol, currentOwner, preferredPod);
                return true;
            }

            return false;

        } catch (Exception e) {
            log.error("Error migrating symbol {} to preferred pod", symbol, e);
            return false;
        }
    }

    /**
     * OPTIMIZATION: Get load balancing statistics
     * @return Load balancing statistics
     */
    public LoadBalancingStats getLoadBalancingStats() {
        try {
            Set<String> activePods = getActivePods();
            Map<String, Integer> loadMap = calculatePodLoads(activePods);

            if (loadMap.isEmpty()) {
                return new LoadBalancingStats(0, 0, 0, 0.0, Collections.emptyMap());
            }

            int totalSymbols = loadMap.values().stream().mapToInt(Integer::intValue).sum();
            int minLoad = loadMap.values().stream().mapToInt(Integer::intValue).min().orElse(0);
            int maxLoad = loadMap.values().stream().mapToInt(Integer::intValue).max().orElse(0);
            double averageLoad = (double) totalSymbols / activePods.size();

            return new LoadBalancingStats(totalSymbols, minLoad, maxLoad, averageLoad, loadMap);

        } catch (Exception e) {
            log.error("Error getting load balancing stats", e);
            return new LoadBalancingStats(0, 0, 0, 0.0, Collections.emptyMap());
        }
    }

    /**
     * Load balancing statistics data class
     */
    public static class LoadBalancingStats {
        public final int totalSymbols;
        public final int minLoad;
        public final int maxLoad;
        public final double averageLoad;
        public final Map<String, Integer> podLoadMap;

        public LoadBalancingStats(int totalSymbols, int minLoad, int maxLoad,
                                double averageLoad, Map<String, Integer> podLoadMap) {
            this.totalSymbols = totalSymbols;
            this.minLoad = minLoad;
            this.maxLoad = maxLoad;
            this.averageLoad = averageLoad;
            this.podLoadMap = new HashMap<>(podLoadMap);
        }

        public double getLoadImbalanceRatio() {
            if (averageLoad == 0) return 0.0;
            return Math.max(
                Math.abs(maxLoad - averageLoad) / averageLoad,
                Math.abs(minLoad - averageLoad) / averageLoad
            );
        }

        @Override
        public String toString() {
            return String.format("LoadBalancingStats{total=%d, min=%d, max=%d, avg=%.2f, imbalance=%.2f}",
                    totalSymbols, minLoad, maxLoad, averageLoad, getLoadImbalanceRatio());
        }
    }
}
