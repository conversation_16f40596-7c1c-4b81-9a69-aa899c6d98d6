package com.icetea.lotus.matching.infrastructure.exchange;

import com.icetea.lotus.matching.infrastructure.messaging.dto.ExchangeOrderMessage;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Exchange Trade Result - Kế<PERSON> quả xử lý order theo Exchange format
 * Tương thích với Exchange module response format
 * 
 * <AUTHOR> nguyen
 */
@Data
@Builder
public class ExchangeTradeResult {
    
    private boolean success;
    private String orderId;
    private String symbol;
    private List<ExchangeTradeData> trades;
    private int tradesCount;
    private String message;
    private String errorCode;
    private Long timestamp;

    // Additional fields for compatibility
    private List<Object> completedOrders;
    private List<Object> partiallyFilledOrders;
    private Object tradePlate;
    private ExchangeOrderMessage cancelResult;
    
    /**
     * Create success result
     */
    public static ExchangeTradeResult success(String orderId, String symbol, List<ExchangeTradeData> trades) {
        return ExchangeTradeResult.builder()
                .success(true)
                .orderId(orderId)
                .symbol(symbol)
                .trades(trades)
                .tradesCount(trades.size())
                .message("Order processed successfully")
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * Create error result
     */
    public static ExchangeTradeResult error(String message) {
        return ExchangeTradeResult.builder()
                .success(false)
                .message(message)
                .errorCode("PROCESSING_ERROR")
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * Create error result with code
     */
    public static ExchangeTradeResult error(String errorCode, String message) {
        return ExchangeTradeResult.builder()
                .success(false)
                .message(message)
                .errorCode(errorCode)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * Get completed orders for compatibility
     */
    public List<Object> getCompletedOrders() {
        return completedOrders != null ? completedOrders : List.of();
    }

    /**
     * Get partially filled orders for compatibility
     */
    public List<Object> getPartiallyFilledOrders() {
        return partiallyFilledOrders != null ? partiallyFilledOrders : List.of();
    }

    /**
     * Get trade plate for compatibility
     */
    public Object getTradePlate() {
        return tradePlate;
    }

    /**
     * Get error message for compatibility
     */
    public String getErrorMessage() {
        return message;
    }

    /**
     * Get cancel result for compatibility
     */
    public ExchangeOrderMessage getCancelResult() {
        return cancelResult;
    }
}
