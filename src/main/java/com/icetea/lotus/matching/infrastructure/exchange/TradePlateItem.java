package com.icetea.lotus.matching.infrastructure.exchange;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigDecimal;

/**
 * TradePlateItem - Copy từ Exchange module
 * Exact same structure như exchange-core.TradePlateItem
 * Represents a price level in the order book
 * 
 * <AUTHOR> nguyen
 */
@Data
public class TradePlateItem {

    // Explicit getters and setters for compilation
    private String orderId;
    private BigDecimal price;
    private BigDecimal amount;
    
    public TradePlateItem() {
        // Default constructor
    }
    
    public TradePlateItem(BigDecimal price, BigDecimal amount) {
        this.price = price;
        this.amount = amount;
    }
    
    public TradePlateItem(String orderId, BigDecimal price, BigDecimal amount) {
        this.orderId = orderId;
        this.price = price;
        this.amount = amount;
    }

    /**
     * Check if item is valid
     */
    @JsonIgnore
    public boolean isValid() {
        return price != null && price.compareTo(BigDecimal.ZERO) > 0 &&
               amount != null && amount.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * Calculate turnover (price * amount)
     */
    @JsonIgnore
    public BigDecimal getTurnover() {
        if (price == null || amount == null) {
            return BigDecimal.ZERO;
        }
        return price.multiply(amount);
    }
    
    /**
     * Create copy of this item
     */
    public TradePlateItem copy() {
        return new TradePlateItem(this.orderId, this.price, this.amount);
    }
    
    @Override
    public String toString() {
        return String.format("{\"orderId\":\"%s\",\"price\":%s,\"amount\":%s}", 
                orderId != null ? orderId : "", price, amount);
    }
}
