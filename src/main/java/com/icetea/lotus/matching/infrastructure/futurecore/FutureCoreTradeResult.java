package com.icetea.lotus.matching.infrastructure.futurecore;

import com.icetea.lotus.matching.domain.entity.Trade;
import com.icetea.lotus.matching.domain.enums.TradeStatus;
import com.icetea.lotus.matching.infrastructure.messaging.dto.FutureTradePlateMessage;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Future-Core Trade Result - Kết quả xử lý order theo Future-Core format
 * Tương thích với DistributedLockFreeMatchingEngine response format
 * 
 * <AUTHOR> nguyen
 */
@Data
@Builder
public class FutureCoreTradeResult {
    
    private boolean success;
    private TradeStatus status;
    private String orderId;
    private String symbol;
    private List<Trade> trades;
    private int tradesCount;
    private String algorithm;
    private PerformanceMetrics performanceMetrics;
    private String message;
    private String errorCode;
    private Long timestamp;
    private String type;

    // Additional fields for compatibility
    private Object completedOrder;
    private Object positionUpdate;
    private FutureTradePlateMessage.OrderBookSnapshot tradePlate;
    private Object cancelResult;
    private Boolean isSTP;
    
    /**
     * Create success result
     */
    public static FutureCoreTradeResult success(String orderId, String symbol, List<Trade> trades, 
                                               String algorithm, PerformanceMetrics metrics) {
        return FutureCoreTradeResult.builder()
                .success(true)
                .orderId(orderId)
                .symbol(symbol)
                .trades(trades)
                .tradesCount(trades.size())
                .algorithm(algorithm)
                .performanceMetrics(metrics)
                .message("Order processed successfully with Future-Core logic")
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * Create error result
     */
    public static FutureCoreTradeResult error(String message) {
        return FutureCoreTradeResult.builder()
                .success(false)
                .message(message)
                .errorCode("PROCESSING_ERROR")
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * Create error result with code
     */
    public static FutureCoreTradeResult error(String errorCode, String message) {
        return FutureCoreTradeResult.builder()
                .success(false)
                .message(message)
                .errorCode(errorCode)
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * Get performance summary
     */
    public String getPerformanceSummary() {
        if (performanceMetrics != null) {
            return performanceMetrics.getSummary();
        }
        return "No performance metrics available";
    }
    
    /**
     * Check if result has high performance
     */
    public boolean isHighPerformance() {
        return performanceMetrics != null &&
               performanceMetrics.getCasSuccessRate() >= 0.95 &&
               performanceMetrics.getThroughputTPS() > 1000;
    }

    /**
     * Get completed order for compatibility
     */
    public Object getCompletedOrder() {
        return completedOrder;
    }

    /**
     * Get position update for compatibility
     */
    public Object getPositionUpdate() {
        return positionUpdate;
    }

    /**
     * Get trade plate for compatibility
     */
    public FutureTradePlateMessage.OrderBookSnapshot getTradePlate() {
        return tradePlate;
    }

    /**
     * Get error message for compatibility
     */
    public String getErrorMessage() {
        return message;
    }
}
