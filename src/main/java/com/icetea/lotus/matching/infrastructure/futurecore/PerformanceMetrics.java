package com.icetea.lotus.matching.infrastructure.futurecore;

import lombok.Builder;
import lombok.Data;

/**
 * Performance Metrics - Copy từ Future-Core performance monitoring
 * Track matching engine performance metrics cho optimization
 * 
 * <AUTHOR> nguyen
 */
@Data
@Builder
public class PerformanceMetrics {
    
    private long totalOrdersProcessed;
    private long totalTradesGenerated;
    private long casSuccessCount;
    private long casFailureCount;
    private double casSuccessRate;
    private long averageProcessingTimeNs;
    private long maxProcessingTimeNs;
    private long minProcessingTimeNs;
    private double throughputTPS;
    
    /**
     * Calculate trades per order ratio
     */
    public double getTradesPerOrder() {
        if (totalOrdersProcessed == 0) {
            return 0.0;
        }
        return (double) totalTradesGenerated / totalOrdersProcessed;
    }
    
    /**
     * Get CAS contention level
     */
    public String getCasContentionLevel() {
        if (casSuccessRate >= 0.95) {
            return "LOW";
        } else if (casSuccessRate >= 0.85) {
            return "MEDIUM";
        } else {
            return "HIGH";
        }
    }
    
    /**
     * Check if performance is healthy
     */
    public boolean isHealthy() {
        return casSuccessRate >= 0.8 && throughputTPS > 0;
    }
    
    /**
     * Get performance summary
     */
    public String getSummary() {
        return String.format(
                "Orders: %d, Trades: %d, TPS: %.2f, CAS Success: %.2f%%, Contention: %s",
                totalOrdersProcessed, totalTradesGenerated, throughputTPS, 
                casSuccessRate * 100, getCasContentionLevel());
    }
}
