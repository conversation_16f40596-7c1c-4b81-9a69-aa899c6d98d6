package com.icetea.lotus.matching.infrastructure.sharding;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.ApplicationListener;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.StampedLock;

import java.util.concurrent.ConcurrentSkipListMap;
import java.util.List;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Map;
import java.util.HashMap;

/**
 * Symbol Sharding Manager for Matching Engine
 * Quản lý phân phối các symbol gi<PERSON>a các matching engine pods
 * Migrated từ Future-Core SymbolShardingManager
 *
 * <AUTHOR> nguyen
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SymbolShardingManager implements ApplicationListener<ContextRefreshedEvent> {

    private final RedissonClient redissonClient;
    private final PodSymbolMapper podSymbolMapper;

    // Constants
    private static final String SYMBOL_TO_POD_MAP = "matching-engine:symbol-to-pod-map";
    private static final String SYMBOL_REBALANCING_LOCK = "matching-engine:symbol-rebalancing-lock";

    // Tên của pod
    @Getter
    @Value("${matching-engine.pod-name:${HOSTNAME:matching-engine-pod}}")
    private String podName;

    // Pod-specific symbols configuration
    @Value("${matching-engine.pod-symbols:}")
    private String podSymbolsConfig;

    @Value("${matching-engine.auto-init-pod-symbols:true}")
    private boolean autoInitPodSymbols;

    // OPTIMIZED: ConcurrentSkipListMap cho better cache locality và ordered access
    private final ConcurrentSkipListMap<String, Boolean> symbolOwnershipCache = new ConcurrentSkipListMap<>();

    // OPTIMIZATION: Advanced locking mechanisms
    private final StampedLock cacheLock = new StampedLock();
    private static final long DEFAULT_LOCK_WAIT_TIME = 100; // milliseconds
    private static final long DEFAULT_LOCK_LEASE_TIME = 5000; // milliseconds

    // Flag để tránh khởi tạo nhiều lần
    private volatile boolean podSymbolsInitialized = false;


    /**
     * Khởi tạo symbols cụ thể cho pod từ profile sau khi application context ready
     */
    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (!autoInitPodSymbols || podSymbolsInitialized) {
            return;
        }

        try {
            initializePodSpecificSymbols();
            podSymbolsInitialized = true;
        } catch (Exception e) {
            log.error("Error initializing pod-specific symbols for pod: {}", podName, e);
        }
    }

    /**
     * Khởi tạo symbols cụ thể cho pod từ profile
     */
    public void initializePodSpecificSymbols() {
        try {
            log.info("======Initializing pod-specific symbols for pod: {}======", podName);

            // Get symbols from configuration or mapper
            List<String> podSymbols = getPodSymbolsToInitialize();

            if (podSymbols.isEmpty()) {
                log.info("No pod-specific symbols to initialize for pod: {}", podName);
                return;
            }

            log.info("Pod-specific symbols to initialize: {}", podSymbols);

            int successCount = 0;
            int skipCount = 0;
            int failCount = 0;

            for (String symbol : podSymbols) {
                // Force claim symbol cho pod này
                boolean claimed = forceClaimSymbol(symbol);

                if (claimed) {
                    successCount++;
                    log.info("Successfully claimed pod-specific symbol: {} for pod: {}", symbol, podName);
                } else {
                    failCount++;
                    log.error("Failed to claim pod-specific symbol: {} for pod: {}", symbol, podName);
                }
            }

            log.info("======Pod-specific symbols initialization completed for pod: {}======", podName);
            log.info("Results: {} success, {} skipped, {} failed out of {} total symbols",
                    successCount, skipCount, failCount, podSymbols.size());

        } catch (Exception e) {
            log.error("Error during pod-specific symbols initialization for pod: {}", podName, e);
        }
    }

    /**
     * Get pod symbols to initialize (from config or mapper)
     */
    private List<String> getPodSymbolsToInitialize() {
        // First try to get from configuration
        List<String> configSymbols = parsePodSymbols();
        if (!configSymbols.isEmpty()) {
            log.info("Using pod symbols from configuration: {}", configSymbols);
            return configSymbols;
        }

        // If no config, use mapper to get symbols based on pod name pattern
        List<String> mapperSymbols = podSymbolMapper.getSymbolsForCurrentPod();
        if (!mapperSymbols.isEmpty()) {
            log.info("Using pod symbols from mapper for pod {}: {}", podName, mapperSymbols);
            return mapperSymbols;
        }

        log.info("No pod-specific symbols found for pod: {}", podName);
        return new ArrayList<>();
    }

    /**
     * Parse pod-specific symbols từ configuration
     */
    private List<String> parsePodSymbols() {
        if (podSymbolsConfig == null || podSymbolsConfig.trim().isEmpty()) {
            return new ArrayList<>();
        }

        return Arrays.stream(podSymbolsConfig.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .toList();
    }

    /**
     * Kiểm tra xem symbol có được gán cho pod này không
     *
     * @param symbol Symbol cần kiểm tra
     * @return true nếu symbol được gán cho pod này, false nếu không
     */
    public boolean isSymbolOwnedByThisPod(String symbol) {
        if (symbol == null || symbol.trim().isEmpty()) {
            return false;
        }

        // Check cache first for performance
        Boolean cached = symbolOwnershipCache.get(symbol);
        if (cached != null) {
            return cached;
        }

        try {
            // Sử dụng Redis để lưu trữ mapping giữa symbol và pod
            RMap<String, String> symbolToPodMap = redissonClient.getMap(SYMBOL_TO_POD_MAP);

            // Nếu symbol chưa được gán cho pod nào, thử gán cho pod hiện tại
            String currentOwner = symbolToPodMap.putIfAbsent(symbol, podName);
            boolean isOwned = currentOwner == null || podName.equals(currentOwner);

            // Cache result for performance
            symbolOwnershipCache.put(symbol, isOwned);

            if (isOwned) {
                log.info("Symbol {} is owned by this pod: {}", symbol, podName);
            } else {
                log.info("Symbol {} is owned by different pod: {}", symbol, currentOwner);
            }

            return isOwned;

        } catch (Exception e) {
            log.error("Error checking symbol ownership for {}: {}", symbol, e.getMessage(), e);
            // Fallback: assume not owned to avoid conflicts
            return false;
        }
    }

    /**
     * Gán symbol cho pod hiện tại
     *
     * @param symbol Symbol cần gán
     * @return true nếu gán thành công, false nếu không
     */
    public boolean claimSymbol(String symbol) {
        if (symbol == null || symbol.trim().isEmpty()) {
            return false;
        }

        try {
            RMap<String, String> symbolToPodMap = redissonClient.getMap(SYMBOL_TO_POD_MAP);

            // Thử gán symbol cho pod hiện tại
            String previousOwner = symbolToPodMap.putIfAbsent(symbol, podName);

            if (previousOwner == null) {
                // Successfully claimed
                symbolOwnershipCache.put(symbol, true);
                log.info("Successfully claimed symbol {} for pod {}", symbol, podName);
                return true;
            } else if (podName.equals(previousOwner)) {
                // Already owned by this pod
                symbolOwnershipCache.put(symbol, true);
                log.info("Symbol {} already owned by this pod {}", symbol, podName);
                return true;
            } else {
                // Owned by different pod
                symbolOwnershipCache.put(symbol, false);
                log.info("Symbol {} is already owned by pod {}", symbol, previousOwner);
                return false;
            }

        } catch (Exception e) {
            log.error("Error claiming symbol {}: {}", symbol, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Giải phóng symbol khỏi pod hiện tại
     *
     * @param symbol Symbol cần giải phóng
     * @return true nếu giải phóng thành công, false nếu không
     */
    public boolean releaseSymbol(String symbol) {
        if (symbol == null || symbol.trim().isEmpty()) {
            return false;
        }

        try {
            RMap<String, String> symbolToPodMap = redissonClient.getMap(SYMBOL_TO_POD_MAP);

            // Chỉ giải phóng nếu symbol thuộc về pod hiện tại
            boolean removed = symbolToPodMap.remove(symbol, podName);

            if (removed) {
                symbolOwnershipCache.remove(symbol);
                log.info("Successfully released symbol {} from pod {}", symbol, podName);
                return true;
            } else {
                log.info("Symbol {} is not owned by this pod {}, cannot release", symbol, podName);
                return false;
            }

        } catch (Exception e) {
            log.error("Error releasing symbol {}: {}", symbol, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Lấy pod owner của symbol
     *
     * @param symbol Symbol cần kiểm tra
     * @return Tên pod owner hoặc null nếu không có
     */
    public String getSymbolOwner(String symbol) {
        if (symbol == null || symbol.trim().isEmpty()) {
            return null;
        }

        try {
            RMap<String, String> symbolToPodMap = redissonClient.getMap(SYMBOL_TO_POD_MAP);
            return symbolToPodMap.get(symbol);

        } catch (Exception e) {
            log.error("Error getting symbol owner for {}: {}", symbol, e.getMessage(), e);
            return null;
        }
    }

    /**
     * Kiểm tra xem có thể xử lý symbol này không
     *
     * @param symbol Symbol cần kiểm tra
     * @return true nếu có thể xử lý, false nếu không
     */
    public boolean canProcessSymbol(String symbol) {
        return isSymbolOwnedByThisPod(symbol);
    }

    /**
     * Force claim symbol (dùng cho rebalancing)
     *
     * @param symbol Symbol cần force claim
     * @return true nếu thành công, false nếu không
     */
    public boolean forceClaimSymbol(String symbol) {
        if (symbol == null || symbol.trim().isEmpty()) {
            return false;
        }

        try {
            RMap<String, String> symbolToPodMap = redissonClient.getMap(SYMBOL_TO_POD_MAP);

            // Force assign symbol to this pod
            symbolToPodMap.put(symbol, podName);
            symbolOwnershipCache.put(symbol, true);

            log.info("Force claimed symbol {} for pod {}", symbol, podName);
            return true;

        } catch (Exception e) {
            log.error("Error force claiming symbol {}: {}", symbol, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Clear cache cho symbol
     *
     * @param symbol Symbol cần clear cache
     */
    public void clearSymbolCache(String symbol) {
        if (symbol != null) {
            symbolOwnershipCache.remove(symbol);
            log.info("Cleared cache for symbol {}", symbol);
        }
    }


    /**
     * Get cache size for monitoring
     *
     * @return Cache size
     */
    public int getCacheSize() {
        return symbolOwnershipCache.size();
    }

    /**
     * Get cache hit ratio for monitoring
     *
     * @return Cache statistics
     */
    public String getCacheStats() {
        return String.format("Cache size: %d symbols for pod: %s",
                symbolOwnershipCache.size(), podName);
    }

    /**
     * Get all symbols owned by this pod
     *
     * @return List of symbols owned by this pod
     */
    public List<String> getOwnedSymbols() {
        try {
            RMap<String, String> symbolToPodMap = redissonClient.getMap(SYMBOL_TO_POD_MAP);

            List<String> ownedSymbols = new ArrayList<>();

            // Iterate through all symbols and find ones owned by this pod
            for (Map.Entry<String, String> entry : symbolToPodMap.entrySet()) {
                if (podName.equals(entry.getValue())) {
                    ownedSymbols.add(entry.getKey());
                }
            }

            log.info("Found {} symbols owned by pod {}: {}", ownedSymbols.size(), podName, ownedSymbols);
            return ownedSymbols;

        } catch (Exception e) {
            log.error("Error getting owned symbols for pod {}: {}", podName, e.getMessage(), e);
            return new ArrayList<>(); // Return empty list on error
        }
    }

    /**
     * Get pod-specific symbols từ configuration
     *
     * @return List of pod-specific symbols
     */
    public List<String> getPodSpecificSymbols() {
        return parsePodSymbols();
    }

    /**
     * Check if pod-specific symbols are initialized
     *
     * @return true if initialized, false otherwise
     */
    public boolean isPodSymbolsInitialized() {
        return podSymbolsInitialized;
    }

    /**
     * Manually trigger pod-specific symbols initialization
     *
     * @return true if successful, false otherwise
     */
    public boolean triggerPodSymbolsInitialization() {
        try {
            initializePodSpecificSymbols();
            podSymbolsInitialized = true;
            return true;
        } catch (Exception e) {
            log.error("Error manually triggering pod-specific symbols initialization", e);
            return false;
        }
    }

    /**
     * Get pod symbol mapping information
     *
     * @return Map with pod symbol mapping details
     */
    public Map<String, Object> getPodSymbolMappingInfo() {
        Map<String, Object> info = new HashMap<>();

        try {
            info.put("podName", podName);
            info.put("podIdentifier", podSymbolMapper.getCurrentPodIdentifier());
            info.put("configuredSymbols", parsePodSymbols());
            info.put("mapperSymbols", podSymbolMapper.getSymbolsForCurrentPod());
            info.put("ownedSymbols", getOwnedSymbols());
            info.put("autoInitEnabled", autoInitPodSymbols);
            info.put("initialized", podSymbolsInitialized);
            info.put("distributionStats", podSymbolMapper.getDistributionStats());

        } catch (Exception e) {
            log.error("Error getting pod symbol mapping info", e);
            info.put("error", e.getMessage());
        }

        return info;
    }

    /**
     * Check if symbol should be handled by this pod based on mapping
     *
     * @param symbol Symbol to check
     * @return true if this pod should handle the symbol, false otherwise
     */
    public boolean shouldHandleSymbol(String symbol) {
        try {
            // Check if symbol is in pod's assigned symbols
            List<String> assignedSymbols = getPodSymbolsToInitialize();
            if (assignedSymbols.contains(symbol)) {
                return true;
            }

            // Check using mapper
            return podSymbolMapper.isSymbolAssignedToPod(symbol, podName);

        } catch (Exception e) {
            log.error("Error checking if pod should handle symbol {}: {}", symbol, e.getMessage(), e);
            // Fallback to ownership check
            return isSymbolOwnedByThisPod(symbol);
        }
    }

    // ===== ADVANCED DISTRIBUTED LOCKING METHODS =====

    /**
     * OPTIMIZATION: Execute operation with distributed lock for symbol
     *
     * @param symbol    Symbol to lock
     * @param operation Operation to execute
     * @param <T>       Return type
     * @return Operation result
     */
    public <T> T executeWithSymbolLock(String symbol, java.util.function.Supplier<T> operation) {
        String lockKey = "matching-engine:symbol-lock:" + symbol;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            if (lock.tryLock(DEFAULT_LOCK_WAIT_TIME, DEFAULT_LOCK_LEASE_TIME, TimeUnit.MILLISECONDS)) {
                try {
                    return operation.get();
                } finally {
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            } else {
                log.warn("Failed to acquire lock for symbol: {} within {}ms", symbol, DEFAULT_LOCK_WAIT_TIME);
                return null;
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Interrupted while waiting for lock on symbol: {}", symbol, e);
            return null;
        } catch (Exception e) {
            log.error("Error executing operation with symbol lock: {}", symbol, e);
            return null;
        }
    }

    /**
     * OPTIMIZATION: Execute operation with rebalancing lock
     *
     * @param operation Operation to execute
     * @param <T>       Return type
     * @return Operation result
     */
    public <T> T executeWithRebalancingLock(java.util.function.Supplier<T> operation) {
        RLock lock = redissonClient.getLock(SYMBOL_REBALANCING_LOCK);

        try {
            if (lock.tryLock(DEFAULT_LOCK_WAIT_TIME, DEFAULT_LOCK_LEASE_TIME, TimeUnit.MILLISECONDS)) {
                try {
                    return operation.get();
                } finally {
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            } else {
                log.warn("Failed to acquire rebalancing lock within {}ms", DEFAULT_LOCK_WAIT_TIME);
                return null;
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Interrupted while waiting for rebalancing lock", e);
            return null;
        } catch (Exception e) {
            log.error("Error executing operation with rebalancing lock", e);
            return null;
        }
    }
}
