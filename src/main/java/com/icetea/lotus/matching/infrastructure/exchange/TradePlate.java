package com.icetea.lotus.matching.infrastructure.exchange;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.icetea.lotus.matching.domain.entity.ExchangeOrder;
import com.icetea.lotus.matching.domain.entity.Order;
import com.icetea.lotus.matching.domain.enums.SpotOrderDirection;
import com.icetea.lotus.matching.domain.enums.SpotOrderType;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

/**
 * TradePlate - Copy từ Exchange module
 * Exact same structure như exchange-core.TradePlate
 * Handicap information cho real-time order book broadcasting
 *
 * <AUTHOR> nguyen
 */
@Data
public class TradePlate {

    private static final Logger logger = LoggerFactory.getLogger(TradePlate.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    // Explicit getters and setters for compilation
    private List<TradePlateItem> items;
    private int maxDepth = 100; // Maximum depth
    private SpotOrderDirection direction;
    private String symbol;

    public TradePlate() {
        // Default constructor
    }

    public TradePlate(String symbol, SpotOrderDirection direction) {
        this.direction = direction;
        this.symbol = symbol;
        this.items = Collections.synchronizedList(new LinkedList<>());
    }

    /**
     * Remove order from trade plate - copy từ Exchange.TradePlate.remove()
     */
    public void remove(Order order, BigDecimal amount) {
        synchronized (items) {
            if (order == null || order.getPrice() == null || amount == null) {
                return;
            }

            BigDecimal orderPrice = order.getPrice().getAmount();

            for (int index = 0; index < items.size(); index++) {
                TradePlateItem item = items.get(index);
                if (item.getPrice().compareTo(orderPrice) == 0) {
                    item.setAmount(item.getAmount().subtract(amount));
                    if (item.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                        items.remove(index);
                    }
                    return;
                }
            }
        }
    }

    /**
     * Remove order completely
     */
    public void remove(Order order) {
        BigDecimal remainingAmount = order.getSize().getAmount().subtract(getTradedAmount());
        remove(order, remainingAmount);
    }

    // ===== METHODS FOR EXCHANGEORDER - DIRECT USAGE =====

    /**
     * Add ExchangeOrder to trade plate - copy từ Exchange.TradePlate.add()
     */
    @SuppressWarnings("java:S3776")
    public boolean add(ExchangeOrder exchangeOrder) {
        synchronized (items) {
            if (exchangeOrder == null || exchangeOrder.getPrice() == null || exchangeOrder.getAmount() == null) {
                return false;
            }
            if (exchangeOrder.getType() == SpotOrderType.MARKET_PRICE) {
                logger.warn(" TradePlate.add() FAILED - market order: orderId={}", exchangeOrder.getOrderId());
                return false;
            }
            if (exchangeOrder.getDirection() != direction) {
                logger.warn(" TradePlate.add() FAILED - direction mismatch: orderDirection={}, plateDirection={}, orderId={}",
                        exchangeOrder.getDirection(), direction, exchangeOrder.getOrderId());
                return false;
            }

            BigDecimal orderPrice = exchangeOrder.getPrice();
            BigDecimal tradedAmount = exchangeOrder.getTradedAmount() != null ? exchangeOrder.getTradedAmount() : BigDecimal.ZERO;
            BigDecimal deltaAmount = exchangeOrder.getAmount().subtract(tradedAmount);

            logger.info(" TradePlate.add() AMOUNTS - orderId: {}, price: {}, amount: {}, tradedAmount: {}, deltaAmount: {}",
                    exchangeOrder.getOrderId(), orderPrice, exchangeOrder.getAmount(), tradedAmount, deltaAmount);

            // Validate remaining amount
            if (deltaAmount.compareTo(BigDecimal.ZERO) <= 0) {
                logger.warn(" TradePlate.add() FAILED - invalid deltaAmount: orderId={}, deltaAmount={}", exchangeOrder.getOrderId(), deltaAmount);
                return false;
            }

            int index = 0;
            for (; index < items.size(); index++) {
                TradePlateItem item = items.get(index);
                int priceCompare = item.getPrice().compareTo(orderPrice);

                if (priceCompare == 0) {
                    // Same price level: update or aggregate
                    if (exchangeOrder.getOrderId().equals(item.getOrderId())) {
                        item.setAmount(deltaAmount);
                        logger.info("Updated existing order {} amount to {} (remaining)", exchangeOrder.getOrderId(), deltaAmount);
                    } else {
                        item.setAmount(item.getAmount().add(deltaAmount));
                        // Keep existing (oldest) orderId; only set if missing
                        if (item.getOrderId() == null || item.getOrderId().isEmpty()) {
                            item.setOrderId(exchangeOrder.getOrderId());
                        }
                        logger.info("Aggregated amount at price {} to {}", orderPrice, item.getAmount());
                    }
                    return true;
                }

                // Both sides sorted by price DESC: insert before first smaller price
                if (item.getPrice().compareTo(orderPrice) < 0) {
                    break;
                }
            }

            // Insert new price level if within max depth
            if (index < maxDepth) {
                TradePlateItem newItem = new TradePlateItem();
                newItem.setAmount(deltaAmount);
                newItem.setPrice(orderPrice);
                newItem.setOrderId(exchangeOrder.getOrderId());
                items.add(index, newItem);
                logger.info(" TradePlate.add() SUCCESS - added new item: orderId={}, price={}, amount={}, index={}, totalItems={}",
                        exchangeOrder.getOrderId(), orderPrice, deltaAmount, index, items.size());
            } else {
                logger.warn(" TradePlate.add() SKIPPED - max depth reached: orderId={}, index={}, maxDepth={}, currentItems={}",
                        exchangeOrder.getOrderId(), index, maxDepth, items.size());
            }
        }
        return true;
    }


    /**
     * Remove ExchangeOrder from trade plate with specific amount
     */
    public void remove(ExchangeOrder exchangeOrder, BigDecimal amount) {
        synchronized (items) {
            if (exchangeOrder == null || exchangeOrder.getPrice() == null || amount == null) {
                return;
            }

            BigDecimal orderPrice = exchangeOrder.getPrice();

            for (int index = 0; index < items.size(); index++) {
                TradePlateItem item = items.get(index);
                if (item.getPrice().compareTo(orderPrice) == 0) {
                    item.setAmount(item.getAmount().subtract(amount));
                    if (item.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                        items.remove(index);
                    }
                    return;
                }
            }
        }
    }

    /**
     * Remove ExchangeOrder completely from trade plate
     */
    public void remove(ExchangeOrder exchangeOrder) {
        //   CRITICAL FIX: Handle null tradedAmount to prevent NullPointerException
        BigDecimal tradedAmount = exchangeOrder.getTradedAmount() != null ?
                exchangeOrder.getTradedAmount() : BigDecimal.ZERO;
        BigDecimal remainingAmount = exchangeOrder.getAmount().subtract(tradedAmount);
        remove(exchangeOrder, remainingAmount);
    }

    /**
     * Đồng bộ lại amount cho TradePlateItem tại price level cụ thể - đồng bộ với exchange-core
     * Method này được gọi để đảm bảo amount trong TradePlate khớp với tổng remaining amount trong MergeOrder
     */
    public void syncAmount(BigDecimal price, BigDecimal correctAmount, String orderId) {
        synchronized (items) {
            for (TradePlateItem item : items) {
                if (item.getPrice().compareTo(price) == 0) {
                    item.setAmount(correctAmount);
                    if (orderId != null) {
                        item.setOrderId(orderId);
                    }
                    logger.info("Synced TradePlateItem amount to {} for price {}", correctAmount, price);

                    // Remove item if amount is zero or negative
                    if (correctAmount.compareTo(BigDecimal.ZERO) <= 0) {
                        items.remove(item);
                        logger.info("Removed TradePlateItem for price {} due to zero amount", price);
                    }
                    return;
                }
            }
        }
    }

    /**
     * Cập nhật orderId cho TradePlateItem tại price level cụ thể - đồng bộ với exchange-core
     * Method này được gọi từ CoinTrader sau khi cancel order
     */
    public void updateOrderId(BigDecimal price, String newOrderId) {
        synchronized (items) {
            for (TradePlateItem item : items) {
                if (item.getPrice().compareTo(price) == 0) {
                    item.setOrderId(newOrderId);
                    logger.info("Updated TradePlateItem orderId to {} for price {}", newOrderId, price);
                    return;
                }
            }
        }
    }

    /**
     * Get highest price - copy từ Exchange logic
     */
    public BigDecimal getHighestPrice() {
        if (items == null || items.isEmpty()) {
            return BigDecimal.ZERO;
        }

        if (direction == SpotOrderDirection.BUY) {
            return items.get(0).getPrice();
        } else {
            return items.get(items.size() - 1).getPrice();
        }
    }

    /**
     * Get lowest price - copy từ Exchange logic
     */
    public BigDecimal getLowestPrice() {
        if (items == null || items.isEmpty()) {
            return BigDecimal.ZERO;
        }

        if (direction == SpotOrderDirection.BUY) {
            return items.get(items.size() - 1).getPrice();
        } else {
            return items.get(0).getPrice();
        }
    }

    /**
     * Get maximum amount at any price level
     */
    public BigDecimal getMaxAmount() {
        if (items == null || items.isEmpty()) {
            return BigDecimal.ZERO;
        }

        return items.stream()
                .map(TradePlateItem::getAmount)
                .max(BigDecimal::compareTo)
                .orElse(BigDecimal.ZERO);
    }

    /**
     * Get minimum amount at any price level
     */
    public BigDecimal getMinAmount() {
        if (items == null || items.isEmpty()) {
            return BigDecimal.ZERO;
        }

        return items.stream()
                .map(TradePlateItem::getAmount)
                .min(BigDecimal::compareTo)
                .orElse(BigDecimal.ZERO);
    }

    /**
     * Get depth (number of price levels)
     */
    public int getDepth() {
        return items == null ? 0 : items.size();
    }

    /**
     * Convert to JSON with limit - copy từ Exchange.TradePlate.toJSON()
     * CRITICAL FIX: Thread-safe implementation with proper limit handling
     */
    @SuppressWarnings("java:S1874")
    public ObjectNode toJSON(int limit) {
        synchronized (items) {
            ObjectNode json = objectMapper.createObjectNode();
            json.put("direction", direction.toString());
            json.put("maxAmount", getMaxAmount().doubleValue());
            json.put("minAmount", getMinAmount().doubleValue());
            json.put("highestPrice", getHighestPrice().doubleValue());
            json.put("lowestPrice", getLowestPrice().doubleValue());
            json.put("symbol", getSymbol());

            //   CRITICAL FIX: Ensure limit is applied correctly and safely
            List<TradePlateItem> limitedItems;
            if (items.size() > limit && limit > 0) {
                limitedItems = new ArrayList<>(items.subList(0, limit));
                logger.info("TradePlate.toJSON(): Limited items from {} to {} for symbol: {}",
                        items.size(), limit, symbol);
            } else {
                limitedItems = new ArrayList<>(items);
                logger.info("TradePlate.toJSON(): Not limiting items. Total items: {} for symbol: {}",
                        items.size(), symbol);
            }

            json.set("items", objectMapper.valueToTree(limitedItems));
            logger.info("TradePlate.toJSON(): Generated JSON for symbol {}: {}", symbol, json);

            return json;
        }
    }

    /**
     * Convert to JSON string - copy từ Exchange.TradePlate.toJSONString()
     */
    public String toJSONString() {
        synchronized (items) {
            try {
                return objectMapper.writeValueAsString(this);
            } catch (JsonProcessingException e) {
                logger.error("Error serializing TradePlate to JSON", e);
                return "{}";
            }
        }
    }

    /**
     * Create copy of trade plate
     */
    public TradePlate copy() {
        TradePlate copy = new TradePlate(this.symbol, this.direction);
        copy.setMaxDepth(this.maxDepth);

        synchronized (items) {
            List<TradePlateItem> copiedItems = new LinkedList<>();
            for (TradePlateItem item : items) {
                TradePlateItem copiedItem = new TradePlateItem();
                copiedItem.setOrderId(item.getOrderId());
                copiedItem.setPrice(item.getPrice());
                copiedItem.setAmount(item.getAmount());
                copiedItems.add(copiedItem);
            }
            copy.setItems(copiedItems);
        }

        return copy;
    }

    /**
     * Clear all items
     */
    public void clear() {
        synchronized (items) {
            items.clear();
        }
    }

    /**
     * Check if trade plate is empty
     */
    public boolean isEmpty() {
        return items == null || items.isEmpty();
    }

    /**
     * Get total volume across all price levels
     */
    public BigDecimal getTotalVolume() {
        if (items == null || items.isEmpty()) {
            return BigDecimal.ZERO;
        }

        return items.stream()
                .map(TradePlateItem::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * Get traded amount for order (placeholder)
     */
    private BigDecimal getTradedAmount() {
        // In production, this would get actual traded amount
        return BigDecimal.ZERO;
    }

    /**
     * Clean up all completed orders from TradePlate - đồng bộ với exchange-core.TradePlate
     * This method should be called periodically to ensure TradePlate consistency
     */
    public void cleanupCompletedOrders() {
        synchronized (items) {
            int removedCount = 0;
            java.util.Iterator<TradePlateItem> iterator = items.iterator();

            while (iterator.hasNext()) {
                TradePlateItem item = iterator.next();

                // Remove items that don't have a valid orderId or have zero amount
                if (item.getOrderId() == null || item.getOrderId().isEmpty() ||
                        item.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                    iterator.remove();
                    removedCount++;
                    logger.info("Removed invalid TradePlateItem: orderId={}, amount={}",
                            item.getOrderId(), item.getAmount());
                }
            }

            if (removedCount > 0) {
                logger.info("Cleaned up {} invalid items from TradePlate for symbol {}",
                        removedCount, symbol);
            }
        }
    }

    @Override
    public String toString() {
        return String.format("TradePlate[symbol=%s, direction=%s, depth=%d, volume=%s]",
                symbol, direction, getDepth(), getTotalVolume());
    }
}
