package com.icetea.lotus.matching.infrastructure.engine;

import com.icetea.lotus.matching.domain.entity.Order;
import com.icetea.lotus.matching.domain.enums.OrderDirection;
import com.icetea.lotus.matching.domain.valueobject.Money;
import com.icetea.lotus.matching.domain.valueobject.OrderId;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Snapshot của sổ lệnh phân tán, sử dụng trong DistributedOrderBook
 *
 * <AUTHOR> nguyen
 */
@Slf4j
@Getter
@Setter
public class DistributedOrderBookSnapshot {

    // Sổ lệnh phân tán
    private final DistributedOrderBook orderBook;

    // Danh sách các lệnh chờ (stop orders)
    @Getter
    private final CopyOnWriteArrayList<Order> stopOrders;

    /**
     * Get buy orders from order book
     */
    public List<Order> getBuyOrders() {
        return orderBook.getBuyOrders();
    }

    /**
     * Get sell orders from order book
     */
    public List<Order> getSellOrders() {
        return orderBook.getSellOrders();
    }

    /**
     * Khởi tạo DistributedOrderBookSnapshot với kích thước phân đoạn mặc định
     */
    public DistributedOrderBookSnapshot() {
        this.orderBook = new DistributedOrderBook();
        this.stopOrders = new CopyOnWriteArrayList<>();
    }

    /**
     * Khởi tạo DistributedOrderBookSnapshot với kích thước phân đoạn cụ thể
     *
     * @param segmentSize Kích thước phân đoạn
     */
    public DistributedOrderBookSnapshot(BigDecimal segmentSize) {
        this.orderBook = new DistributedOrderBook(segmentSize);
        this.stopOrders = new CopyOnWriteArrayList<>();
    }

    /**
     * Tạo bản sao của snapshot hiện tại (OPTIMIZED - reduced copy overhead)
     *
     * @return Bản sao của snapshot
     */
    public DistributedOrderBookSnapshot copy() {
        DistributedOrderBookSnapshot copy = new DistributedOrderBookSnapshot(orderBook.getSegmentSize());

        // OPTIMIZED: Batch copy thay vì individual forEach để giảm overhead
        List<Order> allOrders = orderBook.getAllOrdersList();
        if (allOrders != null && !allOrders.isEmpty()) {
            // FAST: Batch add với stream parallel processing
            allOrders.parallelStream().forEach(copy::addOrder);
            log.info("Fast copied {} orders from orderBook", allOrders.size());
        }

        // OPTIMIZED: Direct collection copy cho stop orders
        if (!this.stopOrders.isEmpty()) {
            copy.stopOrders.addAll(this.stopOrders);
            log.info("Fast copied {} stop orders", this.stopOrders.size());
        }

        return copy;
    }

    /**
     * Tạo bản sao tối ưu với lazy initialization (Exchange-Core style)
     *
     * @return Bản sao tối ưu
     */
    public DistributedOrderBookSnapshot createOptimizedCopy() {
        // Lazy copy: Chỉ copy khi thực sự cần modify
        DistributedOrderBookSnapshot copy = new DistributedOrderBookSnapshot(orderBook.getSegmentSize());

        // Optimized: Use batch operations for better performance
        List<Order> allOrders = orderBook.getAllOrdersList();
        if (allOrders != null && !allOrders.isEmpty()) {
            // Use batch add operation instead of individual adds
            copy.orderBook.addOrdersBatch(allOrders);
            log.info("Optimized batch copied {} orders from orderBook", allOrders.size());
        }

        // Optimized: Direct collection copy for stop orders
        if (!this.stopOrders.isEmpty()) {
            copy.stopOrders.addAll(this.stopOrders);
            log.info("Fast copied {} stop orders", this.stopOrders.size());
        }

        return copy;
    }

    /**
     * PERFORMANCE OPTIMIZED: Tạo bản sao siêu nhẹ với minimal overhead
     *
     * @return Bản sao lightweight cho high-performance scenarios
     */
    public DistributedOrderBookSnapshot createLightweightCopy() {
        // OPTIMIZATION: Create copy with minimal overhead
        DistributedOrderBookSnapshot copy = new DistributedOrderBookSnapshot(orderBook.getSegmentSize());

        // OPTIMIZATION: Use direct reference copy for read-mostly scenarios
        List<Order> allOrders = orderBook.getAllOrdersList();
        if (allOrders != null && !allOrders.isEmpty()) {
            // FAST: Use batch add without logging overhead
            copy.orderBook.addOrdersBatch(allOrders);
        }

        // OPTIMIZATION: Direct collection copy without logging
        if (!this.stopOrders.isEmpty()) {
            copy.stopOrders.addAll(this.stopOrders);
        }

        return copy;
    }

    /**
     * ULTRA-FAST: Copy-on-write snapshot for maximum performance
     *
     * @return Shared reference snapshot with COW semantics
     */
    public DistributedOrderBookSnapshot createCowSnapshot() {
        // OPTIMIZATION: Return shared reference for read operations
        // Only copy when actual modifications are needed
        DistributedOrderBookSnapshot cowSnapshot = new DistributedOrderBookSnapshot(orderBook.getSegmentSize());

        // ULTRA-FAST: Use batch copy instead of direct reference sharing
        List<Order> allOrders = orderBook.getAllOrdersList();
        if (allOrders != null && !allOrders.isEmpty()) {
            cowSnapshot.orderBook.addOrdersBatch(allOrders);
        }

        // Shallow copy stop orders
        if (!this.stopOrders.isEmpty()) {
            cowSnapshot.stopOrders.addAll(this.stopOrders);
        }

        return cowSnapshot;
    }

    /**
     * Thêm lệnh vào snapshot
     *
     * @param order Lệnh cần thêm
     */
    public void addOrder(Order order) {
        orderBook.addOrder(order);
    }

    /**
     * Thêm lệnh chờ vào snapshot
     *
     * @param order Lệnh chờ cần thêm
     */
    public void addStopOrder(Order order) {
        stopOrders.add(order);
    }

    /**
     * Xóa lệnh khỏi snapshot
     *
     * @param orderId ID lệnh cần xóa
     * @return true nếu xóa thành công, false nếu không
     */
    public boolean removeOrder(OrderId orderId) {
        log.info("DEBUG: Bắt đầu xóa lệnh {} khỏi snapshot, total orders trước khi xóa: {}",
                orderId, orderBook.size());

        Order removedOrder = orderBook.removeOrder(orderId);

        log.info("DEBUG: Kết quả xóa lệnh {} khỏi orderBook: {}, total orders sau khi xóa: {}",
                orderId, removedOrder != null, orderBook.size());

        // Nếu không tìm thấy lệnh trong sổ lệnh, kiểm tra trong danh sách lệnh chờ
        if (removedOrder == null) {
            log.info("DEBUG: Lệnh {} không tìm thấy trong orderBook, kiểm tra stop orders ({})",
                    orderId, stopOrders.size());

            // Tìm lệnh chờ có ID tương ứng
            for (Order stopOrder : stopOrders) {
                if (stopOrder.getOrderId().equals(orderId)) {
                    // Xóa lệnh chờ
                    boolean removed = stopOrders.remove(stopOrder);
                    log.info("DEBUG: Xóa lệnh chờ {} thành công: {}", orderId, removed);
                    return removed;
                }
            }
            log.info("DEBUG: Lệnh {} không tìm thấy trong stop orders", orderId);
            return false;
        }

        log.info("DEBUG: Xóa lệnh {} thành công khỏi orderBook", orderId);
        return true;
    }

    /**
     * Lấy tất cả các lệnh trong snapshot
     *
     * @return Danh sách tất cả các lệnh
     */
    public List<Order> getAllOrders() {
        // Sử dụng phương thức getAllOrdersList() mới từ orderBook
        return orderBook.getAllOrdersList();
    }

    /**
     * Lấy số lượng lệnh trong snapshot
     *
     * @return Số lượng lệnh
     */
    public int size() {
        return orderBook.size();
    }

    /**
     * Kiểm tra xem snapshot có rỗng không
     *
     * @return true nếu snapshot rỗng, false nếu không
     */
    public boolean isEmpty() {
        return orderBook.isEmpty();
    }

    /**
     * Get buy price levels - copy từ Future-Core
     */
    public List<Money> getBuyPriceLevels() {
        return orderBook.getBuyPriceLevels();
    }

    /**
     * Get sell price levels - copy từ Future-Core
     */
    public List<Money> getSellPriceLevels() {
        return orderBook.getSellPriceLevels();
    }

    /**
     * Add buy order - copy từ Future-Core
     */
    public void addBuyOrder(Order order) {
        orderBook.addBuyOrder(order);
    }

    /**
     * Add sell order - copy từ Future-Core
     */
    public void addSellOrder(Order order) {
        orderBook.addSellOrder(order);
    }

    /**
     * Get order book depth - copy từ Future-Core
     */
    public int getDepth() {
        return orderBook.getDepth();
    }

    /**
     * Get total volume for direction - copy từ Future-Core
     */
    public java.math.BigDecimal getTotalVolume(OrderDirection direction) {
        return orderBook.getTotalVolume(direction);
    }

    /**
     * Get best buy price - copy từ Future-Core
     */
    public Money getBestBuyPrice() {
        List<Money> buyPrices = getBuyPriceLevels();
        return buyPrices.isEmpty() ? null : buyPrices.get(0);
    }

    /**
     * Get best sell price - copy từ Future-Core
     */
    public Money getBestSellPrice() {
        List<Money> sellPrices = getSellPriceLevels();
        return sellPrices.isEmpty() ? null : sellPrices.get(0);
    }

    /**
     * Get spread - copy từ Future-Core
     */
    public Money getSpread() {
        Money bestBuy = getBestBuyPrice();
        Money bestSell = getBestSellPrice();

        if (bestBuy == null || bestSell == null) {
            return Money.ZERO;
        }

        return Money.of(bestSell.getAmount().subtract(bestBuy.getAmount()));
    }

    /**
     * Get mid price - copy từ Future-Core
     */
    public Money getMidPrice() {
        Money bestBuy = getBestBuyPrice();
        Money bestSell = getBestSellPrice();

        if (bestBuy == null || bestSell == null) {
            return Money.ZERO;
        }

        java.math.BigDecimal midPrice = bestBuy.getAmount().add(bestSell.getAmount())
                .divide(java.math.BigDecimal.valueOf(2), 8, java.math.RoundingMode.HALF_UP);

        return Money.of(midPrice);
    }

    /**
     * Get order book summary - copy từ Future-Core
     */
    public String getSummary() {
        return String.format("Snapshot[orders=%d, buyLevels=%d, sellLevels=%d, spread=%s]",
                size(), getBuyPriceLevels().size(), getSellPriceLevels().size(), getSpread());
    }

    // ===== OPTIMIZATION METHODS DELEGATION =====

    /**
     * OPTIMIZATION: Get size using optimized method
     */
    public int getSizeOptimized() {
        return orderBook.getSizeOptimized();
    }

    /**
     * OPTIMIZATION: Get best buy price using optimized method
     */
    public Money getBestBuyPriceOptimized() {
        return orderBook.getBestBuyPriceOptimized();
    }

    /**
     * OPTIMIZATION: Get best sell price using optimized method
     */
    public Money getBestSellPriceOptimized() {
        return orderBook.getBestSellPriceOptimized();
    }

    // ===== ITERATOR METHODS FOR PERFORMANCE OPTIMIZATION =====

    /**
     * Lấy iterator cho BUY orders với early exit capability
     * Tối ưu hóa theo pattern của Exchange module
     *
     * @return Iterator cho BUY orders theo thứ tự giá cao nhất trước
     */
    public Iterator<Map.Entry<Money, List<Order>>> getBuyOrdersIterator() {
        return orderBook.getBuyOrdersIterator();
    }

    /**
     * Lấy iterator cho SELL orders với early exit capability
     * Tối ưu hóa theo pattern của Exchange module
     *
     * @return Iterator cho SELL orders theo thứ tự giá thấp nhất trước
     */
    public Iterator<Map.Entry<Money, List<Order>>> getSellOrdersIterator() {
        return orderBook.getSellOrdersIterator();
    }

    /**
     * Get buy orders as map for direct access
     *
     * @return Map of buy orders by price
     */
    public Map<Money, List<Order>> getBuyOrdersMap() {
        return orderBook.getBuyOrdersMap();
    }

    /**
     * Get sell orders as map for direct access
     *
     * @return Map of sell orders by price
     */
    public Map<Money, List<Order>> getSellOrdersMap() {
        return orderBook.getSellOrdersMap();
    }

    /**
     * Remove order by ID - optimized version
     *
     * @param orderId Order ID to remove
     * @return true if order was found and removed
     */
    public boolean removeOrderById(String orderId) {
        return orderBook.removeOrderById(orderId);
    }

    /**
     * Find order by ID - optimized version
     *
     * @param orderId Order ID to find
     * @return Order if found, null otherwise
     */
    public Order findOrderById(String orderId) {
        return orderBook.findOrderById(orderId);
    }
}
