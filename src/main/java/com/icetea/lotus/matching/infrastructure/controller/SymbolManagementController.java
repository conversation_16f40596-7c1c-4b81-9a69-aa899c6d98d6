package com.icetea.lotus.matching.infrastructure.controller;

import com.icetea.lotus.matching.infrastructure.integration.MatchingEngineIntegrationService;
import com.icetea.lotus.matching.infrastructure.sharding.SymbolShardingManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.SYMBOL;

/**
 * REST Controller để quản lý symbols trong matching engine
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/symbols")
@RequiredArgsConstructor
public class SymbolManagementController {

    public static final String SUCCESS = "success";
    public static final String MESSAGE = "message";
    public static final String SYMBOL1 = "Symbol ";
    private final SymbolShardingManager shardingManager;
    private final MatchingEngineIntegrationService integrationService;

    /**
     * Thêm symbol mới vào pod hiện tại
     */
    @PostMapping("/add")
    public ResponseEntity<Map<String, Object>> addSymbol(@RequestParam String symbol) {
        try {
            log.info("Adding symbol {} to current pod", symbol);

            // Claim symbol cho pod hiện tại
            boolean claimed = shardingManager.claimSymbol(symbol);

            if (claimed) {
                // Khởi tạo matching engine cho symbol
                integrationService.initializeMatchingEngine(symbol);

                log.info("Successfully added and initialized symbol: {}", symbol);
                return ResponseEntity.ok(Map.of(
                        SUCCESS, true,
                        MESSAGE, SYMBOL1 + symbol + " added successfully",
                        SYMBOL, symbol
                ));
            } else {
                log.warn("Failed to claim symbol: {}", symbol);
                return ResponseEntity.badRequest().body(Map.of(
                        SUCCESS, false,
                        MESSAGE, "Failed to claim symbol " + symbol + " (may be owned by another pod)",
                        SYMBOL, symbol
                ));
            }

        } catch (Exception e) {
            log.error("Error adding symbol: {}", symbol, e);
            return ResponseEntity.internalServerError().body(Map.of(
                    SUCCESS, false,
                    MESSAGE, "Error adding symbol: " + e.getMessage(),
                    SYMBOL, symbol
            ));
        }
    }

    /**
     * Force thêm symbol vào pod hiện tại (override existing assignment)
     */
    @PostMapping("/force-add")
    public ResponseEntity<Map<String, Object>> forceAddSymbol(@RequestParam String symbol) {
        try {
            log.info("Force adding symbol {} to current pod", symbol);

            // Force claim symbol cho pod hiện tại
            boolean claimed = shardingManager.forceClaimSymbol(symbol);

            if (claimed) {
                // Khởi tạo matching engine cho symbol
                integrationService.initializeMatchingEngine(symbol);

                log.info("Successfully force added and initialized symbol: {}", symbol);
                return ResponseEntity.ok(Map.of(
                        SUCCESS, true,
                        MESSAGE, SYMBOL1 + symbol + " force added successfully",
                        SYMBOL, symbol
                ));
            } else {
                log.error("Failed to force claim symbol: {}", symbol);
                return ResponseEntity.internalServerError().body(Map.of(
                        SUCCESS, false,
                        MESSAGE, "Failed to force claim symbol " + symbol,
                        SYMBOL, symbol
                ));
            }

        } catch (Exception e) {
            log.error("Error force adding symbol: {}", symbol, e);
            return ResponseEntity.internalServerError().body(Map.of(
                    SUCCESS, false,
                    MESSAGE, "Error force adding symbol: " + e.getMessage(),
                    SYMBOL, symbol
            ));
        }
    }

    /**
     * Thêm nhiều symbols cùng lúc
     */
    @PostMapping("/batch-add")
    public ResponseEntity<Map<String, Object>> batchAddSymbols(@RequestBody List<String> symbols) {
        try {
            log.info("Batch adding symbols: {}", symbols);

            int successCount = 0;
            int failCount = 0;

            for (String symbol : symbols) {
                boolean claimed = shardingManager.claimSymbol(symbol);
                if (claimed) {
                    integrationService.initializeMatchingEngine(symbol);
                    successCount++;
                    log.info("Successfully added symbol: {}", symbol);
                } else {
                    failCount++;
                    log.warn("Failed to claim symbol: {}", symbol);
                }
            }

            return ResponseEntity.ok(Map.of(
                    SUCCESS, true,
                    MESSAGE, String.format("Batch add completed: %d success, %d failed", successCount, failCount),
                    "successCount", successCount,
                    "failCount", failCount,
                    "totalSymbols", symbols.size()
            ));

        } catch (Exception e) {
            log.error("Error batch adding symbols", e);
            return ResponseEntity.internalServerError().body(Map.of(
                    SUCCESS, false,
                    MESSAGE, "Error batch adding symbols: " + e.getMessage()
            ));
        }
    }

    /**
     * Lấy danh sách symbols được sở hữu bởi pod hiện tại
     */
    @GetMapping("/owned")
    public ResponseEntity<Map<String, Object>> getOwnedSymbols() {
        try {
            List<String> ownedSymbols = shardingManager.getOwnedSymbols();

            return ResponseEntity.ok(Map.of(
                    SUCCESS, true,
                    "symbols", ownedSymbols,
                    "count", ownedSymbols.size()
            ));

        } catch (Exception e) {
            log.error("Error getting owned symbols", e);
            return ResponseEntity.internalServerError().body(Map.of(
                    SUCCESS, false,
                    MESSAGE, "Error getting owned symbols: " + e.getMessage()
            ));
        }
    }

    /**
     * Kiểm tra xem symbol có được sở hữu bởi pod hiện tại không
     */
    @GetMapping("/check/{symbol}")
    public ResponseEntity<Map<String, Object>> checkSymbolOwnership(@PathVariable String symbol) {
        try {
            boolean isOwned = shardingManager.isSymbolOwnedByThisPod(symbol);

            return ResponseEntity.ok(Map.of(
                    SUCCESS, true,
                    SYMBOL, symbol,
                    "isOwned", isOwned
            ));

        } catch (Exception e) {
            log.error("Error checking symbol ownership: {}", symbol, e);
            return ResponseEntity.internalServerError().body(Map.of(
                    SUCCESS, false,
                    MESSAGE, "Error checking symbol ownership: " + e.getMessage(),
                    SYMBOL, symbol
            ));
        }
    }

    /**
     * Release symbol khỏi pod hiện tại
     */
    @DeleteMapping("/release/{symbol}")
    public ResponseEntity<Map<String, Object>> releaseSymbol(@PathVariable String symbol) {
        try {
            boolean released = shardingManager.releaseSymbol(symbol);

            if (released) {
                return ResponseEntity.ok(Map.of(
                        SUCCESS, true,
                        MESSAGE, SYMBOL1 + symbol + " released successfully",
                        SYMBOL, symbol
                ));
            } else {
                return ResponseEntity.badRequest().body(Map.of(
                        SUCCESS, false,
                        MESSAGE, "Failed to release symbol " + symbol + " (not owned by this pod)",
                        SYMBOL, symbol
                ));
            }

        } catch (Exception e) {
            log.error("Error releasing symbol: {}", symbol, e);
            return ResponseEntity.internalServerError().body(Map.of(
                    SUCCESS, false,
                    MESSAGE, "Error releasing symbol: " + e.getMessage(),
                    SYMBOL, symbol
            ));
        }
    }
}
