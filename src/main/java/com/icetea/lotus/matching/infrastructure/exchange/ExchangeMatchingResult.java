package com.icetea.lotus.matching.infrastructure.exchange;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Exchange Matching Result - <PERSON><PERSON><PERSON> quả đầy đủ từ ExchangeMatchingEngine
 * <PERSON><PERSON> trades, completed orders, và trade plate như CoinTraderV2
 * 
 * <AUTHOR> nguyen
 */
@Data
@Builder
public class ExchangeMatchingResult {
    
    private boolean success;
    private String orderId;
    private String symbol;
    private List<ExchangeTrade> trades;
    private List<Object> completedOrders;
    private Object tradePlate;
    private String message;
    private String errorCode;
    private Long timestamp;
    
    /**
     * Create success result
     */
    public static ExchangeMatchingResult success(String orderId, String symbol, 
                                               List<ExchangeTrade> trades,
                                               List<Object> completedOrders,
                                               Object tradePlate) {
        return ExchangeMatchingResult.builder()
                .success(true)
                .orderId(orderId)
                .symbol(symbol)
                .trades(trades)
                .completedOrders(completedOrders)
                .tradePlate(tradePlate)
                .message("Order processed successfully")
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * Create error result
     */
    public static ExchangeMatchingResult error(String message) {
        return ExchangeMatchingResult.builder()
                .success(false)
                .message(message)
                .errorCode("MATCHING_ERROR")
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * Get trades count
     */
    public int getTradesCount() {
        return trades != null ? trades.size() : 0;
    }
    
    /**
     * Check if has completed orders
     */
    public boolean hasCompletedOrders() {
        return completedOrders != null && !completedOrders.isEmpty();
    }
    
    /**
     * Check if has trade plate
     */
    public boolean hasTradePlate() {
        return tradePlate != null;
    }
}
