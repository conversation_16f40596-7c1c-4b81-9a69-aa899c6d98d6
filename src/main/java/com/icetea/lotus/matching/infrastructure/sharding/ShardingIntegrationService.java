package com.icetea.lotus.matching.infrastructure.sharding;

import com.icetea.lotus.matching.infrastructure.constants.CommandTypeConstance;
import com.icetea.lotus.matching.infrastructure.sharding.model.SymbolMetrics;
import com.icetea.lotus.matching.infrastructure.futurecore.FutureCoreCompatibilityService;
import com.icetea.lotus.matching.infrastructure.futurecore.FutureCoreTradeResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * Integration Service để kết nối tất cả sharding components
 * Cung cấp unified interface cho order processing với intelligent routing
 * Migrated từ future-core
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShardingIntegrationService {

    private final IntelligentOrderRouter orderRouter;
    private final SmartShardingManager smartShardingManager;
    private final PartitionBasedLoadBalancer loadBalancer;
    private final PodLoadMonitor podLoadMonitor;
    private final FutureCoreCompatibilityService futureCoreService;

    /**
     * Process order với intelligent routing
     */
    public CompletableFuture<OrderProcessingResult> processOrderWithSharding(Object order, String symbol) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.info("Processing order with intelligent sharding for symbol: {}", symbol);
                
                // Route order đến pod/partition phù hợp
                IntelligentOrderRouter.OrderRoutingResult routingResult = orderRouter.routeOrder(order, symbol);
                
                if (!routingResult.isSuccess()) {
                    return OrderProcessingResult.failed(routingResult.getMessage());
                }
                
                // Nếu order được route đến pod khác, return routing result
                if (!"DIRECT".equals(routingResult.getStrategy()) && 
                    !"DIRECT_ASSIGNED".equals(routingResult.getStrategy()) &&
                    !"PARTITION".equals(routingResult.getStrategy()) &&
                    !"LOAD_BALANCED_LOCAL".equals(routingResult.getStrategy()) &&
                    !"SEQUENTIAL".equals(routingResult.getStrategy())) {
                    
                    return OrderProcessingResult.routed(routingResult.getTargetPod(), routingResult.getStrategy());
                }
                
                // Process order locally
                return processOrderLocally(order, symbol, routingResult.getStrategy());
                
            } catch (Exception e) {
                log.error("Error processing order with sharding for symbol: {}", symbol, e);
                return OrderProcessingResult.failed("Processing error: " + e.getMessage());
            }
        });
    }
    
    /**
     * Process order locally
     */
    private OrderProcessingResult processOrderLocally(Object order, String symbol, String strategy) {
        try {
            // Record processing start time
            long startTime = System.currentTimeMillis();

            // ACTUAL INTEGRATION: Process through FutureCoreCompatibilityService
            FutureCoreTradeResult result = futureCoreService.processContractOrderInternal(order, CommandTypeConstance.PLACE_ORDER);

            // Record processing metrics
            long processingTime = System.currentTimeMillis() - startTime;
            podLoadMonitor.recordOrderProcessed(processingTime);

            if (result.isSuccess()) {
                log.info("Successfully processed order locally for symbol: {} using strategy: {}, trades: {}",
                        symbol, strategy, result.getTradesCount());

                return OrderProcessingResult.success(strategy, smartShardingManager.getCurrentPodName(),
                        result.getTradesCount(), processingTime);
            } else {
                log.warn("Failed to process order locally for symbol: {} using strategy: {} - {}",
                        symbol, strategy, result.getErrorMessage());

                return OrderProcessingResult.failed("Local processing failed: " + result.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("Error processing order locally for symbol: {}", symbol, e);
            return OrderProcessingResult.failed("Local processing error: " + e.getMessage());
        }
    }
    
    /**
     * Kiểm tra xem symbol có thể được xử lý bởi pod này không
     */
    public boolean canProcessSymbol(String symbol) {
        return smartShardingManager.canProcessSymbol(symbol);
    }
    
    /**
     * Assign symbol cho pod hiện tại
     */
    public boolean assignSymbolToCurrentPod(String symbol) {
        return smartShardingManager.assignSymbolToCurrentPod(symbol);
    }
    
    /**
     * Lấy thông tin cluster health
     */
    public ClusterHealthInfo getClusterHealth() {
        Set<String> activePods = loadBalancer.getActivePods();
        PartitionBasedLoadBalancer.LoadBalancingStats stats = loadBalancer.getLoadBalancingStats();
        
        return ClusterHealthInfo.builder()
                .activePods(activePods.size())
                .totalPods(stats.getTotalPods())
                .avgLoad(stats.getAvgLoad())
                .maxLoad(stats.getMaxLoad())
                .minLoad(stats.getMinLoad())
                .healthy(stats.getMaxLoad() < 0.9 && !activePods.isEmpty())
                .lastUpdated(LocalDateTime.now())
                .build();
    }
    
    /**
     * Trigger smart rebalancing
     */
    public void triggerSmartRebalancing() {
        log.info("Triggering smart rebalancing...");
        smartShardingManager.performSmartRebalancing();
    }
    
    /**
     * Auto rebalancing task
     */
    @Scheduled(fixedRate = 300000) // Every 5 minutes
    public void autoRebalancing() {
        try {
            ClusterHealthInfo health = getClusterHealth();
            
            // Trigger rebalancing if load imbalance detected
            if (health.getMaxLoad() - health.getMinLoad() > 0.3) {
                log.info("Load imbalance detected (max: {}, min: {}), triggering rebalancing", 
                        health.getMaxLoad(), health.getMinLoad());
                triggerSmartRebalancing();
            }
            
        } catch (Exception e) {
            log.error("Error in auto rebalancing", e);
        }
    }
    
    /**
     * Create partition config for high-volume symbol
     */
    public void createPartitionConfigForSymbol(String symbol, BigDecimal volume24h, long orders24h) {
        try {
            SymbolMetrics metrics = SymbolMetrics.builder()
                    .symbol(symbol)
                    .totalVolume24h(volume24h)
                    .totalOrders24h(orders24h)
                    .ordersPerSecond(orders24h / 86400.0) // Convert to per second
                    .timestamp(LocalDateTime.now())
                    .build();
            
            metrics.calculateLoadScore();
            
            if (metrics.needsPartitioning()) {
                log.info("Creating partition config for high-volume symbol: {}", symbol);
                loadBalancer.createPartitionConfig(symbol, metrics);
            }
            
        } catch (Exception e) {
            log.error("Error creating partition config for symbol: {}", symbol, e);
        }
    }
    
    /**
     * Order processing result
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class OrderProcessingResult {
        private boolean success;
        private boolean routed;
        private String strategy;
        private String targetPod;
        private String message;
        private int tradesCount;
        private long processingTimeMs;

        // Additional fields for compatibility with OrderRoutingConsumer
        private java.util.List<Object> trades;
        private Object completedOrder;
        private Object tradePlate;
        
        public static OrderProcessingResult success(String strategy, String pod, int trades, long processingTime) {
            return OrderProcessingResult.builder()
                    .success(true)
                    .routed(false)
                    .strategy(strategy)
                    .targetPod(pod)
                    .message("Success")
                    .tradesCount(trades)
                    .processingTimeMs(processingTime)
                    .build();
        }
        
        public static OrderProcessingResult routed(String targetPod, String strategy) {
            return OrderProcessingResult.builder()
                    .success(true)
                    .routed(true)
                    .strategy(strategy)
                    .targetPod(targetPod)
                    .message("Routed to " + targetPod)
                    .tradesCount(0)
                    .processingTimeMs(0)
                    .build();
        }
        
        public static OrderProcessingResult failed(String message) {
            return OrderProcessingResult.builder()
                    .success(false)
                    .routed(false)
                    .message(message)
                    .tradesCount(0)
                    .processingTimeMs(0)
                    .build();
        }
    }
    
    /**
     * Cluster health info
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class ClusterHealthInfo {
        private int activePods;
        private int totalPods;
        private double avgLoad;
        private double maxLoad;
        private double minLoad;
        private boolean healthy;
        private LocalDateTime lastUpdated;
    }
}
