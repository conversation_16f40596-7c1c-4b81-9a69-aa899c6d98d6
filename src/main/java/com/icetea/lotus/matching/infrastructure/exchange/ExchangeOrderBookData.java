package com.icetea.lotus.matching.infrastructure.exchange;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Exchange Order Book Data - Dữ liệu order book theo Exchange format
 * 
 * <AUTHOR> nguyen
 */
@Data
@Builder
public class ExchangeOrderBookData {
    
    private String symbol;
    private List<OrderBookLevel> bids; // Buy orders (highest price first)
    private List<OrderBookLevel> asks; // Sell orders (lowest price first)
    private Long timestamp;
    
    /**
     * Order book level data
     */
    @Data
    @Builder
    public static class OrderBookLevel {
        private BigDecimal price;
        private BigDecimal quantity;
        private int orderCount;
    }
    
    /**
     * Create empty order book
     */
    public static ExchangeOrderBookData empty(String symbol) {
        return ExchangeOrderBookData.builder()
                .symbol(symbol)
                .bids(List.of())
                .asks(List.of())
                .timestamp(System.currentTimeMillis())
                .build();
    }
}
