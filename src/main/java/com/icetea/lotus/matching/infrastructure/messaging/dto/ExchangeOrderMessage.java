package com.icetea.lotus.matching.infrastructure.messaging.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.icetea.lotus.matching.domain.entity.ExchangeOrder;
import com.icetea.lotus.matching.domain.enums.SpotOrderDirection;
import com.icetea.lotus.matching.domain.enums.SpotOrderStatus;
import com.icetea.lotus.matching.domain.enums.SpotOrderType;
import com.icetea.lotus.matching.infrastructure.stp.SelfTradePreventionMode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExchangeOrderMessage implements Serializable {
    private String orderId;
    private Long memberId;
    private SpotOrderType type;
    private BigDecimal amount;
    private String symbol;
    private BigDecimal tradedAmount;
    private BigDecimal turnover;
    private String coinSymbol;
    private String baseSymbol;
    private SpotOrderStatus status;
    private SpotOrderDirection direction;
    private BigDecimal price;
    private Long time;
    private Long completedTime;
    private Long canceledTime;
    private String useDiscount;
    private String orderResource;
    private BigDecimal stopPrice;
    private SelfTradePreventionMode selfTradePreventionMode;
    private transient List<Object> detail;
    private Long triggerTime;
    private String triggered;
    
    public static ExchangeOrderMessage mapFromExchangeOrder(ExchangeOrder order) {
        return ExchangeOrderMessage.builder()
                .orderId(order.getOrderId())
                .memberId(order.getMemberId())
                .type(order.getType())
                .amount(order.getAmount())
                .symbol(order.getSymbol())
                .tradedAmount(order.getTradedAmount())
                .turnover(order.getTurnover())
                .coinSymbol(order.getCoinSymbol())
                .baseSymbol(order.getBaseSymbol())
                .status(order.getStatus())
                .direction(order.getDirection())
                .price(order.getPrice())
                .time(order.getTime())
                .completedTime(order.getCompletedTime())
                .canceledTime(order.getCanceledTime())
                .useDiscount(order.getUseDiscount())
                .orderResource(order.getOrderResource())
                .stopPrice(order.getStopPrice())
                .selfTradePreventionMode(order.getSelfTradePreventionMode())
                .detail(order.getDetail())
                .triggerTime(order.getTriggerTime())
                .triggered(order.getTriggered())
                .build();
    }


}
