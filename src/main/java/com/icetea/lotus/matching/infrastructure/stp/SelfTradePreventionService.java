package com.icetea.lotus.matching.infrastructure.stp;

/**
 * <AUTHOR> nguyen
 */

import com.icetea.lotus.matching.domain.entity.ExchangeOrder;
import com.icetea.lotus.matching.domain.entity.Order;
import com.icetea.lotus.matching.infrastructure.messaging.dto.ExchangeOrderMessage;
import com.icetea.lotus.matching.infrastructure.messaging.producer.ExchangeKafkaProducer;
import com.icetea.lotus.matching.infrastructure.messaging.producer.FutureCoreKafkaProducer;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicLong;

/**
 * Self Trade Prevention Service
 * Migrated from future-core
 */
@Slf4j
@Component
public class SelfTradePreventionService {

    // Performance counters
    private static final AtomicLong stpChecksCount = new AtomicLong(0);
    private static final AtomicLong selfTradesDetected = new AtomicLong(0);
    private static final AtomicLong selfTradesPrevented = new AtomicLong(0);

    private final ExchangeKafkaProducer exchangeKafkaProducer;
    private final FutureCoreKafkaProducer futureCoreKafkaProducer;

    @Autowired
    public SelfTradePreventionService(ExchangeKafkaProducer exchangeKafkaProducer, FutureCoreKafkaProducer futureCoreKafkaProducer) {
        this.exchangeKafkaProducer = exchangeKafkaProducer;
        this.futureCoreKafkaProducer = futureCoreKafkaProducer;
    }

    /**
     * Kiểm tra và xử lý self-trade prevention
     *
     * @param takerOrder Lệnh taker (lệnh mới)
     * @param makerOrder Lệnh maker (lệnh trong order book)
     * @param stpMode    STP mode được áp dụng
     * @return Kết quả xử lý STP
     */
    public SelfTradePreventionResult checkAndPreventSelfTrade(
            Object takerOrder,
            Object makerOrder,
            SelfTradePreventionMode stpMode) {

        // Performance monitoring
        stpChecksCount.incrementAndGet();
        // Fast path for non-self-trades (most common case)
        if (!isSelfTrade(takerOrder, makerOrder)) {
            return SelfTradePreventionResult.noSelfTrade(takerOrder);
        }

        // Self-trade detected
        selfTradesDetected.incrementAndGet();
        // Apply STP mode
        SelfTradePreventionResult result = applySelfTradePreventionMode(takerOrder, makerOrder, stpMode);

        if (result.isTradeAllowed()) {
            log.info("Self-trade allowed by mode {}", stpMode);
        } else {
            selfTradesPrevented.incrementAndGet();
            log.info("Self-trade by mode {}", stpMode);
            processCancelSTP(takerOrder, makerOrder, result);

        }

        return result;
    }

    private void processCancelSTP(Object takerOrder, Object makerOrder, SelfTradePreventionResult result) {
        try {
            if (takerOrder instanceof ExchangeOrder taker && makerOrder instanceof ExchangeOrder maker) {
                if (result.isCancelMaker()) {
                    exchangeKafkaProducer.publishExchangeOrderCancel(taker.getSymbol(), ExchangeOrderMessage.mapFromExchangeOrder(maker));
                }
                if (result.isCancelTaker()) {
                    exchangeKafkaProducer.publishExchangeOrderCancel(taker.getSymbol(), ExchangeOrderMessage.mapFromExchangeOrder(maker));
                }
            }
            if (takerOrder instanceof Order taker && makerOrder instanceof Order maker) {
                if (result.isCancelMaker()) {
                    maker.setIsSTP(true);
                    futureCoreKafkaProducer.sendCancelOrderCommand(maker);
                } else if (result.isCancelTaker()) {
                    taker.setIsSTP(true);
                    futureCoreKafkaProducer.sendCancelOrderCommand(taker);
                }
            }
        } catch (Exception e) {
            log.error("Error publishing exchange order cancel: {}", e.getMessage());
        }
    }

    /**
     * Kiểm tra có phải self-trade không
     *
     * @param takerOrder Taker order
     * @param makerOrder Maker order
     * @return true nếu là self-trade
     */
    private boolean isSelfTrade(Object takerOrder, Object makerOrder) {
        if (takerOrder == null || makerOrder == null) {
            return false;
        }
        if (takerOrder instanceof Order orderTaker && makerOrder instanceof Order orderMaker) {
            return orderTaker.getMemberId().equals(orderMaker.getMemberId());
        }
        if (takerOrder instanceof ExchangeOrder exchangeOrder && makerOrder instanceof ExchangeOrder exchangeMaker) {
            return exchangeOrder.getMemberId().equals(exchangeMaker.getMemberId());
        }
        return false;
    }

    /**
     * Áp dụng STP mode
     *
     * @param takerOrder Taker order
     * @param makerOrder Maker order
     * @param stpMode    STP mode
     * @return STP result
     */
    private SelfTradePreventionResult applySelfTradePreventionMode(
            Object takerOrder, Object makerOrder, SelfTradePreventionMode stpMode) {

        return switch (stpMode) {
            case NONE ->
                // Cho phép self-trade
                    SelfTradePreventionResult.allowSelfTrade(takerOrder, makerOrder);
            case CANCEL_TAKER ->
                // Cancel taker order
                    SelfTradePreventionResult.cancelTaker(takerOrder, makerOrder);
            case CANCEL_MAKER ->
                // Cancel maker order
                    SelfTradePreventionResult.cancelMaker(takerOrder, makerOrder);
            case CANCEL_BOTH ->
                // Cancel both orders
                    SelfTradePreventionResult.cancelBoth(takerOrder, makerOrder);
            default -> {
                log.warn("Unknown STP mode: {}, defaulting to CANCEL_TAKER", stpMode);
                yield SelfTradePreventionResult.cancelTaker(takerOrder, makerOrder);
            }
        };
    }

    /**
     * Get performance statistics
     *
     * @return STP stats
     */
    public SelfTradePreventionStats getStats() {
        return SelfTradePreventionStats.builder()
                .totalChecks(stpChecksCount.get())
                .selfTradesDetected(selfTradesDetected.get())
                .selfTradesPrevented(selfTradesPrevented.get())
                .preventionRate(calculatePreventionRate())
                .build();
    }


    /**
     * Calculate prevention rate
     *
     * @return Prevention rate (0.0 to 1.0)
     */
    private double calculatePreventionRate() {
        long detected = selfTradesDetected.get();
        if (detected == 0) {
            return 0.0;
        }
        return (double) selfTradesPrevented.get() / detected;
    }

    /**
     * STP statistics
     */
    @Builder
    @Data
    public static class SelfTradePreventionStats {
        private long totalChecks;
        private long selfTradesDetected;
        private long selfTradesPrevented;
        private double preventionRate;
    }

    @Override
    public String toString() {
        return String.format("SelfTradePreventionService{checks=%d, detected=%d, prevented=%d, rate=%.2f%%}",
                stpChecksCount.get(), selfTradesDetected.get(), selfTradesPrevented.get(),
                calculatePreventionRate() * 100);
    }
}
