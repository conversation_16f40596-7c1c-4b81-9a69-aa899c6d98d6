package com.icetea.lotus.matching.infrastructure.sharding;

import com.icetea.lotus.matching.infrastructure.sharding.model.PodLoadInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Smart Sharding Manager với intelligent load balancing
 * Thay thế logic sharding cũ bằng cơ chế thông minh hơn
 * Migrated từ future-core
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SmartShardingManager {

    private final PartitionBasedLoadBalancer loadBalancer;
    private final PodLoadMonitor podLoadMonitor;
    private final RedissonClient redissonClient;

    @Value("${matching-engine.pod-name:${HOSTNAME:matching-engine-pod}}")
    private String currentPodName;

    // Constants
    private static final String SMART_SHARDING_LOCK = "matching-engine:smart-sharding-lock";
    private static final String PRIMARY_POD_MAP = "matching-engine:primary-pod-map";
    private static final double OVERLOAD_THRESHOLD = 0.8;
    private static final double UNDERLOAD_THRESHOLD = 0.3;
    
    /**
     * Kiểm tra xem symbol có được gán cho pod này không
     * Thay thế cho SymbolShardingManager.isSymbolOwnedByThisPod()
     */
    public boolean canProcessSymbol(String symbol) {
        try {
            // Kiểm tra partition assignments
            List<String> partitions = loadBalancer.getAllPartitions(symbol);
            for (String partition : partitions) {
                String assignedPod = loadBalancer.getPartitionOwner(partition);
                if (currentPodName.equals(assignedPod)) {
                    return true;
                }
            }
            
            // Kiểm tra primary assignment
            String primaryPod = getPrimaryPodForSymbol(symbol);
            if (currentPodName.equals(primaryPod)) {
                return true;
            }
            
            // Nếu chưa có assignment, có thể claim
            return canClaimSymbol();
            
        } catch (Exception e) {
            log.error("Error checking if can process symbol {}", symbol, e);
            return false;
        }
    }
    
    /**
     * Assign symbol cho pod hiện tại
     */
    public boolean assignSymbolToCurrentPod(String symbol) {
        RLock lock = redissonClient.getLock(SMART_SHARDING_LOCK + ":" + symbol);

        try {
            // Sử dụng quick lock cho assign symbol (100ms wait, 1000ms lease)
            if (lock.tryLock(100, 1000, TimeUnit.MILLISECONDS)) {
                try {
                    // Kiểm tra load của pod hiện tại
                    PodLoadInfo loadInfo = podLoadMonitor.getPodLoadInfo(currentPodName);
                    if (loadInfo != null && loadInfo.isOverloaded()) {
                        log.warn("Cannot assign symbol {} to overloaded pod {}", symbol, currentPodName);
                        return false;
                    }

                    // Assign primary pod
                    RMap<String, String> primaryPodMap = redissonClient.getMap(PRIMARY_POD_MAP);
                    primaryPodMap.put(symbol, currentPodName);

                    // Assign partitions
                    loadBalancer.assignPartitionsToPods(symbol);

                    log.info("Successfully assigned symbol {} to pod {}", symbol, currentPodName);
                    return true;

                } finally {
                    lock.unlock();
                }
            } else {
                log.info("Could not acquire lock for symbol assignment: {}", symbol);
                return false;
            }
        } catch (InterruptedException ie) {
            log.warn("Thread interrupted while assigning symbol {} to pod {}", symbol, currentPodName, ie);
            Thread.currentThread().interrupt(); // Phục hồi trạng thái interrupt
            return false;
        } catch (Exception e) {
            log.error("Error assigning symbol {} to current pod", symbol, e);
            return false;
        }
    }

    /**
     * Thực hiện smart rebalancing
     */
    public void performSmartRebalancing() {
        RLock lock = redissonClient.getLock(SMART_SHARDING_LOCK);

        try {
            // Sử dụng quick lock cho smart rebalancing (200ms wait, 2000ms lease)
            if (lock.tryLock(200, 2000, TimeUnit.MILLISECONDS)) {
                try {
                    log.info("Starting smart rebalancing...");
                    // Phân tích load của tất cả pods
                    Map<String, PodLoadInfo> podLoads = podLoadMonitor.getAllPodLoadInfo();

                    // Tìm pods overloaded và underloaded
                    List<String> overloadedPods = findOverloadedPods(podLoads);
                    List<String> underloadedPods = findUnderloadedPods(podLoads);

                    if (!overloadedPods.isEmpty() && !underloadedPods.isEmpty()) {
                        // Thực hiện rebalancing
                        performLoadRebalancing(overloadedPods, underloadedPods, podLoads);
                    }

                    log.info("Smart rebalancing completed");
                } finally {
                    lock.unlock();
                }
            }
        } catch (InterruptedException ie) {
            log.warn("Smart rebalancing interrupted", ie);
            Thread.currentThread().interrupt(); //   Khôi phục trạng thái interrupt
        } catch (Exception e) {
            log.error("Error performing smart rebalancing", e);
        }
    }

    /**
     * Tìm pods bị overloaded
     */
    private List<String> findOverloadedPods(Map<String, PodLoadInfo> podLoads) {
        return podLoads.entrySet().stream()
                .filter(entry -> entry.getValue().getOverallLoad() > OVERLOAD_THRESHOLD)
                .map(Map.Entry::getKey)
                .toList();
    }
    
    /**
     * Tìm pods bị underloaded
     */
    private List<String> findUnderloadedPods(Map<String, PodLoadInfo> podLoads) {
        return podLoads.entrySet().stream()
                .filter(entry -> entry.getValue().getOverallLoad() < UNDERLOAD_THRESHOLD)
                .map(Map.Entry::getKey)
                .toList();
    }
    
    /**
     * Thực hiện load rebalancing
     */
    @SuppressWarnings("java:S3776")
    private void performLoadRebalancing(List<String> overloadedPods, List<String> underloadedPods,
                                       Map<String, PodLoadInfo> podLoads) {
        for (String overloadedPod : overloadedPods) {
            if (underloadedPods.isEmpty()) break;
            
            // Tìm partitions có thể di chuyển từ overloaded pod
            List<String> movablePartitions = findMovablePartitions();
            
            for (String partition : movablePartitions) {
                if (underloadedPods.isEmpty()) break;
                
                // Chọn target pod tốt nhất
                String targetPod = selectBestTargetPod(underloadedPods, podLoads);
                
                if (targetPod != null) {
                    // Di chuyển partition
                    boolean moved = movePartition(partition, overloadedPod, targetPod);
                    
                    if (moved) {
                        log.info("Moved partition {} from {} to {}", partition, overloadedPod, targetPod);
                        
                        // Cập nhật load info
                        updateLoadAfterMove(targetPod, podLoads);
                        
                        // Nếu target pod đã đủ load, remove khỏi underloaded list
                        PodLoadInfo targetLoad = podLoads.get(targetPod);
                        if (targetLoad != null && targetLoad.getOverallLoad() > UNDERLOAD_THRESHOLD) {
                            underloadedPods.remove(targetPod);
                        }
                    }
                }
            }
        }
    }
    
    /**
     * Tìm partitions có thể di chuyển
     */
    private List<String> findMovablePartitions() {
        return List.of();
    }
    
    /**
     * Chọn target pod tốt nhất
     */
    private String selectBestTargetPod(List<String> underloadedPods, Map<String, PodLoadInfo> podLoads) {
        return underloadedPods.stream()
                .min((p1, p2) -> {
                    double load1 = podLoads.get(p1).getOverallLoad();
                    double load2 = podLoads.get(p2).getOverallLoad();
                    return Double.compare(load1, load2);
                })
                .orElse(null);
    }
    
    /**
     * Di chuyển partition
     */
    private boolean movePartition(String partition, String sourcePod, String targetPod) {
        try {
            log.info("Moving partition {} from {} to {}", partition, sourcePod, targetPod);
            return true;
        } catch (Exception e) {
            log.error("Error moving partition {} from {} to {}", partition, sourcePod, targetPod, e);
            return false;
        }
    }
    
    /**
     * Cập nhật load sau khi move
     */
    private void updateLoadAfterMove(String targetPod, Map<String, PodLoadInfo> podLoads) {
        PodLoadInfo loadInfo = podLoads.get(targetPod);
        if (loadInfo != null) {
            // Tăng load một chút sau khi nhận partition mới
            loadInfo.setOverallLoad(Math.min(1.0, loadInfo.getOverallLoad() + 0.1));
        }
    }
    
    /**
     * Lấy primary pod cho symbol
     */
    public String getPrimaryPodForSymbol(String symbol) {
        try {
            RMap<String, String> primaryPodMap = redissonClient.getMap(PRIMARY_POD_MAP);
            return primaryPodMap.get(symbol);
        } catch (Exception e) {
            log.error("Error getting primary pod for symbol {}", symbol, e);
            return null;
        }
    }
    
    /**
     * Kiểm tra xem có thể claim symbol không
     */
    private boolean canClaimSymbol() {
        PodLoadInfo loadInfo = podLoadMonitor.getPodLoadInfo(currentPodName);
        return loadInfo == null || !loadInfo.isOverloaded();
    }
    
    /**
     * Lấy tên pod hiện tại
     */
    public String getCurrentPodName() {
        return currentPodName;
    }
}
