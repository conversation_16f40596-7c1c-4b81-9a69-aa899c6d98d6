package com.icetea.lotus.matching.infrastructure.persistence.redis.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * Enhanced Redis Snapshot Cleanup Service
 * Handles aggressive cleanup of old snapshot versions
 * 
 * <AUTHOR> nguyen
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RedisSnapshotCleanupService {

    @Qualifier("snapshotRedisTemplate")
    private final RedisTemplate<String, Object> redisTemplate;

    // Configuration
    @Value("${matching-engine.redis.snapshot.cleanup.enabled:true}")
    private boolean cleanupEnabled;

    @Value("${matching-engine.redis.snapshot.max-versions-per-symbol:10}")
    private int maxVersionsPerSymbol;

    @Value("${matching-engine.redis.snapshot.ttl-hours:24}")
    private int snapshotTtlHours;

    @Value("${matching-engine.redis.snapshot.cleanup.grace-period-minutes:5}")
    private int gracePeriodMinutes;

    @Value("${matching-engine.redis.snapshot.cleanup.batch-size:100}")
    private int batchSize;

    @Value("${matching-engine.redis.snapshot.cleanup.aggressive-mode:true}")
    private boolean aggressiveMode;

    // Statistics
    private long totalCleaned = 0;
    private long lastCleanupTime = 0;

    /**
     * Scheduled cleanup task - runs every 10 minutes
     */
    @Scheduled(fixedRate = 600000) // 10 minutes
    public void performScheduledCleanup() {
        if (!cleanupEnabled) {
            log.info("Redis snapshot cleanup is disabled");
            return;
        }

        try {
            log.info("Starting scheduled Redis snapshot cleanup");
            long startTime = System.currentTimeMillis();
            
            // Get all snapshot patterns
            Set<String> allKeys = redisTemplate.keys("matching-engine:snapshot:*");
            if (allKeys.isEmpty()) {
                log.info("No snapshot keys found for cleanup");
                return;
            }

            log.info("Found {} total snapshot keys", allKeys.size());

            // Group keys by symbol and trading type
            Map<String, List<String>> symbolGroups = groupKeysBySymbol(allKeys);
            
            int totalSnapshotCleaned = 0;
            for (Map.Entry<String, List<String>> entry : symbolGroups.entrySet()) {
                String symbolKey = entry.getKey();

                String[] parts = symbolKey.split(":");
                if (parts.length >= 2) {
                    String symbol = parts[0];
                    String tradingType = parts[1];
                    
                    int cleaned = cleanupSymbolVersions(symbol, tradingType);
                    totalSnapshotCleaned += cleaned;
                }
            }

            long duration = System.currentTimeMillis() - startTime;
            this.totalCleaned += totalSnapshotCleaned;
            this.lastCleanupTime = System.currentTimeMillis();

            if (totalSnapshotCleaned > 0) {
                log.info("  Completed Redis snapshot cleanup: {} keys cleaned in {} ms",
                        totalSnapshotCleaned, duration);
                // Log statistics after cleanup
                logCleanupStatistics();
            } else {
                log.info("  Redis snapshot cleanup completed: no keys needed cleaning");
            }

        } catch (Exception e) {
            log.error("  Error during scheduled Redis snapshot cleanup", e);
        }
    }

    /**
     * Aggressive cleanup for specific symbol
     */
    @Async
    public void performAggressiveCleanup(String symbol, String tradingType) {
        if (!cleanupEnabled) {
            CompletableFuture.completedFuture(0);
            return;
        }

        try {
            log.info("Starting aggressive cleanup for {}:{}", symbol, tradingType);
            
            // Get all versions for this symbol
            List<Long> versions = getAvailableVersions(symbol, tradingType);
            if (versions.size() <= maxVersionsPerSymbol) {
                log.info("No cleanup needed for {}:{} - only {} versions",
                         symbol, tradingType, versions.size());
                CompletableFuture.completedFuture(0);
                return;
            }

            // Calculate versions to delete (keep only latest N)
            List<Long> versionsToDelete = versions.stream()
                .skip(maxVersionsPerSymbol)
                .toList();

            if (aggressiveMode) {
                // In aggressive mode, delete more aggressively
                versionsToDelete = versions.stream()
                    .skip(Math.max(1, maxVersionsPerSymbol / 2)) // Keep only half of max versions
                    .toList();
            }

            int deletedCount = 0;
            for (Long version : versionsToDelete) {
                if (deleteSnapshotVersion(symbol, tradingType, version)) {
                    deletedCount++;
                }
            }

            log.info("Aggressive cleanup completed for {}:{} - deleted {} versions",
                    symbol, tradingType, deletedCount);

            CompletableFuture.completedFuture(deletedCount);

        } catch (Exception e) {
            log.error("  Error during aggressive cleanup for {}:{}", symbol, tradingType, e);
            CompletableFuture.completedFuture(0);
        }
    }

    /**
     * Get basic cleanup statistics for logging
     */
    public void logCleanupStatistics() {
        try {
            Set<String> allKeys = redisTemplate.keys("matching-engine:snapshot:*:*:*");
            int currentSnapshotCount = allKeys.size();

            log.info("📊 Cleanup Stats - Total cleaned: {}, Current snapshots: {}, Last cleanup: {}",
                    totalCleaned, currentSnapshotCount,
                    lastCleanupTime > 0 ? new java.util.Date(lastCleanupTime) : "Never");
        } catch (Exception e) {
            log.warn("Failed to log cleanup statistics", e);
        }
    }

    // Helper methods

    private Map<String, List<String>> groupKeysBySymbol(Set<String> keys) {
        Map<String, List<String>> groups = new HashMap<>();
        
        for (String key : keys) {
            if (key.startsWith("matching-engine:snapshot:") && !key.contains(":latest:") && !key.contains(":versions:")) {
                String[] parts = key.split(":");
                if (parts.length >= 4) {
                    String symbolKey = parts[2] + ":" + parts[3]; // symbol:tradingType
                    groups.computeIfAbsent(symbolKey, k -> new ArrayList<>()).add(key);
                }
            }
        }
        
        return groups;
    }

    private int cleanupSymbolVersions(String symbol, String tradingType) {
        try {
            List<Long> versions = getAvailableVersions(symbol, tradingType);
            if (versions.size() <= maxVersionsPerSymbol) {
                return 0;
            }

            // Apply grace period
            Instant gracePeriodCutoff = Instant.now().minus(gracePeriodMinutes, ChronoUnit.MINUTES);
            
            List<Long> versionsToDelete = versions.stream()
                .skip(maxVersionsPerSymbol)
                .filter(version -> Instant.ofEpochMilli(version).isBefore(gracePeriodCutoff))
                .toList();

            int deletedCount = 0;
            for (Long version : versionsToDelete) {
                if (deleteSnapshotVersion(symbol, tradingType, version)) {
                    deletedCount++;
                }
            }

            if (deletedCount > 0) {
                log.info("Cleaned up {} versions for {}:{}", deletedCount, symbol, tradingType);
            }

            return deletedCount;

        } catch (Exception e) {
            log.error("Error cleaning up versions for {}:{}", symbol, tradingType, e);
            return 0;
        }
    }

    private List<Long> getAvailableVersions(String symbol, String tradingType) {
        try {
            String versionsKey = String.format("matching-engine:snapshot:versions:%s:%s", symbol, tradingType);
            Set<Object> versions = redisTemplate.opsForZSet().reverseRange(versionsKey, 0, -1);
            
            if (versions == null || versions.isEmpty()) {
                return Collections.emptyList();
            }
            
            return versions.stream()
                    .map(v -> Long.valueOf(v.toString()))
                    .toList();
                    
        } catch (Exception e) {
            log.error("Failed to get available versions for {}:{}", symbol, tradingType, e);
            return Collections.emptyList();
        }
    }

    private boolean deleteSnapshotVersion(String symbol, String tradingType, Long version) {
        try {
            String snapshotKey = String.format("matching-engine:snapshot:%s:%s:%s", symbol, tradingType, version);
            String versionsKey = String.format("matching-engine:snapshot:versions:%s:%s", symbol, tradingType);
            
            // Delete snapshot data
            redisTemplate.delete(snapshotKey);
            
            // Remove from versions set
            redisTemplate.opsForZSet().remove(versionsKey, version);
            
            return true;
            
        } catch (Exception e) {
            log.error("Error deleting snapshot version {} for {}:{}", version, symbol, tradingType, e);
            return false;
        }
    }
}
