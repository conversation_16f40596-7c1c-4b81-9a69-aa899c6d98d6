package com.icetea.lotus.matching.infrastructure.sharding.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Metrics cho symbol trading
 * Migrated từ future-core
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SymbolMetrics {
    
    private String symbol;
    private LocalDateTime timestamp;
    
    // Volume metrics
    private BigDecimal totalVolume24h;
    private BigDecimal avgOrderSize;
    private BigDecimal maxOrderSize;
    private BigDecimal minOrderSize;
    
    // Order metrics
    private long totalOrders24h;
    private long buyOrders24h;
    private long sellOrders24h;
    private double ordersPerSecond;
    
    // Price metrics
    private BigDecimal currentPrice;
    private BigDecimal highPrice24h;
    private BigDecimal lowPrice24h;
    private BigDecimal priceVolatility;
    
    // Performance metrics
    private double avgProcessingTime; // milliseconds
    private double maxProcessingTime;
    private double minProcessingTime;
    private long totalTrades24h;
    
    // Load metrics
    private double loadScore; // 0.0 to 1.0
    private int activePartitions;
    private String primaryPod;
    
    /**
     * Tính toán load score dựa trên metrics
     */
    public void calculateLoadScore() {
        double volumeScore = Math.min(1.0, totalVolume24h.doubleValue() / 10_000_000.0); // 10M threshold
        double orderScore = Math.min(1.0, totalOrders24h / 100_000.0); // 100K threshold
        double latencyScore = Math.min(1.0, avgProcessingTime / 100.0); // 100ms threshold
        
        this.loadScore = (volumeScore * 0.4) + (orderScore * 0.4) + (latencyScore * 0.2);
    }
    
    /**
     * Kiểm tra xem symbol có cần partition không
     */
    public boolean needsPartitioning() {
        return loadScore > 0.7 || ordersPerSecond > 500 || totalVolume24h.compareTo(new BigDecimal("5000000")) > 0;
    }
    
    /**
     * Kiểm tra xem symbol có phải high-volume không
     */
    public boolean isHighVolume() {
        return totalVolume24h.compareTo(new BigDecimal("1000000")) > 0;
    }
    
    /**
     * Kiểm tra xem symbol có phải high-frequency không
     */
    public boolean isHighFrequency() {
        return ordersPerSecond > 100;
    }
}
