package com.icetea.lotus.matching.infrastructure.sharding.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Configuration cho partition của symbol
 * Migrated từ future-core
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PartitionConfig {
    
    private String symbol;
    private int partitionCount;
    private PartitionStrategy strategy;
    private List<PartitionInfo> partitions;
    private LocalDateTime lastUpdated;
    private String updatedBy;
    
    // Thresholds cho partition splitting
    private BigDecimal volumeThreshold;
    private BigDecimal priceRangeThreshold;
    private int orderCountThreshold;
    
    // Load balancing settings
    private double loadBalanceThreshold;
    private boolean autoRebalanceEnabled;
    private int rebalanceIntervalMinutes;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PartitionInfo {
        private String partitionId;
        private String assignedPod;
        private PartitionRange range;
        private PartitionMetrics metrics;
        private boolean active;
        private LocalDateTime lastAssigned;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PartitionRange {
        private BigDecimal minPrice;
        private BigDecimal maxPrice;
        private BigDecimal minVolume;
        private BigDecimal maxVolume;
        private String hashRange; // For hash-based partitioning
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PartitionMetrics {
        private long orderCount;
        private BigDecimal totalVolume;
        private double avgLatency;
        private double loadScore;
        private LocalDateTime lastUpdated;
    }
}
