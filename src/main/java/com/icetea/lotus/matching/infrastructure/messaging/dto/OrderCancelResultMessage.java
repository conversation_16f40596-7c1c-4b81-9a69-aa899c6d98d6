package com.icetea.lotus.matching.infrastructure.messaging.dto;

import com.icetea.lotus.matching.domain.enums.OrderDirection;
import com.icetea.lotus.matching.domain.enums.OrderStatus;
import com.icetea.lotus.matching.domain.enums.OrderType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.Instant;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderCancelResultMessage {
    private String orderId;
    private BigDecimal price;
    private BigDecimal size;
    private BigDecimal filledSize;
    private OrderType type;
    private Long memberId;
    private String symbol;
    private OrderStatus status;
    private OrderDirection direction;
    private Instant timestamp;
}
