package com.icetea.lotus.matching.infrastructure.service;

/**
 * <AUTHOR> nguyen
 */

import com.icetea.lotus.matching.domain.valueobject.Money;
import com.icetea.lotus.matching.domain.valueobject.Symbol;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Price Manager - Quản lý giá cả trong matching engine
 * 
 * Chịu trách nhiệm:
 * 1. Quản lý mark price (giá đánh dấu)
 * 2. Quản lý index price (giá chỉ số)
 * 3. Quản lý last price (giá giao dịch cuối)
 * 4. Cung cấp giá cho stop order triggering
 */
@Slf4j
@Service
public class PriceManager {
    
    // Mark prices by symbol
    private final ConcurrentHashMap<Symbol, PriceData> markPrices = new ConcurrentHashMap<>();
    
    // Index prices by symbol
    private final ConcurrentHashMap<Symbol, PriceData> indexPrices = new ConcurrentHashMap<>();
    
    // Last trade prices by symbol
    private final ConcurrentHashMap<Symbol, PriceData> lastPrices = new ConcurrentHashMap<>();
    
    /**
     * Cập nhật mark price cho symbol
     * 
     * @param symbol Symbol cần cập nhật
     * @param markPrice Giá đánh dấu mới
     */
    public void updateMarkPrice(Symbol symbol, Money markPrice) {
        if (symbol == null || markPrice == null || !markPrice.isPositive()) {
            log.warn("Invalid mark price update: symbol={}, price={}", symbol, markPrice);
            return;
        }
        
        PriceData priceData = new PriceData(markPrice, Instant.now());
        markPrices.put(symbol, priceData);
        
        log.info("Updated mark price for {}: {}", symbol, markPrice);
    }
    
    /**
     * Cập nhật index price cho symbol
     * 
     * @param symbol Symbol cần cập nhật
     * @param indexPrice Giá chỉ số mới
     */
    public void updateIndexPrice(Symbol symbol, Money indexPrice) {
        if (symbol == null || indexPrice == null || !indexPrice.isPositive()) {
            log.warn("Invalid index price update: symbol={}, price={}", symbol, indexPrice);
            return;
        }
        
        PriceData priceData = new PriceData(indexPrice, Instant.now());
        indexPrices.put(symbol, priceData);
        
        log.info("Updated index price for {}: {}", symbol, indexPrice);
    }
    
    /**
     * Cập nhật last price cho symbol
     * 
     * @param symbol Symbol cần cập nhật
     * @param lastPrice Giá giao dịch cuối mới
     */
    public void updateLastPrice(Symbol symbol, Money lastPrice) {
        if (symbol == null || lastPrice == null || !lastPrice.isPositive()) {
            log.warn("Invalid last price update: symbol={}, price={}", symbol, lastPrice);
            return;
        }
        
        PriceData priceData = new PriceData(lastPrice, Instant.now());
        lastPrices.put(symbol, priceData);
        
        log.info("Updated last price for {}: {}", symbol, lastPrice);
    }
    
    /**
     * Lấy mark price cho symbol
     * 
     * @param symbol Symbol cần lấy giá
     * @return Mark price hoặc null nếu không có
     */
    public Money getMarkPrice(Symbol symbol) {
        PriceData priceData = markPrices.get(symbol);
        return priceData != null ? priceData.getPrice() : null;
    }
    
    /**
     * Lấy index price cho symbol
     * 
     * @param symbol Symbol cần lấy giá
     * @return Index price hoặc null nếu không có
     */
    public Money getIndexPrice(Symbol symbol) {
        PriceData priceData = indexPrices.get(symbol);
        return priceData != null ? priceData.getPrice() : null;
    }
    
    /**
     * Lấy last price cho symbol
     * 
     * @param symbol Symbol cần lấy giá
     * @return Last price hoặc null nếu không có
     */
    public Money getLastPrice(Symbol symbol) {
        PriceData priceData = lastPrices.get(symbol);
        return priceData != null ? priceData.getPrice() : null;
    }
    
    /**
     * Lấy giá hiện tại để trigger stop orders
     * Ưu tiên: mark price > last price > index price
     * 
     * @param symbol Symbol cần lấy giá
     * @return Giá hiện tại hoặc null nếu không có giá nào
     */
    public Money getCurrentPrice(Symbol symbol) {
        Money markPrice = getMarkPrice(symbol);
        if (markPrice != null) {
            return markPrice;
        }
        
        Money lastPrice = getLastPrice(symbol);
        if (lastPrice != null) {
            return lastPrice;
        }
        
        return getIndexPrice(symbol);
    }
    
    /**
     * Kiểm tra xem symbol có giá hay không
     * 
     * @param symbol Symbol cần kiểm tra
     * @return true nếu có ít nhất một loại giá
     */
    public boolean hasPrices(Symbol symbol) {
        return markPrices.containsKey(symbol) || 
               indexPrices.containsKey(symbol) || 
               lastPrices.containsKey(symbol);
    }
    
    /**
     * Xóa tất cả giá cho symbol
     * 
     * @param symbol Symbol cần xóa
     */
    public void clearPrices(Symbol symbol) {
        markPrices.remove(symbol);
        indexPrices.remove(symbol);
        lastPrices.remove(symbol);
        
        log.info("Cleared all prices for symbol: {}", symbol);
    }
    
    /**
     * Lấy thời gian cập nhật mark price cuối cùng
     * 
     * @param symbol Symbol cần kiểm tra
     * @return Thời gian cập nhật hoặc null
     */
    public Instant getMarkPriceTimestamp(Symbol symbol) {
        PriceData priceData = markPrices.get(symbol);
        return priceData != null ? priceData.getTimestamp() : null;
    }
    
    /**
     * Lấy thời gian cập nhật index price cuối cùng
     * 
     * @param symbol Symbol cần kiểm tra
     * @return Thời gian cập nhật hoặc null
     */
    public Instant getIndexPriceTimestamp(Symbol symbol) {
        PriceData priceData = indexPrices.get(symbol);
        return priceData != null ? priceData.getTimestamp() : null;
    }
    
    /**
     * Lấy thời gian cập nhật last price cuối cùng
     * 
     * @param symbol Symbol cần kiểm tra
     * @return Thời gian cập nhật hoặc null
     */
    public Instant getLastPriceTimestamp(Symbol symbol) {
        PriceData priceData = lastPrices.get(symbol);
        return priceData != null ? priceData.getTimestamp() : null;
    }
    

    
    /**
     * Price Data holder
     */
    private static class PriceData {
        private final Money price;
        private final Instant timestamp;
        
        public PriceData(Money price, Instant timestamp) {
            this.price = price;
            this.timestamp = timestamp;
        }
        
        public Money getPrice() {
            return price;
        }
        
        public Instant getTimestamp() {
            return timestamp;
        }
    }
}
