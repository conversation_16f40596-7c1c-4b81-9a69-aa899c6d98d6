package com.icetea.lotus.matching.infrastructure.messaging.producer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.matching.domain.entity.Order;
import com.icetea.lotus.matching.domain.entity.Trade;
import com.icetea.lotus.matching.infrastructure.constants.OrderEventType;
import com.icetea.lotus.matching.infrastructure.messaging.dto.ClosePositionMessage;
import com.icetea.lotus.matching.infrastructure.messaging.dto.FundingRateMessage;
import com.icetea.lotus.matching.infrastructure.messaging.dto.FutureTradePlateMessage;
import com.icetea.lotus.matching.infrastructure.messaging.dto.IndexPriceMessage;
import com.icetea.lotus.matching.infrastructure.messaging.dto.LastPriceMessage;
import com.icetea.lotus.matching.infrastructure.messaging.dto.MarkPriceMessage;
import com.icetea.lotus.matching.infrastructure.messaging.dto.OrderCommand;
import com.icetea.lotus.matching.infrastructure.messaging.dto.OrderEventMessage;
import com.icetea.lotus.matching.infrastructure.messaging.dto.OrderTriggeredMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * Future-Core Kafka Producer - Copy từ Future-Core module
 * Handles publishing futures trading events
 *
 * <AUTHOR> nguyen
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FutureCoreKafkaProducer {

    public static final String UNKNOWN = "unknown";
    public static final String ORDER_PLACED = "ORDER_PLACED";
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ObjectMapper objectMapper;

    // Contract Topics - Copy từ Future-Core module configuration
    @Value("${topic-kafka.contract.order-new:contract-order-new}")
    private String contractOrderNewTopic;

    @Value("${topic-kafka.contract.order-cancel:contract-order-cancel}")
    private String contractOrderCancelTopic;

    @Value("${topic-kafka.contract.order-completed:contract-order-completed}")
    private String contractOrderCompletedTopic;

    @Value("${topic-kafka.contract.order-cancel-success:contract-order-cancel-success}")
    private String contractOrderCancelSuccessTopic;

    @Value("${topic-kafka.contract.trade:contract-trade}")
    private String contractTradeTopic;

    @Value("${topic-kafka.contract.trade-plate:contract-trade-plate}")
    private String contractTradePlateTopic;

    @Value("${topic-kafka.contract.mark-price:contract-mark-price}")
    private String contractMarkPriceTopic;

    @Value("${topic-kafka.contract.index-price:contract-index-price}")
    private String contractIndexPriceTopic;

    @Value("${topic-kafka.contract.funding-rate:contract-funding-rate}")
    private String contractFundingRateTopic;

    @Value("${topic-kafka.contract.last-price:contract-last-price}")
    private String contractLastPriceTopic;

    @Value("${topic-kafka.contract.order-events:contract-order-events}")
    private String contractOrderEventsTopic;

    @Value("${topic-kafka.contract.position-cancel-result:contract-position-cancel-result}")
    private String contractOrderClosePositionEventsTopic;

    @Value("${topic-kafka.contract.position-new:contract-position-new}")
    private String contractPositionNewTopic;

    @Value("${topic-kafka.contract.order-cancel-result}")
    private String contractOrderCancelResultTopic;

    @Value("${topic-kafka.contract.order-commands}")
    private String orderCommandsTopic;

    @Value("${topic-kafka.contract.order-cancel-all-result}")
    private String contractOrderCancelAllResultTopic;

    @Value("${topic-kafka.contract.order-triggered}")
    private String contractOrderTriggeredTopic;

    /**
     * Publish contract order new
     */
    public void publishContractOrderNew(String symbol, Object order) {
        publishAsync(contractOrderNewTopic, symbol, order, "contract order new");
    }

    /**
     * Publish contract order cancel
     */
    public void publishContractOrderCancel(String symbol, Object cancelRequest) {
        publishAsync(contractOrderCancelTopic, symbol, cancelRequest, "contract order cancel");
    }

    /**
     * Publish contract order completed
     */
    public void publishContractOrderCompleted(String symbol, Object order) {
        publishAsync(contractOrderCompletedTopic, symbol, order, "contract order completed");
    }

    /**
     * Publish contract position new - for market order partial fills
     */
    public void publishContractPositionNew(String symbol, Object order) {
        publishAsync(contractPositionNewTopic, symbol, order, "contract position new");
    }

    /**
     * Publish contract order cancel success - consistent with spot pattern
     */
    public void publishContractOrderCancelSuccess(String symbol, Object cancelResult) {
        publishAsync(contractOrderCancelSuccessTopic, symbol, cancelResult, "contract order cancel success");
    }

    /**
     * Publishes the result of a contract order cancellation to the appropriate Kafka topic.
     *
     * @param symbol       the symbol representing the contract associated with the order cancellation
     * @param cancelResult the result object of the contract order cancellation containing relevant details
     */
    public void publishContractOrderCancelResult(String symbol, Object cancelResult) {
        publishAsync(contractOrderCancelResultTopic, symbol, cancelResult, "contract order cancel success");
    }

    /**
     * Publishes the result of canceling all contract orders for a given symbol to the appropriate Kafka topic.
     *
     * @param symbol       the symbol representing the contract associated with the canceled orders
     * @param cancelResult the result object of the cancellation operation containing relevant details
     */
    public void publishContractOrderCancelAllResult(String symbol, Object cancelResult) {
        publishAsync(contractOrderCancelAllResultTopic, symbol, cancelResult, "contract order cancel all success");
    }

    /**
     * Publish contract trade
     */
    public void publishContractTrade(String symbol, Object trade) {
        publishAsync(contractTradeTopic, symbol, trade, "contract trade");
    }

    /**
     * Sends a cancel order command to the Kafka topic.
     *
     * @param order The order to cancel.
     */
    public void sendCancelOrderCommand(Order order) {

        OrderCommand command = OrderCommand.builder()
                .commandId(UUID.randomUUID().toString())
                .type(OrderCommand.OrderCommandType.CANCEL_ORDER)
                .order(order)
                .timestamp(LocalDateTime.now())
                .build();

        sendOrderCommand(command);
    }

    /**
     * Publish contract trade batch
     */
    public void publishContractTradeBatch(List<?> trades, String symbol) {
        if (trades == null || trades.isEmpty()) {
            return;
        }

        CompletableFuture.runAsync(() -> {
            try {
                // Extract symbol from first trade for key
                String tradesJson = objectMapper.writeValueAsString(trades);
                kafkaTemplate.send(contractTradeTopic, symbol, tradesJson);
                log.info("Published {} contract trades for symbol: {}", trades.size(), symbol);
            } catch (JsonProcessingException e) {
                log.error("Error serializing contract trades to JSON", e);
            }
        });
    }

    /**
     * Publish contract trade plate
     */
    public void publishContractTradePlate(String symbol, FutureTradePlateMessage tradePlate) {
        publishAsync(contractTradePlateTopic, symbol, tradePlate, "contract trade plate");
    }

    /**
     * Publish contract mark price - Copy từ PriceProducer
     */
    public void publishContractMarkPrice(String symbol, BigDecimal price, BigDecimal volume) {
        try {
            MarkPriceMessage message = MarkPriceMessage.builder()
                    .symbol(symbol)
                    .price(price)
                    .volume(volume)
                    .timestamp(LocalDateTime.now())
                    .build();

            publishAsync(contractMarkPriceTopic, symbol, message, "contract mark price");

        } catch (Exception e) {
            log.error("Failed to publish contract mark price for symbol: {}", symbol, e);
        }
    }

    /**
     * Publish contract index price
     */
    public void publishContractIndexPrice(String symbol, BigDecimal price) {
        try {
            IndexPriceMessage message = IndexPriceMessage.builder()
                    .symbol(symbol)
                    .price(price)
                    .timestamp(LocalDateTime.now())
                    .build();

            publishAsync(contractIndexPriceTopic, symbol, message, "contract index price");

        } catch (Exception e) {
            log.error("Failed to publish contract index price for symbol: {}", symbol, e);
        }
    }

    /**
     * Publish contract funding rate
     */
    public void publishContractFundingRate(String symbol, BigDecimal rate) {
        try {
            FundingRateMessage message = FundingRateMessage.builder()
                    .symbol(symbol)
                    .fundingRate(rate)
                    .timestamp(LocalDateTime.now())
                    .build();

            publishAsync(contractFundingRateTopic, symbol, message, "contract funding rate");

        } catch (Exception e) {
            log.error("Failed to publish contract funding rate for symbol: {}", symbol, e);
        }
    }


    /**
     * Send last price - Copy từ PriceProducer.sendLastPrice()
     */
    public void sendLastPrice(String symbol, BigDecimal price, BigDecimal volume) {
        try {
            log.info("Gửi thông tin giá cuối cùng đến Kafka, symbol = {}, price = {}, volume = {}",
                    symbol, price, volume);

            LastPriceMessage message = LastPriceMessage.builder()
                    .symbol(symbol)
                    .price(price)
                    .volume(volume)
                    .timestamp(LocalDateTime.now())
                    .build();

            publishAsync(contractLastPriceTopic, symbol, message, "contract last price");

        } catch (Exception e) {
            log.error("Lỗi khi gửi thông tin giá cuối cùng đến Kafka cho symbol: {}", symbol, e);
        }
    }

    /**
     * Publish order placed event
     */
    public void publishOrderPlacedEvent(List<Trade> trade, OrderEventType commandType, String symbol, String orderId) {
        try {
            OrderEventMessage event = orderEventMessageBuilder(commandType, trade, symbol,  orderId);
            publishAsync(contractOrderEventsTopic, symbol, event, "contract order placed event");
            log.info("Published contract order placed event for order: {}", orderId);
        } catch (Exception e) {
            log.error("Failed to publish contract order placed event", e);
        }
    }

    /**
     * Publish order close position event
     */
    public void publishOrderClosePositionEvent(String symbol, ClosePositionMessage events) {
        try {

            publishAsync(contractOrderClosePositionEventsTopic, symbol, events, "contract order close position events");

        } catch (Exception e) {
            log.error("Failed to publish contract order close position events", e);
        }
    }

    public OrderEventMessage orderEventMessageBuilder(OrderEventType eventType, List<Trade> trade, String symbol, String orderId) {
        return OrderEventMessage.builder()
                .eventType(eventType)
                .symbol(symbol)
                .trades(trade)
                .orderId(orderId)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * Publish order updated event
     */
    public void publishOrderUpdatedEvent(Object order) {
        try {
            String symbol = extractSymbol(order);
            OrderEventMessage event = OrderEventMessage.builder()
                    .eventType(OrderEventType.ORDER_UPDATED)
                    .symbol(symbol)
                    .timestamp(LocalDateTime.now())
                    .build();
            publishAsync(contractOrderEventsTopic, symbol, event, "contract order updated event");
            log.info("Published contract order updated event for order: {}", extractOrderId(order));
        } catch (Exception e) {
            log.error("Failed to publish contract order updated event", e);
        }
    }

    /**
     * Generic async publish method
     */
    private void publishAsync(String topic, String key, Object message, String eventType) {
        CompletableFuture.runAsync(() -> {
            try {
                String messageJson = objectMapper.writeValueAsString(message);
                kafkaTemplate.send(topic, key, messageJson);
                log.info("Published {} for key: {} to topic: {}", eventType, key, topic);
            } catch (JsonProcessingException e) {
                log.error("Error serializing {} to JSON for key: {}", eventType, key, e);
            }
        });
    }

    /**
     * Sends an order command to the Kafka topic.
     *
     * @param command The order command to send.
     */
    private void sendOrderCommand(OrderCommand command) {
        // Extract symbol key as plain string
        String symbol = command != null && command.getOrder() != null && command.getOrder().getSymbol() != null
                ? command.getOrder().getSymbol().getValue()
                : UNKNOWN;

        try {
            // Convert payload so that nested value objects are flattened for Kafka JSON consumers
            Map<String, Object> payload = buildOrderCommandPayload(command);
            publishAsync(orderCommandsTopic, symbol, payload, "contract order command");
            log.info("Send success command: {}, symbol: {}, topic: {}", command.getType(), symbol, orderCommandsTopic);
        } catch (Exception e) {
            log.error("Send failed command: {}, symbol: {}, topic: {}", command.getType(), symbol, orderCommandsTopic, e);
        }
    }

    private Map<String, Object> buildOrderCommandPayload(OrderCommand command) {
        Map<String, Object> map = new java.util.HashMap<>();
        if (command == null) {
            return map;
        }
        map.put("commandId", command.getCommandId());
        map.put("type", command.getType() != null ? command.getType().name() : null);
        map.put("timestamp", command.getTimestamp());
        map.put("order", convertOrderForKafka(command.getOrder()));
        return map;
    }

    private Map<String, Object> convertOrderForKafka(Order order) {
        Map<String, Object> m = new java.util.HashMap<>();
        if (order == null) {
            return m;
        }
        // Scalars
        m.put("orderId", order.getOrderId() != null ? order.getOrderId().getValue() : null);
        m.put("memberId", order.getMemberId());
        m.put("symbol", order.getSymbol() != null ? order.getSymbol().getValue() : null);
        m.put("direction", order.getDirection() != null ? order.getDirection().name() : null);
        m.put("type", order.getType() != null ? order.getType().name() : null);
        m.put("status", order.getStatus() != null ? order.getStatus().name() : null);

        // Monetary values to BigDecimal
        m.put("price", order.getPrice() != null ? order.getPrice().getValue() : null);
        m.put("size", order.getSize() != null ? order.getSize().getValue() : null);
        m.put("filledSize", order.getFilledSize() != null ? order.getFilledSize().getValue() : null);
        m.put("leverage", order.getLeverage() != null ? order.getLeverage() : null);
        m.put("baseSymbol", order.getBaseSymbol() != null ? order.getBaseSymbol() : null);
        m.put("positionId", order.getPositionId() != null ? order.getPositionId() : null);
        m.put("isSTP", order.getIsSTP() != null ? order.getIsSTP() : null);
        // Timestamps - keep as is (ObjectMapper configured for JavaTime)
        m.put("timestamp", order.getTimestamp());
        return m;
    }

    public void publishStopOrderTriggered(String symbol, Order triggeredOrder) {
        try {
            OrderTriggeredMessage message = OrderTriggeredMessage.builder()
                    .orderId(triggeredOrder.getOrderId().getValue())
                    .symbol(symbol)
                    .price(triggeredOrder.getPrice().getValue())
                    .size(triggeredOrder.getSize().getValue())
                    .memberId(triggeredOrder.getMemberId())
                    .filledSize(triggeredOrder.getFilledSize().getValue())
                    .triggerPrice(triggeredOrder.getStopPrice().getValue())
                    .direction(triggeredOrder.getDirection())
                    .status(triggeredOrder.getStatus())
                    .type(triggeredOrder.getType())
                    .build();
            publishAsync(contractOrderTriggeredTopic, symbol, message, "contract order triggered");
            log.info("Published contract order triggered event: {}", extractOrderId(triggeredOrder));
        } catch (Exception e) {
            log.error("Failed to publish contract order triggered event", e);
        }
    }
    // ==================== HELPER METHODS ====================

    /**
     * Extract order ID from order object
     */
    private String extractOrderId(Object order) {
        try {
            if (order instanceof Map) {
                return String.valueOf(((Map<?, ?>) order).get("orderId"));
            }
            return UNKNOWN;
        } catch (Exception e) {
            return UNKNOWN;
        }
    }

    /**
     * Extract symbol from order object
     */
    private String extractSymbol(Object order) {
        try {
            if (order instanceof Map) {
                return String.valueOf(((Map<?, ?>) order).get("symbol"));
            }
            return UNKNOWN;
        } catch (Exception e) {
            return UNKNOWN;
        }
    }
}
