package com.icetea.lotus.matching.infrastructure.exchange;

import com.icetea.lotus.matching.domain.entity.ExchangeOrder;
import com.icetea.lotus.matching.domain.enums.SpotOrderDirection;
import com.icetea.lotus.matching.domain.enums.SpotOrderStatus;
import com.icetea.lotus.matching.domain.enums.SpotOrderType;
import com.icetea.lotus.matching.infrastructure.constants.CommonConstance;
import com.icetea.lotus.matching.infrastructure.event.TradeExecutedEvent;
import com.icetea.lotus.matching.infrastructure.messaging.dto.ExchangeOrderMessage;
import com.icetea.lotus.matching.infrastructure.messaging.producer.ExchangeKafkaProducer;
import com.icetea.lotus.matching.infrastructure.service.LastPriceService;
import com.icetea.lotus.matching.infrastructure.stp.SelfTradePreventionMode;
import com.icetea.lotus.matching.infrastructure.stp.SelfTradePreventionResult;
import com.icetea.lotus.matching.infrastructure.stp.SelfTradePreventionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ConcurrentSkipListMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.AMOUNT;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.BUY_ORDER_ID;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.BUY_TURNOVER;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.DIRECTION;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.IS_PARTIALLY_FILL;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.PRICE;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.SELL_ORDER_ID;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.SELL_TURNOVER;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.SYMBOL;
import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.TIMESTAMP;

/**
 * Pure DTO-based Exchange Matching Engine for ultra-high performance
 * <p>
 * This is a complete rewrite using only ExchangeOrderDTO for all operations.
 * No Order/DTO conversion overhead in hot paths.
 * <p>
 * Key Features:
 * - Pure DTO architecture
 * - Zero conversion overhead
 * - STP (Self-Trade Prevention) with CANCEL_MAKER mode
 * - Thread-safe concurrent operations
 * - High-performance order books
 */
@Component
public class ExchangeMatchingEngine {

    private static final Logger logger = LoggerFactory.getLogger(ExchangeMatchingEngine.class);

    // ===== CORE DATA STRUCTURES =====

    // Symbol for this engine instance
    private String symbol;

    // Trading halt flag
    private final AtomicBoolean tradingHalt = new AtomicBoolean(false);

    // Precision settings (critical for small amount handling)
    private int coinScale = 8; // Default to 8, should be set from ExchangeCoin

    // DTO-based order books (high performance)
    private final ConcurrentSkipListMap<BigDecimal, DTOMergeOrder> buyLimitOrderBook =
            new ConcurrentSkipListMap<>(Collections.reverseOrder()); // Highest price first
    private final ConcurrentSkipListMap<BigDecimal, DTOMergeOrder> sellLimitOrderBook =
            new ConcurrentSkipListMap<>(); // Lowest price first

    // DTO-based market order lists
    private final CopyOnWriteArrayList<ExchangeOrder> buyMarketOrderList = new CopyOnWriteArrayList<>();
    private final CopyOnWriteArrayList<ExchangeOrder> sellMarketOrderList = new CopyOnWriteArrayList<>();

    // Locks for thread safety
    private final ReadWriteLock buyLimitLock = new ReentrantReadWriteLock();
    private final ReadWriteLock sellLimitLock = new ReentrantReadWriteLock();

    // Order tracking
    private final Map<String, BigDecimal> orderTradedAmounts = new HashMap<>();
    private final Map<String, BigDecimal> orderTurnovers = new HashMap<>();

    // Track maker prices for average price calculation
    private final Map<String, List<BigDecimal>> orderMakerPrices = new HashMap<>();

    // Trade plate publisher for real-time trade plate updates
    private final TradePlatePublisher tradePlatePublisher;

    private final SelfTradePreventionService stpService;

    // Event publisher for stop order processing
    private final ApplicationEventPublisher eventPublisher;

    // Kafka producer for sending cancel messages
    private final ExchangeKafkaProducer exchangeKafkaProducer;

    // Services for last price management
    private final ExchangeCompatibilityService exchangeCompatibilityService;
    private final LastPriceService lastPriceService;

    @Autowired
    private ExchangeMatchingEngine(TradePlatePublisher tradePlatePublisher,
                                   SelfTradePreventionService stpService,
                                   ApplicationEventPublisher eventPublisher,
                                   ExchangeKafkaProducer exchangeKafkaProducer,
                                   ExchangeCompatibilityService exchangeCompatibilityService,
                                   LastPriceService lastPriceService) {
        this.tradePlatePublisher = tradePlatePublisher;
        this.stpService = stpService;
        this.eventPublisher = eventPublisher;
        this.exchangeKafkaProducer = exchangeKafkaProducer;
        this.exchangeCompatibilityService = exchangeCompatibilityService;
        this.lastPriceService = lastPriceService;
    }

    public ExchangeMatchingEngine(String symbol, TradePlatePublisher tradePlatePublisher,
                                  SelfTradePreventionService stpService, ApplicationEventPublisher eventPublisher,
                                  ExchangeKafkaProducer exchangeKafkaProducer,
                                  ExchangeCompatibilityService exchangeCompatibilityService,
                                  LastPriceService lastPriceService) {
        this(tradePlatePublisher, stpService, eventPublisher, exchangeKafkaProducer, exchangeCompatibilityService, lastPriceService);
        this.symbol = symbol;
    }

    // ===== MAIN TRADING METHODS =====

    /**
     * Main trading method - Pure DTO version
     */
    public Object trade(ExchangeOrder orderDTO) {
        if (tradingHalt.get()) {
            return createFailureResult(orderDTO.getOrderId(), orderDTO.getSymbol(),
                    "Trading is halted for symbol: " + orderDTO.getSymbol());
        }

        if (!validateOrderFormat(orderDTO)) {
            return createFailureResult(orderDTO.getOrderId(), orderDTO.getSymbol(),
                    "Invalid order format");
        }

        try {
            //   CRITICAL FIX: Check if triggered stop order is already completed
            if (isTriggeredStopOrderAlreadyCompleted(orderDTO)) {
                logger.info("Triggered stop order {} already completed, skipping processing", orderDTO.getOrderId());
                return createSuccessResult(orderDTO.getOrderId(), symbol, new ArrayList<>(),
                        List.of(orderDTO), new ArrayList<>());
            }

            //   CRITICAL FIX: Initialize tracking maps for new orders (especially triggered stop orders)
            initializeOrderTracking(orderDTO);

            List<Object> allTrades = new ArrayList<>();
            List<ExchangeOrder> completedOrders = new ArrayList<>();
            List<ExchangeOrder> partiallyFilledOrders = new ArrayList<>();

            if (orderDTO.getType() == SpotOrderType.MARKET_PRICE) {
                // Market order processing (STOP_MARKET đã được chuyển thành MARKET_PRICE sau khi trigger)
                processMarketOrder(orderDTO, allTrades, completedOrders, partiallyFilledOrders);
            } else {
                // Limit order processing (STOP_LIMIT đã được chuyển thành LIMIT_PRICE sau khi trigger)
                processLimitOrder(orderDTO, allTrades, completedOrders, partiallyFilledOrders);
            }

            // Handle completed orders
            handleCompletedOrders(completedOrders);
            return createSuccessResult(
                    orderDTO.getOrderId(),
                    orderDTO.getSymbol(),
                    allTrades,
                    completedOrders,
                    partiallyFilledOrders
            );

        } catch (Exception e) {
            logger.error("Error processing order {}: {}", orderDTO.getOrderId(), e.getMessage(), e);
            return createFailureResult(orderDTO.getOrderId(), orderDTO.getSymbol(),
                    "Internal error: " + e.getMessage());
        }
    }

    /**
     * Process market order
     * Market orders follow IOC (Immediate or Cancel) logic:
     * - Market BUY orders need SELL limit orders to match against
     * - Market SELL orders need BUY limit orders to match against
     * - If no corresponding orders exist, cancel immediately
     */
    private void processMarketOrder(ExchangeOrder marketOrderDTO, List<Object> trades, List<ExchangeOrder> completedOrders, List<ExchangeOrder> partiallyFilledOrders) {
        // Check if there are matchable orders in the target order book
        ConcurrentSkipListMap<BigDecimal, DTOMergeOrder> targetOrderBook = getTargetOrderBookForMarketOrder(marketOrderDTO);

        if (!hasMatchableOrdersInBook(targetOrderBook)) {
            handleMarketOrderWithNoMatches(marketOrderDTO);
            return;
        }

        // Execute matching with available orders
        executeMarketOrderMatching(marketOrderDTO, trades, completedOrders, partiallyFilledOrders);

        // Handle post-matching logic
        handleMarketOrderPostMatching(marketOrderDTO, completedOrders);
    }

    /**
     * Get target order book for market order - extracted to reduce complexity
     */
    private ConcurrentSkipListMap<BigDecimal, DTOMergeOrder> getTargetOrderBookForMarketOrder(ExchangeOrder marketOrderDTO) {
        // Market BUY → check SELL limit order book
        // Market SELL → check BUY limit order book
        if (marketOrderDTO.getDirection() == SpotOrderDirection.BUY) {
            return sellLimitOrderBook;  // Market BUY needs SELL orders
        } else {
            return buyLimitOrderBook;   // Market SELL needs BUY orders
        }
    }

    /**
     * Check if order book has matchable orders - extracted to reduce complexity
     */
    private boolean hasMatchableOrdersInBook(ConcurrentSkipListMap<BigDecimal, DTOMergeOrder> targetOrderBook) {
        for (DTOMergeOrder mergeOrder : targetOrderBook.values()) {
            if (!mergeOrder.getOrders().isEmpty()) {
                return true;
            }
        }
        return false;
    }

    /**
     * Handle market order when no matches available - extracted to reduce complexity
     */
    private void handleMarketOrderWithNoMatches(ExchangeOrder marketOrderDTO) {
        String targetDirection = marketOrderDTO.getDirection() == SpotOrderDirection.BUY ? "SELL" : "BUY";
        logger.info("Market {} order {} cancelled immediately - no {} limit orders available in queue",
                marketOrderDTO.getDirection(), marketOrderDTO.getOrderId(), targetDirection);

        // Send market order to cancel flow via kafka message
        sendMarketOrderToCancelFlow(marketOrderDTO);
    }

    /**
     * Execute market order matching - extracted to reduce complexity
     */
    private void executeMarketOrderMatching(ExchangeOrder marketOrderDTO, List<Object> trades, List<ExchangeOrder> completedOrders, List<ExchangeOrder> partiallyFilledOrders) {
        if (marketOrderDTO.getDirection() == SpotOrderDirection.BUY) {
            // Buy market order: match with sell limit orders
            matchMarketWithLimitOrders(sellLimitOrderBook, marketOrderDTO, sellLimitLock, trades, completedOrders, partiallyFilledOrders);
        } else {
            // Sell market order: match with buy limit orders
            matchMarketWithLimitOrders(buyLimitOrderBook, marketOrderDTO, buyLimitLock, trades, completedOrders, partiallyFilledOrders);
        }
    }

    /**
     * Handle market order post-matching logic - extracted to reduce complexity
     */
    private void handleMarketOrderPostMatching(ExchangeOrder marketOrderDTO, List<ExchangeOrder> completedOrders) {
        if (isOrderDTOCompleted(marketOrderDTO)) {
            handleFullyMatchedMarketOrder(marketOrderDTO, completedOrders);
        } else if (hasAnyTrades(marketOrderDTO)) {
            handlePartiallyMatchedMarketOrder(marketOrderDTO, completedOrders);
        } else {
            handleUnmatchedMarketOrder(marketOrderDTO);
        }
    }

    /**
     * Handle fully matched market order - extracted to reduce complexity
     */
    private void handleFullyMatchedMarketOrder(ExchangeOrder marketOrderDTO, List<ExchangeOrder> completedOrders) {
        if (!completedOrders.contains(marketOrderDTO)) {
            completedOrders.add(marketOrderDTO);
            logger.info("Market taker order fully matched: {} - will be sent to topic for status update",
                    marketOrderDTO.getOrderId());
        }
    }

    /**
     * Handle partially matched market order - extracted to reduce complexity
     */
    private void handlePartiallyMatchedMarketOrder(ExchangeOrder marketOrderDTO, List<ExchangeOrder> completedOrders) {
        if (!completedOrders.contains(marketOrderDTO)) {
            completedOrders.add(marketOrderDTO);
            logger.info("Market taker order partially filled (IOC): {} - traded: {}/{} - will be sent to topic for status update",
                    marketOrderDTO.getOrderId(),
                    getOrderTradedAmount(marketOrderDTO),
                    marketOrderDTO.getAmount());
        }
    }

    /**
     * Handle unmatched market order - extracted to reduce complexity
     */
    private void handleUnmatchedMarketOrder(ExchangeOrder marketOrderDTO) {
        logger.info("Market taker order no matches: {} - sending to cancel flow",
                marketOrderDTO.getOrderId());

        // Send market order with no matches to cancel flow via kafka message
        sendMarketOrderToCancelFlow(marketOrderDTO);
    }

    /**
     * Process limit order
     */
    private void processLimitOrder(ExchangeOrder limitOrderDTO, List<Object> trades, List<ExchangeOrder> completedOrders, List<ExchangeOrder> partiallyFilledOrders) {
        if (limitOrderDTO.getDirection() == SpotOrderDirection.BUY) {
            // Buy limit order: first match with sell limit orders, then sell market orders

            // 1. Match with sell limit orders
            matchLimitWithLimitOrders(sellLimitOrderBook, limitOrderDTO, sellLimitLock, trades, completedOrders, partiallyFilledOrders);

            // 2. If not fully filled, match with sell market orders
            if (!isOrderDTOCompleted(limitOrderDTO)) {
                matchLimitWithMarketOrders(sellMarketOrderList, limitOrderDTO, trades, completedOrders, partiallyFilledOrders);
            }

            // 3. If still not fully filled, add to order book
            if (!isOrderDTOCompleted(limitOrderDTO)) {
                addLimitOrderDTOToBook(limitOrderDTO);
                logger.info("Added BUY limit order {} to order book - not fully filled", limitOrderDTO.getOrderId());
            } else {
                logger.info("BUY limit order {} fully completed - NOT adding to order book", limitOrderDTO.getOrderId());
            }
        } else {
            // Sell limit order: first match with buy limit orders, then buy market orders

            // 1. Match with buy limit orders
            matchLimitWithLimitOrders(buyLimitOrderBook, limitOrderDTO, buyLimitLock, trades, completedOrders, partiallyFilledOrders);

            // 2. If not fully filled, match with buy market orders
            if (!isOrderDTOCompleted(limitOrderDTO)) {
                matchLimitWithMarketOrders(buyMarketOrderList, limitOrderDTO, trades, completedOrders, partiallyFilledOrders);
            }

            // 3. If still not fully filled, add to order book
            if (!isOrderDTOCompleted(limitOrderDTO)) {
                addLimitOrderDTOToBook(limitOrderDTO);
                logger.info("Added SELL limit order {} to order book - not fully filled", limitOrderDTO.getOrderId());
            } else {
                logger.info("SELL limit order {} fully completed - NOT adding to order book", limitOrderDTO.getOrderId());
            }
        }

        //   Check if taker order (limit order) is completed - Add to completedOrders for notification
        // Status change will be handled by other service
        boolean isCompleted = isOrderDTOCompleted(limitOrderDTO);
        BigDecimal currentTradedAmount = orderTradedAmounts.getOrDefault(limitOrderDTO.getOrderId(), BigDecimal.ZERO);
        BigDecimal orderTradedAmount = limitOrderDTO.getTradedAmount() != null ? limitOrderDTO.getTradedAmount() : BigDecimal.ZERO;

        logger.info("Limit order {} completion check: isCompleted={}, trackingTradedAmount={}, orderTradedAmount={}, totalAmount={}",
                limitOrderDTO.getOrderId(), isCompleted, currentTradedAmount, orderTradedAmount, limitOrderDTO.getAmount());

        // Only add if not already in completedOrders (avoid duplicates)
        if (isCompleted && !completedOrders.contains(limitOrderDTO)) {
            completedOrders.add(limitOrderDTO);
            logger.info("Limit taker order fully matched: {} - will be sent to topic for status update",
                    limitOrderDTO.getOrderId());
        }
    }

    // ===== CORE MATCHING METHODS =====

    /**
     * Match market order with limit orders
     */
    private void matchMarketWithLimitOrders(ConcurrentSkipListMap<BigDecimal, DTOMergeOrder> orderBook,
                                            ExchangeOrder marketOrderDTO,
                                            ReadWriteLock lock,
                                            List<Object> trades,
                                            List<ExchangeOrder> completedOrders,
                                            List<ExchangeOrder> partiallyFilledOrders) {
        lock.writeLock().lock();
        try {
            processMarketOrderMatching(orderBook, marketOrderDTO, trades, completedOrders, partiallyFilledOrders, lock);
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * Process market order matching logic - extracted to reduce complexity
     */
    private void processMarketOrderMatching(ConcurrentSkipListMap<BigDecimal, DTOMergeOrder> orderBook,
                                            ExchangeOrder marketOrderDTO,
                                            List<Object> trades,
                                            List<ExchangeOrder> completedOrders,
                                            List<ExchangeOrder> partiallyFilledOrders,
                                            ReadWriteLock lock) {
        lock.writeLock().lock();
        try {
            Iterator<Map.Entry<BigDecimal, DTOMergeOrder>> mergeOrderIterator = orderBook.entrySet().iterator();
            boolean exitLoop = false;

            while (!exitLoop && mergeOrderIterator.hasNext()) {
                Map.Entry<BigDecimal, DTOMergeOrder> entry = mergeOrderIterator.next();
                DTOMergeOrder mergeOrder = entry.getValue();

                exitLoop = processOrdersInMergeOrder(mergeOrder, marketOrderDTO, trades, completedOrders, partiallyFilledOrders, lock);

                // Remove empty merge orders
                if (mergeOrder.getOrders().isEmpty()) {
                    mergeOrderIterator.remove();
                }
            }

        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * Process orders in merge order - extracted to reduce complexity
     */
    private boolean processOrdersInMergeOrder(DTOMergeOrder mergeOrder,
                                              ExchangeOrder marketOrderDTO,
                                              List<Object> trades,
                                              List<ExchangeOrder> completedOrders,
                                              List<ExchangeOrder> partiallyFilledOrders,
                                              ReadWriteLock lock) {
        lock.writeLock().lock();
        try {
            Iterator<ExchangeOrder> orderIterator = mergeOrder.getOrders().iterator();
            boolean exitLoop = false;

            while (!exitLoop && orderIterator.hasNext()) {
                ExchangeOrder matchOrder = orderIterator.next();

                // Create trade between DTOs
                Object trade = createTrade(marketOrderDTO, matchOrder);
                if (trade != null) {
                    exitLoop = processSuccessfulTrade(trade, marketOrderDTO, matchOrder, orderIterator, trades, completedOrders, partiallyFilledOrders);
                }
            }

            return exitLoop;
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * Process successful trade - extracted to reduce complexity
     */
    private boolean processSuccessfulTrade(Object trade,
                                           ExchangeOrder marketOrderDTO,
                                           ExchangeOrder matchOrder,
                                           Iterator<ExchangeOrder> orderIterator,
                                           List<Object> trades,
                                           List<ExchangeOrder> completedOrders,
                                           List<ExchangeOrder> partiallyFilledOrders) {
        trades.add(trade);

        // Update order quantities
        // marketOrderDTO is taker, matchOrder is maker
        updateOrderDTOAfterTrade(marketOrderDTO, trade, matchOrder.getPrice()); // taker
        updateOrderDTOAfterTrade(matchOrder, trade, matchOrder.getPrice()); // maker

        // Handle completed match order
        handleCompletedMatchOrder(matchOrder, orderIterator, completedOrders, partiallyFilledOrders);

        // Check if market order is completed
        return isOrderDTOCompleted(marketOrderDTO);
    }

    /**
     * Handle completed match order - extracted to reduce complexity
     */
    private void handleCompletedMatchOrder(ExchangeOrder matchOrder,
                                           Iterator<ExchangeOrder> orderIterator,
                                           List<ExchangeOrder> completedOrders,
                                           List<ExchangeOrder> partiallyFilledOrders) {
        if (isOrderDTOCompleted(matchOrder)) {
            // Only add if not already in completedOrders (avoid duplicates)
            if (!completedOrders.contains(matchOrder)) {
                completedOrders.add(matchOrder);
            }
            orderIterator.remove(); // Remove từ queue
            logger.info("Match order {} fully matched and removed from queue", matchOrder.getOrderId());
        } else if (matchOrder.getStatus() == SpotOrderStatus.PARTIAL_FILLED && !partiallyFilledOrders.contains(matchOrder)) {
            // Add to partially filled orders for notification
            partiallyFilledOrders.add(matchOrder);
        }
    }

    /**
     * Match limit order with limit orders
     */
    private void matchLimitWithLimitOrders(ConcurrentSkipListMap<BigDecimal, DTOMergeOrder> orderBook,
                                           ExchangeOrder limitOrderDTO,
                                           ReadWriteLock lock,
                                           List<Object> trades,
                                           List<ExchangeOrder> completedOrders,
                                           List<ExchangeOrder> partiallyFilledOrders) {
        lock.writeLock().lock();
        try {
            Iterator<Map.Entry<BigDecimal, DTOMergeOrder>> mergeOrderIterator = orderBook.entrySet().iterator();
            boolean exitLoop = false;

            while (!exitLoop && mergeOrderIterator.hasNext()) {
                Map.Entry<BigDecimal, DTOMergeOrder> entry = mergeOrderIterator.next();
                BigDecimal price = entry.getKey();
                DTOMergeOrder mergeOrder = entry.getValue();

                // Check price matching
                if (!isPriceMatchingForLimitOrder(limitOrderDTO, price)) {
                    break;
                }

                exitLoop = processLimitOrdersInMergeOrder(mergeOrder, limitOrderDTO, trades, completedOrders, partiallyFilledOrders, lock);

                // Remove empty merge orders
                if (mergeOrder.getOrders().isEmpty()) {
                    mergeOrderIterator.remove();
                }
            }
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * Process limit order matching logic - extracted to reduce complexity
     */
    @SuppressWarnings("all")
    private void processLimitOrderMatching(ConcurrentSkipListMap<BigDecimal, DTOMergeOrder> orderBook,
                                           ExchangeOrder limitOrderDTO,
                                           List<Object> trades,
                                           List<ExchangeOrder> completedOrders,
                                           List<ExchangeOrder> partiallyFilledOrders) {
        Iterator<Map.Entry<BigDecimal, DTOMergeOrder>> mergeOrderIterator = orderBook.entrySet().iterator();
        boolean exitLoop = false;

        while (!exitLoop && mergeOrderIterator.hasNext()) {
            Map.Entry<BigDecimal, DTOMergeOrder> entry = mergeOrderIterator.next();
            BigDecimal price = entry.getKey();
            DTOMergeOrder mergeOrder = entry.getValue();

            // Check price matching
            if (!isPriceMatchingForLimitOrder(limitOrderDTO, price)) {
                break;
            }

//            exitLoop = processLimitOrdersInMergeOrder(mergeOrder, limitOrderDTO, trades, completedOrders, partiallyFilledOrders);

            // Remove empty merge orders
            if (mergeOrder.getOrders().isEmpty()) {
                mergeOrderIterator.remove();
            }
        }
    }

    /**
     * Check if price matches for limit order - extracted to reduce complexity
     */
    private boolean isPriceMatchingForLimitOrder(ExchangeOrder limitOrderDTO, BigDecimal price) {
        if (limitOrderDTO.getDirection() == SpotOrderDirection.BUY) {
            return limitOrderDTO.getPrice().compareTo(price) >= 0;
        } else {
            return limitOrderDTO.getPrice().compareTo(price) <= 0;
        }
    }

    /**
     * Process limit orders in merge order - extracted to reduce complexity
     */
    private boolean processLimitOrdersInMergeOrder(DTOMergeOrder mergeOrder,
                                                   ExchangeOrder limitOrderDTO,
                                                   List<Object> trades,
                                                   List<ExchangeOrder> completedOrders,
                                                   List<ExchangeOrder> partiallyFilledOrders,
                                                   ReadWriteLock lock) {
        lock.writeLock().lock();
        try {
            Iterator<ExchangeOrder> orderIterator = mergeOrder.getOrders().iterator();
            boolean exitLoop = false;

            while (!exitLoop && orderIterator.hasNext()) {
                ExchangeOrder matchOrder = orderIterator.next();

                // Create trade
                Object trade = createTrade(limitOrderDTO, matchOrder);
                if (trade != null) {
                    exitLoop = processSuccessfulLimitTrade(trade, limitOrderDTO, matchOrder, orderIterator, trades, completedOrders, partiallyFilledOrders);
                }
            }

            return exitLoop;
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * Process successful limit trade - extracted to reduce complexity
     */
    private boolean processSuccessfulLimitTrade(Object trade,
                                                ExchangeOrder limitOrderDTO,
                                                ExchangeOrder matchOrder,
                                                Iterator<ExchangeOrder> orderIterator,
                                                List<Object> trades,
                                                List<ExchangeOrder> completedOrders,
                                                List<ExchangeOrder> partiallyFilledOrders) {
        trades.add(trade);

        // Update order quantities
        // limitOrderDTO is taker, matchOrder is maker
        updateOrderDTOAfterTrade(limitOrderDTO, trade, matchOrder.getPrice()); // taker
        updateOrderDTOAfterTrade(matchOrder, trade, matchOrder.getPrice()); // maker

        // Handle completed match order for limit trading
        handleCompletedLimitMatchOrder(matchOrder, orderIterator, completedOrders, partiallyFilledOrders);

        // Check if limit order is completed
        return isOrderDTOCompleted(limitOrderDTO);
    }

    /**
     * Handle completed limit match order - extracted to reduce complexity
     */
    private void handleCompletedLimitMatchOrder(ExchangeOrder matchOrder,
                                                Iterator<ExchangeOrder> orderIterator,
                                                List<ExchangeOrder> completedOrders,
                                                List<ExchangeOrder> partiallyFilledOrders) {
        if (isOrderDTOCompleted(matchOrder)) {
            // Only add if not already in completedOrders (avoid duplicates)
            if (!completedOrders.contains(matchOrder)) {
                completedOrders.add(matchOrder);
            }
            orderIterator.remove(); // Remove từ queue
            logger.info("Match order {} fully matched and removed from queue - will be sent to topic for status update",
                    matchOrder.getOrderId());
        } else if (matchOrder.getStatus() == SpotOrderStatus.PARTIAL_FILLED && !partiallyFilledOrders.contains(matchOrder)) {
            // Add to partially filled orders for notification
            partiallyFilledOrders.add(matchOrder);
        }
    }

    /**
     * Match limit order with market orders
     */
    private void matchLimitWithMarketOrders(CopyOnWriteArrayList<ExchangeOrder> marketOrderList,
                                            ExchangeOrder limitOrderDTO,
                                            List<Object> trades,
                                            List<ExchangeOrder> completedOrders,
                                            List<ExchangeOrder> partiallyFilledOrders) {

        for (ExchangeOrder matchOrder : marketOrderList) {
            // Create trade
            Object trade = createTrade(limitOrderDTO, matchOrder);
            if (trade != null) {
                trades.add(trade);

                // Update order quantities
                // limitOrderDTO is taker, matchOrder is maker
                updateOrderDTOAfterTrade(limitOrderDTO, trade, matchOrder.getPrice()); // taker
                updateOrderDTOAfterTrade(matchOrder, trade, matchOrder.getPrice()); // maker

                // Check if orders are completed - Add to completedOrders for notification
                if (isOrderDTOCompleted(matchOrder)) {
                    // Only add if not already in completedOrders (avoid duplicates)
                    if (!completedOrders.contains(matchOrder)) {
                        completedOrders.add(matchOrder);
                    }
                    marketOrderList.remove(matchOrder); // Remove từ queue
                    logger.info("Match order {} fully matched and removed from queue - will be sent to topic for status update",
                            matchOrder.getOrderId());
                } else if (matchOrder.getStatus() == SpotOrderStatus.PARTIAL_FILLED && !partiallyFilledOrders.contains(matchOrder)) {
                    // Add to partially filled orders for notification
                    partiallyFilledOrders.add(matchOrder);
                }

                if (isOrderDTOCompleted(limitOrderDTO)) {
                    break;
                }
            }
        }
    }

    // ===== TRADE CREATION METHODS =====

    /**
     * Create trade between two DTOs with STP CANCEL_MAKER check
     */
    private Object createTrade(ExchangeOrder focusedOrderDTO, ExchangeOrder matchOrderDTO) {
        try {
            // ===== STP CHECK - ALWAYS USE CANCEL_MAKER MODE FOR SPOT TRADING =====
            SelfTradePreventionMode stpMode = SelfTradePreventionMode.CANCEL_MAKER;
            SelfTradePreventionResult result = stpService.checkAndPreventSelfTrade(focusedOrderDTO, matchOrderDTO, stpMode);

            if (!result.isTradeAllowed()) {
                return null;
            }

            // Determine deal price
            BigDecimal dealPrice;
            if (matchOrderDTO.getType() == SpotOrderType.LIMIT_PRICE) {
                dealPrice = matchOrderDTO.getPrice(); // Use maker's price for limit orders
            } else {
                dealPrice = focusedOrderDTO.getPrice(); // Use taker's price for market orders
            }

            // Calculate trade quantity using CoinTraderV2 logic
            BigDecimal focusedRemaining = calculateTradedAmount(focusedOrderDTO, dealPrice);
            BigDecimal matchRemaining = calculateTradedAmount(matchOrderDTO, dealPrice);

            BigDecimal tradeQuantity = focusedRemaining.min(matchRemaining);

            if (tradeQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                return null; // No quantity to trade
            }

            // ⭐ CRITICAL FIX: Remove traded amount from trade plate immediately (like CoinTraderV2.processMatch)
            // This ensures trade plate is updated in real-time during matching process
            removeTradedAmountFromTradePlate(focusedOrderDTO, tradeQuantity);
            removeTradedAmountFromTradePlate(matchOrderDTO, tradeQuantity);

            // Calculate basic turnover
            BigDecimal basicTurnover = dealPrice.multiply(tradeQuantity);

            // Apply market order turnover adjustment logic từ CoinTraderV2:852-861
            BigDecimal buyTurnover = basicTurnover;
            BigDecimal sellTurnover = basicTurnover;

            // Check if focusedOrder is market buy order
            if (focusedOrderDTO.getType() == SpotOrderType.MARKET_PRICE &&
                    focusedOrderDTO.getDirection() == SpotOrderDirection.BUY) {
                BigDecimal adjustTurnover = adjustMarketOrderTurnover(focusedOrderDTO, dealPrice);
                buyTurnover = basicTurnover.add(adjustTurnover);
            }
            // Check if matchOrder is market buy order
            else if (matchOrderDTO.getType() == SpotOrderType.MARKET_PRICE &&
                    matchOrderDTO.getDirection() == SpotOrderDirection.BUY) {
                BigDecimal adjustTurnover = adjustMarketOrderTurnover(matchOrderDTO, dealPrice);
                buyTurnover = basicTurnover.add(adjustTurnover);
            }

            // Create simple trade object - đồng bộ với exchange module (không có tradeId)
            Map<String, Object> trade = new HashMap<>();
            trade.put(SYMBOL, focusedOrderDTO.getSymbol());
            trade.put(BUY_ORDER_ID, focusedOrderDTO.getDirection() == SpotOrderDirection.BUY ?
                    focusedOrderDTO.getOrderId() : matchOrderDTO.getOrderId());
            trade.put(SELL_ORDER_ID, focusedOrderDTO.getDirection() == SpotOrderDirection.SELL ?
                    focusedOrderDTO.getOrderId() : matchOrderDTO.getOrderId());
            trade.put(PRICE, dealPrice);
            trade.put(AMOUNT, tradeQuantity);
            trade.put(BUY_TURNOVER, buyTurnover);
            trade.put(SELL_TURNOVER, sellTurnover);
            trade.put(DIRECTION, focusedOrderDTO.getDirection().getExchangeCode());
            trade.put("time", System.currentTimeMillis());

            // Check if either order will be partially filled after this trade
            boolean isPartiallyFilled = checkIfPartiallyFilled(focusedOrderDTO, matchOrderDTO, tradeQuantity, dealPrice);
            trade.put(IS_PARTIALLY_FILL, isPartiallyFilled);

            // Add maker price for average price calculation
            // The maker is always the matchOrderDTO (order already in order book)
            BigDecimal makerPrice = matchOrderDTO.getPrice();
            trade.put("makerPrice", makerPrice);

            logger.info("Created trade between orders {} and {}",
                    focusedOrderDTO.getOrderId(), matchOrderDTO.getOrderId());

            //   CRITICAL FIX: Update last price immediately after trade creation
            // Cập nhật lastprice ngay sau khi trade được tạo để đảm bảo cache được cập nhật
            updateLastPriceAfterTrade(focusedOrderDTO.getSymbol(), dealPrice);

            //   REAL-TIME TRADE PUBLISHING: Gửi individual trade ngay khi được tạo
            // Thay đổi từ batch publishing sang individual publishing để đúng với nghiệp vụ thực tế
            publishIndividualTrade(focusedOrderDTO.getSymbol(), trade);

            //   REAL-TIME EVENT PUBLISHING: Gửi TradeExecutedEvent ngay khi trade được tạo
            // Để trigger stop orders immediately thay vì đợi batch processing
            publishIndividualTradeExecutedEvent(focusedOrderDTO.getSymbol(), trade);

            return trade;

        } catch (Exception e) {
            logger.error("Error creating trade between DTOs {} and {}: {}",
                    focusedOrderDTO.getOrderId(), matchOrderDTO.getOrderId(), e.getMessage());
            return null;
        }
    }

    /**
     * CRITICAL FIX: Update last price immediately after trade creation
     * Cập nhật lastprice ngay sau khi trade được tạo để đảm bảo cache được cập nhật đúng
     */
    private void updateLastPriceAfterTrade(String symbol, BigDecimal tradePrice) {
        try {
            if (symbol == null || tradePrice == null || tradePrice.compareTo(BigDecimal.ZERO) <= 0) {
                logger.warn("Cannot update last price with invalid symbol or price: symbol={}, price={}", symbol, tradePrice);
                return;
            }

            // Update last price in ExchangeCompatibilityService cache
            if (exchangeCompatibilityService != null) {
                exchangeCompatibilityService.updateLastTradePriceCache(symbol, tradePrice);
                logger.info("Updated last trade price cache for symbol: {} = {}", symbol, tradePrice);

                //   CRITICAL FIX: Lưu last price vào MongoDB ngay lập tức
                exchangeCompatibilityService.saveLastPriceToMongoDB(symbol, tradePrice);
                logger.info("Saved last price to MongoDB for symbol: {} = {}", symbol, tradePrice);
            }

            // Update last price in LastPriceService for stop order initialization
            if (lastPriceService != null) {
                lastPriceService.updateLastPrice(symbol, tradePrice, CommonConstance.SPOT);
                logger.info("Updated LastPriceService for symbol: {} = {}", symbol, tradePrice);
            }

            // Publish last price update to Kafka
            if (exchangeKafkaProducer != null) {
                exchangeKafkaProducer.publishLastPriceUpdate(symbol, tradePrice, BigDecimal.ZERO);
                logger.info("Published last price update to Kafka for symbol: {} = {}", symbol, tradePrice);
            }

        } catch (Exception e) {
            logger.error("Error updating last price after trade for symbol: {}: {}", symbol, e.getMessage(), e);
        }
    }

    /**
     * Publish individual trade immediately after creation
     * Gửi từng trade ngay khi được khớp để đúng với nghiệp vụ thực tế
     */
    private void publishIndividualTrade(String symbol, Object trade) {
        try {
            // Convert to exchange-core format
            Object exchangeCoreTrade = convertToExchangeCoreTrade(trade);

            // Publish individual trade immediately
            exchangeKafkaProducer.publishExchangeTrade(symbol, exchangeCoreTrade);

            logger.info("Published individual trade for symbol: {} immediately after creation", symbol);
        } catch (Exception e) {
            logger.error("Error publishing individual trade for symbol: {}: {}", symbol, e.getMessage(), e);
        }
    }

    /**
     * Publish individual TradeExecutedEvent immediately after trade creation
     * Gửi event ngay khi trade được tạo để trigger stop orders real-time
     */
    private void publishIndividualTradeExecutedEvent(String symbol, Object trade) {
        try {
            if (trade instanceof ExchangeTrade exchangeTrade) {
                // Create TradeExecutedEvent with trade details
                TradeExecutedEvent event = TradeExecutedEvent.createSpotDetailed(
                        symbol,
                        exchangeTrade.getPrice(),
                        exchangeTrade.getAmount(),
                        exchangeTrade.getTradeId(),
                        exchangeTrade.getDirection(),
                        exchangeTrade.getTakerOrderId(),
                        exchangeTrade.getMakerOrderId()
                );

                // Publish event asynchronously to avoid blocking matching engine
                eventPublisher.publishEvent(event);

                logger.info("Published individual TradeExecutedEvent for stop order processing: symbol={}, price={}, amount={}, tradeId={}",
                        symbol, exchangeTrade.getPrice(), exchangeTrade.getAmount(), exchangeTrade.getTradeId());

            } else if (trade instanceof Map) {
                // Handle Map-based trade format
                Map<String, Object> tradeMap = (Map<String, Object>) trade;

                TradeExecutedEvent event = TradeExecutedEvent.createSpotDetailed(
                        symbol,
                        (BigDecimal) tradeMap.get(PRICE),
                        (BigDecimal) tradeMap.get(AMOUNT),
                        (String) tradeMap.get("tradeId"),
                        SpotOrderDirection.fromExchangeCode((String) tradeMap.get(DIRECTION)),
                        (String) tradeMap.get("takerOrderId"),
                        (String) tradeMap.get("makerOrderId")
                );

                eventPublisher.publishEvent(event);

                logger.info("Published individual TradeExecutedEvent from Map: symbol={}, price={}, amount={}, tradeId={}",
                        symbol, tradeMap.get(PRICE), tradeMap.get(AMOUNT), tradeMap.get("tradeId"));
            }

        } catch (Exception e) {
            logger.error("Error publishing individual TradeExecutedEvent for symbol: {}: {}", symbol, e.getMessage(), e);
        }
    }


    /**
     * Convert single trade to exchange-core format
     * CRITICAL FIX: Ensure proper conversion of SpotOrderDirection to ExchangeOrderDirection
     */
    private Object convertToExchangeCoreTrade(Object trade) {
        if (trade instanceof ExchangeTrade exchangeTrade) {

            // Create exchange-core compatible Map
            Map<String, Object> exchangeCoreTrade = new HashMap<>();
            exchangeCoreTrade.put(SYMBOL, exchangeTrade.getSymbol());
            exchangeCoreTrade.put(PRICE, exchangeTrade.getPrice());
            exchangeCoreTrade.put(AMOUNT, exchangeTrade.getAmount());
            exchangeCoreTrade.put("buyTurnover", exchangeTrade.getBuyTurnover());
            exchangeCoreTrade.put("sellTurnover", exchangeTrade.getSellTurnover());
            exchangeCoreTrade.put("buyOrderId", exchangeTrade.getBuyOrderId());
            exchangeCoreTrade.put("sellOrderId", exchangeTrade.getSellOrderId());
            exchangeCoreTrade.put("time", exchangeTrade.getTime());
            exchangeCoreTrade.put("isPartiallyFilled", exchangeTrade.getIsPartiallyFilled());
            exchangeCoreTrade.put("makerPrice", exchangeTrade.getMakerPrice());

            // CRITICAL FIX: Convert SpotOrderDirection to ExchangeOrderDirection string
            String directionStr = exchangeTrade.getDirection().getExchangeCode(); // "BUY" or "SELL"
            exchangeCoreTrade.put(DIRECTION, directionStr);

            return exchangeCoreTrade;

        } else if (trade instanceof Map) {
            Map<String, Object> tradeMap = new HashMap<>((Map<String, Object>) trade);

            // Ensure all required fields are present for exchange-core compatibility
            tradeMap.computeIfAbsent(SYMBOL, k -> this.symbol);

            // CRITICAL FIX: Convert SpotOrderDirection to string if present
            Object direction = tradeMap.get(DIRECTION);
            if (direction instanceof SpotOrderDirection spotOrderDirection) {
                tradeMap.put(DIRECTION, spotOrderDirection.getExchangeCode());
            }

            return tradeMap;
        }
        return trade;
    }

    // ===== UTILITY METHODS =====

    /**
     * Check if triggered stop order is already completed to prevent duplicate processing
     * Triggered stop orders (type=LIMIT_PRICE + has stopPrice) should only be processed once
     */
    private boolean isTriggeredStopOrderAlreadyCompleted(ExchangeOrder orderDTO) {
        // Only check for triggered stop orders
        if (orderDTO.getType() != SpotOrderType.LIMIT_PRICE ||
                orderDTO.getStopPrice() == null ||
                orderDTO.getStopPrice().compareTo(BigDecimal.ZERO) <= 0) {
            return false; // Not a triggered stop order
        }

        String orderId = orderDTO.getOrderId();

        // Check if order is already tracked and completed
        if (orderTradedAmounts.containsKey(orderId)) {
            BigDecimal trackedTradedAmount = orderTradedAmounts.get(orderId);
            BigDecimal orderAmount = orderDTO.getAmount();

            // If tracked traded amount >= order amount, order is already completed
            if (trackedTradedAmount.compareTo(orderAmount) >= 0) {
                logger.info("Triggered stop order {} already completed: trackedTradedAmount={}, orderAmount={}",
                        orderId, trackedTradedAmount, orderAmount);
                return true;
            }
        }

        return false;
    }

    /**
     * Initialize order tracking for new orders (especially triggered stop orders)
     * CRITICAL FIX: Ensure tracking maps are properly initialized
     */
    private void initializeOrderTracking(ExchangeOrder orderDTO) {
        String orderId = orderDTO.getOrderId();

        // Initialize tracking maps if not already present
        if (!orderTradedAmounts.containsKey(orderId)) {
            // Use existing tradedAmount from order object if available (for triggered stop orders)
            BigDecimal initialTradedAmount = orderDTO.getTradedAmount() != null ?
                    orderDTO.getTradedAmount() : BigDecimal.ZERO;
            orderTradedAmounts.put(orderId, initialTradedAmount);

            logger.info("Initialized orderTradedAmounts for order {}: {}", orderId, initialTradedAmount);
        }

        if (!orderTurnovers.containsKey(orderId)) {
            // Use existing turnover from order object if available (for triggered stop orders)
            BigDecimal initialTurnover = orderDTO.getTurnover() != null ?
                    orderDTO.getTurnover() : BigDecimal.ZERO;
            orderTurnovers.put(orderId, initialTurnover);

            logger.info("Initialized orderTurnovers for order {}: {}", orderId, initialTurnover);
        }
    }

    /**
     * Check if DTO order is completed
     * Logic từ CoinTraderV2 - market buy orders check turnover, others check quantity
     * ENHANCED: Use both tracking maps and order object properties for better accuracy
     */
    private boolean isOrderDTOCompleted(ExchangeOrder orderDTO) {
        if (orderDTO.getType() == SpotOrderType.MARKET_PRICE && orderDTO.getDirection() == SpotOrderDirection.BUY) {
            // Market buy: check turnover (amount field contains turnover for market buy orders)
            BigDecimal currentTurnover = orderTurnovers.getOrDefault(orderDTO.getOrderId(), BigDecimal.ZERO);

            //   ENHANCED: Also check order object turnover as fallback
            if (orderDTO.getTurnover() != null && orderDTO.getTurnover().compareTo(currentTurnover) > 0) {
                currentTurnover = orderDTO.getTurnover();
            }

            return currentTurnover.compareTo(orderDTO.getAmount()) >= 0;
        } else {
            // Regular orders (limit orders and market sell): check quantity
            BigDecimal tradedAmount = orderTradedAmounts.getOrDefault(orderDTO.getOrderId(), BigDecimal.ZERO);

            //   ENHANCED: Also check order object tradedAmount as fallback
            if (orderDTO.getTradedAmount() != null && orderDTO.getTradedAmount().compareTo(tradedAmount) > 0) {
                tradedAmount = orderDTO.getTradedAmount();
            }

            return tradedAmount.compareTo(orderDTO.getAmount()) >= 0;
        }
    }

    /**
     * Check if order has any trades (for partial fill detection)
     * Used to determine if market order should be marked as COMPLETED vs CANCELLED
     */
    private boolean hasAnyTrades(ExchangeOrder orderDTO) {
        if (orderDTO.getType() == SpotOrderType.MARKET_PRICE && orderDTO.getDirection() == SpotOrderDirection.BUY) {
            // Market buy order: check if any turnover was executed
            BigDecimal currentTurnover = orderTurnovers.getOrDefault(orderDTO.getOrderId(), BigDecimal.ZERO);
            return currentTurnover.compareTo(BigDecimal.ZERO) > 0;
        } else {
            // Regular orders: check if any quantity was traded
            BigDecimal tradedAmount = orderTradedAmounts.getOrDefault(orderDTO.getOrderId(), BigDecimal.ZERO);
            return tradedAmount.compareTo(BigDecimal.ZERO) > 0;
        }
    }

    /**
     * Check if either order will be partially filled after this trade
     */
    private boolean checkIfPartiallyFilled(ExchangeOrder focusedOrder, ExchangeOrder matchOrder,
                                           BigDecimal tradeQuantity, BigDecimal dealPrice) {
        // Check focused order
        boolean focusedPartiallyFilled = willBePartiallyFilled(focusedOrder, tradeQuantity, dealPrice);

        // Check match order
        boolean matchPartiallyFilled = willBePartiallyFilled(matchOrder, tradeQuantity, dealPrice);

        return focusedPartiallyFilled || matchPartiallyFilled;
    }

    /**
     * Check if a specific order will be partially filled after adding trade quantity
     */
    private boolean willBePartiallyFilled(ExchangeOrder order, BigDecimal tradeQuantity, BigDecimal dealPrice) {
        if (order.getType() == SpotOrderType.MARKET_PRICE && order.getDirection() == SpotOrderDirection.BUY) {
            // Market buy order: check turnover
            BigDecimal currentTurnover = orderTurnovers.getOrDefault(order.getOrderId(), BigDecimal.ZERO);
            BigDecimal newTurnover = currentTurnover.add(dealPrice.multiply(tradeQuantity));

            // Partially filled if has some turnover but not fully completed
            return newTurnover.compareTo(BigDecimal.ZERO) > 0 && newTurnover.compareTo(order.getAmount()) < 0;
        } else {
            // Regular orders: check quantity
            BigDecimal currentTradedAmount = orderTradedAmounts.getOrDefault(order.getOrderId(), BigDecimal.ZERO);
            BigDecimal newTradedAmount = currentTradedAmount.add(tradeQuantity);

            // Partially filled if has some traded amount but not fully completed
            return newTradedAmount.compareTo(BigDecimal.ZERO) > 0 && newTradedAmount.compareTo(order.getAmount()) < 0;
        }
    }

    /**
     * Get traded amount for an order (for logging purposes)
     */
    private BigDecimal getOrderTradedAmount(ExchangeOrder orderDTO) {
        if (orderDTO.getType() == SpotOrderType.MARKET_PRICE && orderDTO.getDirection() == SpotOrderDirection.BUY) {
            // Market buy order: return turnover executed
            return orderTurnovers.getOrDefault(orderDTO.getOrderId(), BigDecimal.ZERO);
        } else {
            // Regular orders: return quantity traded
            return orderTradedAmounts.getOrDefault(orderDTO.getOrderId(), BigDecimal.ZERO);
        }
    }

    /**
     * Update DTO order after trade
     *
     * @param order      The order to update
     * @param trade      The trade object
     * @param makerPrice The price of the maker order in this trade
     */
    private void updateOrderDTOAfterTrade(ExchangeOrder order, Object trade, BigDecimal makerPrice) {
        String orderId = order.getOrderId();

        // Extract trade data from trade object
        BigDecimal tradeQuantity = BigDecimal.ZERO;
        BigDecimal tradeTurnover = BigDecimal.ZERO;

        if (trade instanceof Map<?, ?> tradeMap) {
            Object amount = tradeMap.get(AMOUNT);
            if (amount instanceof BigDecimal bigDecimal) {
                tradeQuantity = bigDecimal;
            }

            // Extract turnover based on order direction
            Object turnover;
            if (order.getDirection() == SpotOrderDirection.BUY) {
                turnover = tradeMap.get("buyTurnover");
            } else {
                turnover = tradeMap.get("sellTurnover");
            }
            if (turnover instanceof BigDecimal bigDecimal) {
                tradeTurnover = bigDecimal;
            }
        }

        // Update traded amounts and turnovers in tracking maps
        orderTradedAmounts.merge(orderId, tradeQuantity, BigDecimal::add);
        orderTurnovers.merge(orderId, tradeTurnover, BigDecimal::add);

        // Track maker prices for average price calculation
        // Only track maker prices for the order (not for every trade it participates in)
        if (makerPrice != null) {
            orderMakerPrices.computeIfAbsent(orderId, k -> new ArrayList<>()).add(makerPrice);
        }

        // ⭐ CRITICAL FIX: Update tradedAmount and turnover in order object
        BigDecimal currentTradedAmount = order.getTradedAmount() != null ? order.getTradedAmount() : BigDecimal.ZERO;
        order.setTradedAmount(currentTradedAmount.add(tradeQuantity));

        BigDecimal currentTurnover = order.getTurnover() != null ? order.getTurnover() : BigDecimal.ZERO;
        order.setTurnover(currentTurnover.add(tradeTurnover));

        // Calculate and update average price based on maker prices
        updateOrderAveragePriceFromMakerPrices(order);

        // Update order status based on completion
        if (isOrderDTOCompleted(order)) {
            // Order is fully matched - status will be updated by market service to COMPLETED
            logger.info("Order {} fully matched: tradedAmount={}, totalAmount={}, turnover={} - status change handled by other service",
                    orderId, order.getTradedAmount(), order.getAmount(), order.getTurnover());
        } else {
            // Order is partially filled - update status to PARTIAL_FILLED
            if (order.getTradedAmount().compareTo(BigDecimal.ZERO) > 0) {
                order.setStatus(SpotOrderStatus.PARTIAL_FILLED);
                logger.info("Order {} partially filled: tradedAmount={}, totalAmount={}, status=PARTIAL_FILLED",
                        orderId, order.getTradedAmount(), order.getAmount());
            }
        }
    }

    /**
     * Add limit order DTO to order book
     */
    private void addLimitOrderDTOToBook(ExchangeOrder orderDTO) {
        ConcurrentSkipListMap<BigDecimal, DTOMergeOrder> orderBook;
        ReadWriteLock lock;

        if (orderDTO.getDirection() == SpotOrderDirection.BUY) {
            orderBook = buyLimitOrderBook;
            lock = buyLimitLock;
        } else {
            orderBook = sellLimitOrderBook;
            lock = sellLimitLock;
        }

        lock.writeLock().lock();
        try {
            BigDecimal price = orderDTO.getPrice();
            DTOMergeOrder dtoMergeOrder = orderBook.computeIfAbsent(price, k -> new DTOMergeOrder());
            dtoMergeOrder.addOrder(orderDTO);

            logger.info("Added limit order {} to order book at price {}", orderDTO.getOrderId(), price);
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * Validate order format (basic validation without business logic)
     */
    private boolean validateOrderFormat(ExchangeOrder order) {
        if (order.getSymbol() == null || order.getSymbol().isEmpty()) {
            return false;
        }

        if (order.getAmount() == null || order.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        // For limit orders, price must be valid
        return order.getType() != SpotOrderType.LIMIT_PRICE || (order.getPrice() != null && order.getPrice().compareTo(BigDecimal.ZERO) > 0);
    }

    /**
     * Handle completed orders
     * ENHANCED: Better cleanup and logging
     */
    private void handleCompletedOrders(List<ExchangeOrder> completedOrders) {
        for (ExchangeOrder orderDTO : completedOrders) {
            String orderId = orderDTO.getOrderId();

            // Log completion details for debugging
            BigDecimal tradedAmount = orderTradedAmounts.getOrDefault(orderId, BigDecimal.ZERO);
            BigDecimal turnover = orderTurnovers.getOrDefault(orderId, BigDecimal.ZERO);

            logger.info("Order completed: {} - tradedAmount: {}/{}, turnover: {}, type: {}, direction: {}",
                    orderId, tradedAmount, orderDTO.getAmount(), turnover,
                    orderDTO.getType(), orderDTO.getDirection());

            // Clear tracking for completed order
            orderTradedAmounts.remove(orderId);
            orderTurnovers.remove(orderId);
        }
    }


    /**
     * Create success result
     */
    private Object createSuccessResult(String orderId, String symbol, List<Object> trades, List<ExchangeOrder> completedOrders, List<ExchangeOrder> partiallyFilledOrders) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("orderId", orderId);
        result.put(SYMBOL, symbol);
        result.put("trades", trades);
        result.put("completedOrders", completedOrders);
        result.put("partiallyFilledOrders", partiallyFilledOrders);
        result.put(TIMESTAMP, System.currentTimeMillis());
        return result;
    }

    /**
     * Create failure result
     */
    private Object createFailureResult(String orderId, String symbol, String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("orderId", orderId);
        result.put(SYMBOL, symbol);
        result.put("message", message);
        result.put(TIMESTAMP, System.currentTimeMillis());
        return result;
    }

    // ===== ORDER MANAGEMENT METHODS =====

    /**
     * Add order directly to order book without matching (for restoration)
     */
    public boolean addOrderDirectly(ExchangeOrder orderDTO) {
        try {
            if (orderDTO.getType() == SpotOrderType.LIMIT_PRICE) {
                addLimitOrderDTOToBook(orderDTO);
            } else {
                // Add to market order list (MARKET_PRICE)
                if (orderDTO.getDirection() == SpotOrderDirection.BUY) {
                    buyMarketOrderList.add(orderDTO);
                } else {
                    sellMarketOrderList.add(orderDTO);
                }
            }
            return true;
        } catch (Exception e) {
            logger.error("Failed to add order directly: {}", orderDTO.getOrderId(), e);
            return false;
        }
    }

    /**
     * Cancel order
     */
    public boolean cancelOrder(String orderId) {
        try {
            logger.info("Cancelling order: {}", orderId);

            // Find and remove from limit order books
            for (DTOMergeOrder dtoMergeOrder : buyLimitOrderBook.values()) {
                if (dtoMergeOrder.getOrders().removeIf(dto -> dto.getOrderId().equals(orderId))) {
                    orderTradedAmounts.remove(orderId);
                    logger.info("Cancelled limit buy order: {}", orderId);
                    return true;
                }
            }

            for (DTOMergeOrder dtoMergeOrder : sellLimitOrderBook.values()) {
                if (dtoMergeOrder.getOrders().removeIf(dto -> dto.getOrderId().equals(orderId))) {
                    orderTradedAmounts.remove(orderId);
                    logger.info("Cancelled limit sell order: {}", orderId);
                    return true;
                }
            }

            // Find and remove from market order lists
            if (buyMarketOrderList.removeIf(dto -> dto.getOrderId().equals(orderId))) {
                orderTradedAmounts.remove(orderId);
                logger.info("Cancelled market buy order: {}", orderId);
                return true;
            }

            if (sellMarketOrderList.removeIf(dto -> dto.getOrderId().equals(orderId))) {
                orderTradedAmounts.remove(orderId);
                logger.info("Cancelled market sell order: {}", orderId);
                return true;
            }

            logger.warn("Order not found for cancellation: {}", orderId);
            return true;

        } catch (Exception e) {
            logger.error("Failed to cancel order {}: {}", orderId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Get order book for snapshot/monitoring
     */
    public Object getOrderBook() {
        try {
            Map<String, Object> orderBook = new HashMap<>();

            // Get buy orders
            Map<String, Object> buyOrders = new HashMap<>();
            for (Map.Entry<BigDecimal, DTOMergeOrder> entry : buyLimitOrderBook.entrySet()) {
                // Convert ConcurrentLinkedQueue to a standard ArrayList for reliable serialization
                buyOrders.put(entry.getKey().toString(), new ArrayList<>(entry.getValue().getOrders()));
            }

            // Get sell orders
            Map<String, Object> sellOrders = new HashMap<>();
            for (Map.Entry<BigDecimal, DTOMergeOrder> entry : sellLimitOrderBook.entrySet()) {
                // Convert ConcurrentLinkedQueue to a standard ArrayList for reliable serialization
                sellOrders.put(entry.getKey().toString(), new ArrayList<>(entry.getValue().getOrders()));
            }

            //   CRITICAL FIX: Include stop orders in snapshot
            List<Object> stopOrders = getStopOrdersForSnapshot();

            //   CRITICAL FIX: Create allOrders list with proper queue order for fallback
            List<ExchangeOrder> allOrders = createAllOrdersListWithQueueOrder();

            orderBook.put(SYMBOL, symbol);
            orderBook.put("buyOrders", buyOrders);
            orderBook.put("sellOrders", sellOrders);
            orderBook.put("allOrders", allOrders);
            orderBook.put("stopOrders", stopOrders);
            orderBook.put("timestamp", System.currentTimeMillis());

            return orderBook;

        } catch (Exception e) {
            logger.error("Error getting order book for symbol: {}", symbol, e);
            return Map.of(
                    SYMBOL, symbol,
                    "error", e.getMessage(),
                    "timestamp", System.currentTimeMillis()
            );
        }
    }

    /**
     * CRITICAL FIX: Create allOrders list with proper queue order
     * Tạo danh sách tất cả orders với đúng thứ tự queue để làm fallback
     */
    private List<ExchangeOrder> createAllOrdersListWithQueueOrder() {
        List<ExchangeOrder> allOrders = new ArrayList<>();

        try {
            // Add buy limit orders (sorted by price descending, then by queue order)
            buyLimitOrderBook.entrySet().stream()
                    .sorted(Map.Entry.<BigDecimal, DTOMergeOrder>comparingByKey().reversed())
                    .forEach(entry -> {
                        DTOMergeOrder mergeOrder = entry.getValue();
                        allOrders.addAll(mergeOrder.getOrders());
                    });

            // Add sell limit orders (sorted by price ascending, then by queue order)
            sellLimitOrderBook.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .forEach(entry -> {
                        DTOMergeOrder mergeOrder = entry.getValue();
                        allOrders.addAll(mergeOrder.getOrders());
                    });

            logger.info("Created allOrders list with {} orders in proper queue order", allOrders.size());

        } catch (Exception e) {
            logger.error("Error creating allOrders list with queue order", e);
        }

        return allOrders;
    }

    /**
     * CRITICAL FIX: Get stop orders for snapshot
     * Lấy stop orders từ MatchingEngineStopOrderManager để include vào snapshot
     */
    private List<Object> getStopOrdersForSnapshot() {
        try {
            // For now, return empty list since we don't have direct access to ExchangeCompatibilityService
            // Stop orders will be handled separately in the snapshot service
            logger.info("Stop orders will be handled by snapshot service for symbol: {}", symbol);
            return new ArrayList<>();
        } catch (Exception e) {
            logger.warn("Error getting stop orders for snapshot for symbol: {}", symbol, e);
            return new ArrayList<>();
        }
    }

    public boolean isMergeOrderEmpty(SpotOrderDirection direction, BigDecimal price) {
        return direction == SpotOrderDirection.BUY ? buyLimitOrderBook.get(price).isEmpty() : sellLimitOrderBook.get(price).isEmpty();
    }

    public String getIDofOldestOrderInOrderBook(SpotOrderDirection direction, BigDecimal price) {
        return (direction == SpotOrderDirection.BUY
                ? buyLimitOrderBook.get(price).getOldestOrder()
                : sellLimitOrderBook.get(price).getOldestOrder()
        ).getOrderId();
    }

    // ===== DTO MERGE ORDER CLASS =====

    /**
     * DTO-based MergeOrder for better performance
     */
    private static class DTOMergeOrder {
        private final ConcurrentLinkedQueue<ExchangeOrder> orders = new ConcurrentLinkedQueue<>();

        public void addOrder(ExchangeOrder order) {
            //   Check for duplicate orders by ID to prevent triggered orders being added twice
            boolean isDuplicate = orders.stream()
                    .anyMatch(existingOrder -> existingOrder.getOrderId().equals(order.getOrderId()));

            if (!isDuplicate) {
                orders.add(order);
            } else {
                // Log warning for duplicate order
                logger.info("WARNING: Duplicate order detected and prevented: {}", order.getOrderId());
            }
        }

        public Queue<ExchangeOrder> getOrders() {
            return orders;
        }

        public boolean isEmpty() {
            return orders.isEmpty();
        }

        public boolean removeOrder(ExchangeOrder order) {
            return orders.remove(order);
        }

        public ExchangeOrder getOldestOrder() {
            return orders.peek();
        }
    }

    // ===== GETTERS AND SETTERS =====

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public void setTradingHalt(boolean halt) {
        this.tradingHalt.set(halt);
    }

    public String getSymbol() {
        return symbol;
    }

    public boolean isTradingHalt() {
        return tradingHalt.get();
    }

    /**
     * Calculate the remaining amount of transactions for the order
     * Logic từ CoinTraderV2.calculateTradedAmount:744-753
     * FIXED: Use coinScale instead of hardcoded 8 for precision
     */
    private BigDecimal calculateTradedAmount(ExchangeOrder order, BigDecimal dealPrice) {
        if (order.getDirection() == SpotOrderDirection.BUY &&
                order.getType() == SpotOrderType.MARKET_PRICE) {
            // Market buy: calculate remaining quantity based on remaining turnover
            BigDecimal currentTurnover = orderTurnovers.getOrDefault(order.getOrderId(), BigDecimal.ZERO);
            BigDecimal leftTurnover = order.getAmount().subtract(currentTurnover);
            // CRITICAL FIX: Use coinScale for precision (not hardcoded 8)
            return leftTurnover.divide(dealPrice, coinScale, RoundingMode.DOWN);
        } else {
            // Regular orders: calculate remaining quantity
            BigDecimal tradedAmount = orderTradedAmounts.getOrDefault(order.getOrderId(), BigDecimal.ZERO);
            return order.getAmount().subtract(tradedAmount);
        }
    }

    /**
     * Adjust the remaining transaction volume of the market order
     * Logic từ CoinTraderV2.adjustMarketOrderTurnover:758-769
     * FIXED: Use coinScale instead of hardcoded 8 for precision
     */
    private BigDecimal adjustMarketOrderTurnover(ExchangeOrder order, BigDecimal dealPrice) {
        if (order.getDirection() == SpotOrderDirection.BUY &&
                order.getType() == SpotOrderType.MARKET_PRICE) {

            // Get current turnover for this order
            BigDecimal currentTurnover = orderTurnovers.getOrDefault(order.getOrderId(), BigDecimal.ZERO);
            BigDecimal leftTurnover = order.getAmount().subtract(currentTurnover);

            // Check if remaining turnover can't buy even smallest unit
            // CRITICAL FIX: Use coinScale for precision (not hardcoded 8)
            if (leftTurnover.divide(dealPrice, coinScale, RoundingMode.DOWN)
                    .compareTo(BigDecimal.ZERO) == 0) {
                // Set turnover to full amount (order completed)
                orderTurnovers.put(order.getOrderId(), order.getAmount());
                return leftTurnover;
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * Remove traded amount from trade plate for an order (copy từ CoinTraderV2.processMatch logic)
     * This method ensures trade plate is updated immediately when trade occurs
     */
    private void removeTradedAmountFromTradePlate(ExchangeOrder order, BigDecimal tradedAmount) {
        try {
            // Only remove from trade plate for limit orders (market orders are not in trade plate)
            if (order.getType() == SpotOrderType.LIMIT_PRICE && tradePlatePublisher != null) {
                // Get appropriate trade plate based on order direction
                TradePlate tradePlate = tradePlatePublisher.getTradePlate(order.getSymbol(), order.getDirection());

                // Remove traded amount from trade plate
                tradePlate.remove(order, tradedAmount);

                logger.info("Removed traded amount {} from trade plate for order {}: direction={}, price={}",
                        tradedAmount, order.getOrderId(), order.getDirection(), order.getPrice());
            }
        } catch (Exception e) {
            logger.warn("Failed to remove traded amount from trade plate for order {}: {}",
                    order.getOrderId(), e.getMessage());
        }
    }

    /**
     * Send market order to cancel flow via kafka message
     * This method sends the market order to exchange-order-cancel topic
     * so it can be processed through the standard cancel order flow
     */
    private void sendMarketOrderToCancelFlow(ExchangeOrder marketOrder) {
        try {
            // Convert ExchangeOrder to ExchangeOrderMessage for kafka
            ExchangeOrderMessage cancelMessage = ExchangeOrderMessage.mapFromExchangeOrder(marketOrder);

            // Send to exchange-order-cancel topic via ExchangeKafkaProducer.java:89
            exchangeKafkaProducer.publishExchangeOrderCancel(marketOrder.getSymbol(), cancelMessage);

            logger.info("Sent market order {} to cancel flow - amount: {}",
                    marketOrder.getOrderId(), marketOrder.getAmount());

        } catch (Exception e) {
            logger.error("Failed to send market order {} to cancel flow: {}",
                    marketOrder.getOrderId(), e.getMessage(), e);
        }
    }

    /**
     * Update order's average price based on maker prices
     * Average price = sum of maker prices / number of maker prices
     *
     * @param order The order to update
     */
    private void updateOrderAveragePriceFromMakerPrices(ExchangeOrder order) {
        try {
            String orderId = order.getOrderId();
            List<BigDecimal> makerPrices = orderMakerPrices.get(orderId);

            if (makerPrices != null && !makerPrices.isEmpty()) {
                // Calculate average of maker prices
                BigDecimal sum = BigDecimal.ZERO;
                for (BigDecimal price : makerPrices) {
                    sum = sum.add(price);
                }

                BigDecimal averagePrice = sum.divide(new BigDecimal(makerPrices.size()), 8, RoundingMode.HALF_UP);
                order.setAveragePrice(averagePrice);

                logger.info("Updated average price for order {} from {} maker prices: averagePrice={}",
                        orderId, makerPrices.size(), averagePrice);
            } else {
                // Set to zero if no maker prices tracked
                order.setAveragePrice(BigDecimal.ZERO);
                logger.info("No maker prices tracked for order {}, setting average price to 0", orderId);
            }
        } catch (ArithmeticException e) {
            logger.warn("Failed to calculate average price from maker prices for order {}: {}",
                    order.getOrderId(), e.getMessage(), e);
            order.setAveragePrice(BigDecimal.ZERO);
        }
    }
}
