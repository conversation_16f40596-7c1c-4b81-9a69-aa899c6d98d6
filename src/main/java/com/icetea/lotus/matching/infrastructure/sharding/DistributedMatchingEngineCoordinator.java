package com.icetea.lotus.matching.infrastructure.sharding;

import com.icetea.lotus.matching.domain.entity.Order;
import com.icetea.lotus.matching.domain.valueobject.Symbol;
import com.icetea.lotus.matching.infrastructure.futurecore.FutureCoreCompatibilityService;
import com.icetea.lotus.matching.infrastructure.messaging.producer.ExchangeKafkaProducer;
import com.icetea.lotus.matching.infrastructure.messaging.producer.FutureCoreKafkaProducer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

/**
 * Distributed Matching Engine Coordinator
 * Điều phối các operations giữa distributed matching engine pods
 * Ensures symbol ownership và distributed locking cho order processing
 * 
 * <AUTHOR> nguyen
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DistributedMatchingEngineCoordinator {

    private final SymbolShardingManager shardingManager;
    private final SymbolLoadBalancer loadBalancer;
    private final ExchangeKafkaProducer exchangeKafkaProducer;
    private final FutureCoreKafkaProducer futureCoreKafkaProducer;
    private final RedissonClient redissonClient;
    private final FutureCoreCompatibilityService futureCoreService;
    
    // Lock configuration
    private static final long DEFAULT_LOCK_WAIT_TIME = 100; // milliseconds
    private static final long DEFAULT_LOCK_LEASE_TIME = 5000; // milliseconds

    /**
     * OPTIMIZATION: Route order to correct pod if not owned by current pod
     * 
     * @param order Order to route
     * @return true if routing was successful
     */
    public boolean routeOrderToCorrectPod(Order order) {
        String symbol = order.getSymbol().getValue();
        
        try {
            // Check if this pod owns the symbol
            if (shardingManager.isSymbolOwnedByThisPod(symbol)) {
                // Process locally
                return true;
            }
            
            // Find the correct pod owner
            String ownerPod = shardingManager.getSymbolOwner(symbol);
            if (ownerPod == null) {
                // Try to claim the symbol for this pod
                if (shardingManager.claimSymbol(symbol)) {
                    log.info("Claimed unclaimed symbol {} for pod {}", symbol, shardingManager.getPodName());
                    return true;
                } else {
                    log.warn("Failed to claim symbol {} for pod {}", symbol, shardingManager.getPodName());
                    return false;
                }
            }
            
            // Route to correct pod via Kafka
            exchangeKafkaProducer.publishExchangeOrder(symbol, order);
            log.info("Routed order {} for symbol {} to pod {}",
                    order.getOrderId().getValue(), symbol, ownerPod);
            
            return true;
            
        } catch (Exception e) {
            log.error("Error routing order for symbol {} to correct pod", symbol, e);
            return false;
        }
    }
    
    /**
     * OPTIMIZATION: Ensure symbol ownership before processing
     * 
     * @param symbol Symbol to check and claim if needed
     * @return true if symbol is owned by this pod
     */
    public boolean ensureSymbolOwnership(Symbol symbol) {
        String symbolStr = symbol.getValue();
        
        try {
            // Check current ownership
            if (shardingManager.isSymbolOwnedByThisPod(symbolStr)) {
                return true;
            }
            
            // Try to claim if not owned
            if (shardingManager.claimSymbol(symbolStr)) {
                log.info("Successfully claimed symbol {} for pod {}", symbolStr, shardingManager.getPodName());
                return true;
            }
            
            // Check if we should migrate based on load balancing
            if (loadBalancer.migrateSymbolToPreferredPod(symbolStr)) {
                // Check again after migration
                return shardingManager.isSymbolOwnedByThisPod(symbolStr);
            }
            
            return false;
            
        } catch (Exception e) {
            log.error("Error ensuring symbol ownership for {}", symbolStr, e);
            return false;
        }
    }

    /**
     * Checks if the symbol is owned by the current pod.
     * This is a delegate method to SymbolShardingManager for easier access.
     *
     * @param symbol The symbol to check.
     * @return true if the symbol is owned by the current pod, false otherwise.
     */
    public boolean isSymbolOwnedByCurrentNode(Symbol symbol) {
        return shardingManager.isSymbolOwnedByThisPod(symbol.getValue());
    }

    /**
     * Check if a symbol has any owner (any pod is responsible for it)
     * This is used to determine if it's safe to discard messages
     *
     * @param symbol The symbol to check.
     * @return true if the symbol has an owner (any pod), false if no owner
     */
    public boolean hasSymbolOwner(Symbol symbol) {
        try {
            String owner = shardingManager.getSymbolOwner(symbol.getValue());
            return owner != null && !owner.trim().isEmpty();
        } catch (Exception e) {
            log.error("Error checking if symbol {} has owner", symbol.getValue(), e);
            // Return false to be safe - don't discard if we can't determine ownership
            return false;
        }
    }

    /**
     * Order processing result
     */
    public static class OrderProcessingResult {
        public final boolean success;
        public final String message;
        public final String errorCode;
        public final Integer tradeCount;
        public final String algorithm;
        public final Object performanceMetrics;

        private OrderProcessingResult(boolean success, String message, String errorCode,
                                    Integer tradeCount, String algorithm, Object performanceMetrics) {
            this.success = success;
            this.message = message;
            this.errorCode = errorCode;
            this.tradeCount = tradeCount;
            this.algorithm = algorithm;
            this.performanceMetrics = performanceMetrics;
        }

        public static OrderProcessingResult orderProcessSuccess(String message) {
            return new OrderProcessingResult(true, message, null, null, null, null);
        }

        public static OrderProcessingResult rejected(String message) {
            return new OrderProcessingResult(false, message, "REJECTED", null, null, null);
        }

        public static OrderProcessingResult error(String message) {
            return new OrderProcessingResult(false, message, "ERROR", null, null, null);
        }

        public OrderProcessingResult withTradeCount(Integer tradeCount) {
            return new OrderProcessingResult(this.success, this.message, this.errorCode,
                    tradeCount, this.algorithm, this.performanceMetrics);
        }

        public OrderProcessingResult withAlgorithm(String algorithm) {
            return new OrderProcessingResult(this.success, this.message, this.errorCode,
                    this.tradeCount, algorithm, this.performanceMetrics);
        }

        public OrderProcessingResult withPerformanceMetrics(Object performanceMetrics) {
            return new OrderProcessingResult(this.success, this.message, this.errorCode,
                    this.tradeCount, this.algorithm, performanceMetrics);
        }

        @Override
        public String toString() {
            return String.format("OrderProcessingResult{success=%s, message='%s', errorCode='%s', tradeCount=%d, algorithm='%s'}",
                    success, message, errorCode, tradeCount, algorithm);
        }
    }
    
    /**
     * Cluster health status
     */
    public static class ClusterHealthStatus {
        public final boolean healthy;
        public final int activePods;
        public final SymbolLoadBalancer.LoadBalancingStats loadStats;
        
        public ClusterHealthStatus(boolean healthy, int activePods, SymbolLoadBalancer.LoadBalancingStats loadStats) {
            this.healthy = healthy;
            this.activePods = activePods;
            this.loadStats = loadStats;
        }
        
        @Override
        public String toString() {
            return String.format("ClusterHealthStatus{healthy=%s, activePods=%d, loadStats=%s}", 
                    healthy, activePods, loadStats);
        }
    }
}
