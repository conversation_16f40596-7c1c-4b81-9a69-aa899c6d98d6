package com.icetea.lotus.matching.infrastructure.sharding.model;

/**
 * Partition strategies cho symbol sharding
 * Migrated từ future-core
 */
public enum PartitionStrategy {
    
    /**
     * Phân chia đều theo order ID hash
     */
    HASH_BASED,
    
    /**
     * Phân chia theo mức giá
     */
    PRICE_RANGE,
    
    /**
     * Phân chia theo volume order
     */
    VOLUME_BASED,
    
    /**
     * Xử lý tuần tự trên primary pod
     */
    SEQUENTIAL,
    
    /**
     * Hybrid strategy - kết hợp multiple strategies
     */
    HYBRID
}
