package com.icetea.lotus.matching.infrastructure.persistence.mongodb.service;

import com.icetea.lotus.matching.infrastructure.exchange.ExchangeCompatibilityService;
import com.icetea.lotus.matching.infrastructure.futurecore.FutureCoreCompatibilityService;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Scheduled Snapshot Service cho Matching Engine
 * Tự động save snapshots theo interval được config
 *
 * <AUTHOR> nguyen
 */
@Slf4j
@Service
@RequiredArgsConstructor
@SuppressWarnings("all")
public class SnapshotSchedulerService {

    private final ExchangeCompatibilityService exchangeService;

    private final FutureCoreCompatibilityService futureCoreService;

    // Updated to use Redis snapshot configuration instead of MongoDB
    @Value("${matching-engine.redis.snapshot.enabled:true}")
    private boolean snapshotEnabled;

    @Value("${matching-engine.redis.snapshot.auto-save:true}")
    private boolean autoSaveEnabled;

    @Value("${matching-engine.redis.snapshot.save-interval-seconds:60}")
    private int saveIntervalSeconds;

    @PostConstruct
    public void init() {
        log.info(" SnapshotSchedulerService initialized");
        log.info("Configuration: enabled={}, auto-save={}, interval={}s",
                snapshotEnabled, autoSaveEnabled, saveIntervalSeconds);
        log.info("Services: exchange={}, future-core={}",
                exchangeService != null, futureCoreService != null);

        if (!snapshotEnabled) {
            log.warn("   Snapshot is DISABLED - no snapshots will be saved");
        } else if (!autoSaveEnabled) {
            log.warn("   Auto-save is DISABLED - only manual snapshots will work");
        } else {
            log.info("  Snapshot auto-save is ENABLED - will save every {} seconds", saveIntervalSeconds);
        }
    }

    /**
     * Scheduled auto-save task - runs based on configured interval
     */
    @Scheduled(fixedRateString = "#{${matching-engine.redis.snapshot.save-interval-seconds:60} * 100}")
    public void performScheduledSnapshot() {
        log.info("SnapshotSchedulerService.performScheduledSnapshot() called at {}", java.time.LocalDateTime.now());

        if (!snapshotEnabled || !autoSaveEnabled) {
            log.warn("  Snapshot auto-save is disabled (enabled: {}, auto-save: {})", snapshotEnabled, autoSaveEnabled);
            return;
        }

        try {
            log.info("  Starting scheduled snapshot save (interval: {} seconds)", saveIntervalSeconds);

            int totalSaved = 0;

            // Save exchange (SPOT) snapshots
            if (exchangeService != null) {
                log.info(" Saving SPOT snapshots via ExchangeCompatibilityService...");
                exchangeService.saveAllSnapshots();
                log.info("  Completed SPOT snapshots save");
                totalSaved++;

            } else {
                log.warn("   ExchangeCompatibilityService is null - no SPOT snapshots will be saved");
            }

            // Save future-core (FUTURES) snapshots
            if (futureCoreService != null) {
                log.info(" Saving FUTURES snapshots via FutureCoreCompatibilityService...");
                futureCoreService.saveAllSnapshots();
                log.info("  Completed FUTURES snapshots save");
                totalSaved++;

            } else {
                log.warn("   FutureCoreCompatibilityService is null - no FUTURES snapshots will be saved");
            }

            if (totalSaved == 0) {
                log.warn("   No snapshots were saved - both services are null or failed");
            } else {
                log.info(" Completed scheduled snapshot save - {} service(s) processed", totalSaved);
            }

        } catch (Exception e) {
            log.error(" Critical error during scheduled snapshot save", e);
        }
    }

    /**
     * Heartbeat để verify scheduling hoạt động
     */
    @Scheduled(fixedRate = 30000) // 30 seconds
    public void testScheduleHeartbeat() {
        log.info("SnapshotSchedulerService heartbeat - Schedule is working! Time: {}", java.time.LocalDateTime.now());

        // Log service status
        if (exchangeService == null && futureCoreService == null) {
            log.warn("   Heartbeat: Both ExchangeService and FutureCoreService are NULL");
        } else {
            log.info("Heartbeat: Services available - Exchange: {}, FutureCore: {}",
                    exchangeService != null, futureCoreService != null);
        }

        // Log configuration status
        if (!snapshotEnabled) {
            log.warn("   Heartbeat: Snapshot is DISABLED");
        } else if (!autoSaveEnabled) {
            log.warn("   Heartbeat: Auto-save is DISABLED");
        }
    }

    /**
     * Manual trigger for snapshot save
     */
    public void triggerManualSnapshot() {
        log.info("🔧 Manual snapshot save triggered");
        performScheduledSnapshot();
    }

    /**
     * Get snapshot configuration info
     */
    public Map<String, Object> getSnapshotInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("snapshot_enabled", snapshotEnabled);
        info.put("auto_save_enabled", autoSaveEnabled);
        info.put("save_interval_seconds", saveIntervalSeconds);
        info.put("exchange_service_available", exchangeService != null);
        info.put("future_core_service_available", futureCoreService != null);
        info.put("last_check", java.time.LocalDateTime.now());
        return info;
    }


    /**
     * Check if snapshot system is ready
     */
    public boolean isReady() {
        return snapshotEnabled && autoSaveEnabled && (exchangeService != null || futureCoreService != null);
    }
}
