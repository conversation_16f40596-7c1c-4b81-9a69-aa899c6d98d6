package com.icetea.lotus.matching.infrastructure.messaging.filter.consumer;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.matching.infrastructure.futurecore.FutureCoreCompatibilityService;
import com.icetea.lotus.matching.infrastructure.messaging.producer.FutureCoreKafkaProducer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Future-Core Order Consumer - Copy từ Future-Core module
 * Processes futures trading orders and events from Kafka
 * 
 * <AUTHOR> nguyen
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FutureCoreOrderConsumer {
    
    private final FutureCoreCompatibilityService futureCoreService;
    private final ObjectMapper objectMapper;
    
    // Executor for async processing
    private final ExecutorService processingExecutor = Executors.newFixedThreadPool(20);

    /**
     * Handle funding rate updates - Copy từ OrderConsumer.handleFundingRate()
     */
    @KafkaListener(
            topics = "${topic-kafka.contract.funding-rate:contract-funding-rate}",
            containerFactory = "futuresKafkaListenerContainerFactory",
            groupId = "matching-engine-funding-rate")
    public void handleFundingRate(List<ConsumerRecord<String, String>> records, Acknowledgment ack) {
        log.info("Received {} funding rate update records", records.size());

        try {
            for (ConsumerRecord<String, String> consumerRecord : records) {
                processingExecutor.submit(() -> processFundingRate(consumerRecord));
            }

            // Manual acknowledgment sau khi submit tất cả tasks
            ack.acknowledge();

        } catch (Exception e) {
            log.error("Error processing funding rate batch", e);
            // Không acknowledge nếu có lỗi
        }
    }

    /**
     * Process individual funding rate update
     */
    private void processFundingRate(ConsumerRecord<String, String> consumerRecord) {
        log.info("Processing funding rate update for topic: {}, key: {}", consumerRecord.topic(), consumerRecord.key());

        try {
            String symbol = consumerRecord.key();
            String value = consumerRecord.value();

            JsonNode json = objectMapper.readTree(value);
            BigDecimal rate = new BigDecimal(json.get("rate").asText());

            // Update funding rate through Future-Core service
            futureCoreService.updateFundingRate(symbol, rate);

            log.info("Successfully updated funding rate for symbol: {} to {}", symbol, rate);

        } catch (Exception e) {
            log.error("Error processing funding rate update", e);
        }
    }
}
