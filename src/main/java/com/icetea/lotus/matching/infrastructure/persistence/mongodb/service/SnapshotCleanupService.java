package com.icetea.lotus.matching.infrastructure.persistence.mongodb.service;

import com.icetea.lotus.matching.infrastructure.persistence.mongodb.document.OrderBookSnapshotDocument;
import com.icetea.lotus.matching.infrastructure.persistence.mongodb.repository.OrderBookSnapshotRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * Service để cleanup old snapshots trong MongoDB
 * Copy từ future-core SnapshotCleanupService
 * 
 * <AUTHOR> nguyen
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SnapshotCleanupService {

    private final OrderBookSnapshotRepository repository;
    
    @Value("${matching-engine.snapshot.cleanup.enabled:true}")
    private boolean cleanupEnabled;
    
    @Value("${matching-engine.snapshot.cleanup.retention.hours:168}") // 7 days default
    private int retentionHours;
    
    @Value("${matching-engine.snapshot.cleanup.batch.size:100}")
    private int batchSize;
    
    @Value("${matching-engine.snapshot.max.versions.per.symbol:50}")
    private int maxVersionsPerSymbol;

    @Value("${matching-engine.snapshot.cleanup.grace.period.minutes:5}") // 5 minutes grace period
    private int gracePeriodMinutes;

    /**
     * Scheduled cleanup task - runs every hour
     */
//    @Scheduled(fixedRate = 3600000) // 1 hour
    public void performScheduledCleanup() {
        if (!cleanupEnabled) {
            log.info("Snapshot cleanup is disabled");
            return;
        }

        try {
            log.info("Starting scheduled snapshot cleanup");
            
            // Cleanup expired snapshots
            cleanupExpiredSnapshots();
            
            // Cleanup old snapshots beyond retention period
            cleanupOldSnapshots();
            
            // Cleanup excess versions per symbol
            cleanupExcessVersions();
            
            log.info("Completed scheduled snapshot cleanup");
            
        } catch (Exception e) {
            log.error("Error during scheduled snapshot cleanup", e);
        }
    }

    /**
     * Cleanup expired snapshots (using TTL)
     */
    public void cleanupExpiredSnapshots() {
        try {
            Instant now = Instant.now();
            List<OrderBookSnapshotDocument> expiredSnapshots = repository.findExpiredSnapshots(now);
            
            if (!expiredSnapshots.isEmpty()) {
                repository.deleteAll(expiredSnapshots);
                log.info("Cleaned up {} expired snapshots", expiredSnapshots.size());
            } else {
                log.info("No expired snapshots found");
            }
            
        } catch (Exception e) {
            log.error("Error cleaning up expired snapshots", e);
        }
    }

    /**
     * Cleanup old snapshots beyond retention period
     */
    public void cleanupOldSnapshots() {
        try {
            Instant cutoffTime = Instant.now().minus(retentionHours, ChronoUnit.HOURS);
            
            // Use pagination to avoid loading too many documents at once
            PageRequest pageRequest = PageRequest.of(0, batchSize);
            List<OrderBookSnapshotDocument> oldSnapshots = 
                repository.findByTimestampBeforeOrderByTimestampAsc(cutoffTime, pageRequest);
            
            int totalDeleted = 0;
            while (!oldSnapshots.isEmpty()) {
                repository.deleteAll(oldSnapshots);
                totalDeleted += oldSnapshots.size();
                
                // Get next batch
                oldSnapshots = repository.findByTimestampBeforeOrderByTimestampAsc(cutoffTime, pageRequest);
            }
            
            if (totalDeleted > 0) {
                log.info("Cleaned up {} old snapshots beyond retention period of {} hours", 
                        totalDeleted, retentionHours);
            } else {
                log.info("No old snapshots found beyond retention period");
            }
            
        } catch (Exception e) {
            log.error("Error cleaning up old snapshots", e);
        }
    }

    /**
     * Cleanup excess versions per symbol (keep only latest N versions)
     */
    public void cleanupExcessVersions() {
        try {
            List<String> symbols = repository.findDistinctSymbols();
            List<String> tradingTypes = repository.findDistinctTradingTypes();
            
            int totalDeleted = 0;
            
            for (String symbol : symbols) {
                for (String tradingType : tradingTypes) {
                    totalDeleted += cleanupExcessVersionsForSymbol(symbol, tradingType);
                }
            }
            
            if (totalDeleted > 0) {
                log.info("Cleaned up {} excess snapshot versions across all symbols", totalDeleted);
            } else {
                log.info("No excess snapshot versions found");
            }
            
        } catch (Exception e) {
            log.error("Error cleaning up excess versions", e);
        }
    }

    /**
     * Cleanup excess versions for specific symbol and trading type
     */
    private int cleanupExcessVersionsForSymbol(String symbol, String tradingType) {
        try {
            List<OrderBookSnapshotDocument> snapshots =
                repository.findBySymbolAndTradingTypeOrderByVersionDesc(symbol, tradingType);

            log.info("Found {} snapshots for symbol: {}, tradingType: {}", snapshots.size(), symbol, tradingType);

            if (snapshots.size() > maxVersionsPerSymbol) {
                //   CRITICAL FIX: Apply grace period to avoid deleting recently saved snapshots
                Instant gracePeriodCutoff = Instant.now().minus(gracePeriodMinutes, ChronoUnit.MINUTES);

                // Filter out snapshots that are too recent (within grace period)
                List<OrderBookSnapshotDocument> candidatesForDeletion = snapshots.subList(maxVersionsPerSymbol, snapshots.size())
                    .stream()
                    .filter(snapshot -> snapshot.getTimestamp().isBefore(gracePeriodCutoff))
                    .toList();

                if (!candidatesForDeletion.isEmpty()) {
                    repository.deleteAll(candidatesForDeletion);

                    log.info("Cleaned up {} excess versions for symbol: {}, tradingType: {} (applied {} min grace period)",
                             candidatesForDeletion.size(), symbol, tradingType, gracePeriodMinutes);

                    return candidatesForDeletion.size();
                } else {
                    log.info("No excess versions eligible for cleanup for symbol: {}, tradingType: {} (all within grace period)",
                             symbol, tradingType);
                    return 0;
                }
            }

            return 0;

        } catch (Exception e) {
            log.error("Error cleaning up excess versions for symbol: {}, tradingType: {}",
                     symbol, tradingType, e);
            return 0;
        }
    }

    /**
     * Manual cleanup for specific symbol
     */
    public void cleanupSymbol(String symbol) {
        try {
            log.info("Starting manual cleanup for symbol: {}", symbol);
            
            List<String> tradingTypes = repository.findDistinctTradingTypes();
            int totalDeleted = 0;
            
            for (String tradingType : tradingTypes) {
                totalDeleted += cleanupExcessVersionsForSymbol(symbol, tradingType);
            }
            
            log.info("Completed manual cleanup for symbol: {}, deleted {} snapshots", symbol, totalDeleted);
            
        } catch (Exception e) {
            log.error("Error during manual cleanup for symbol: {}", symbol, e);
        }
    }

    /**
     * Manual cleanup for specific symbol and trading type
     */
    public void cleanupSymbolAndTradingType(String symbol, String tradingType) {
        try {
            log.info("Starting manual cleanup for symbol: {}, tradingType: {}", symbol, tradingType);
            
            int deleted = cleanupExcessVersionsForSymbol(symbol, tradingType);
            
            log.info("Completed manual cleanup for symbol: {}, tradingType: {}, deleted {} snapshots", 
                    symbol, tradingType, deleted);
            
        } catch (Exception e) {
            log.error("Error during manual cleanup for symbol: {}, tradingType: {}", 
                     symbol, tradingType, e);
        }
    }


}
