package com.icetea.lotus.matching.infrastructure.config;

import com.icetea.lotus.matching.infrastructure.exchange.ExchangeCompatibilityService;
import com.icetea.lotus.matching.infrastructure.futurecore.FutureCoreCompatibilityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * Matching Engine Shutdown Hook
 * Đảm bảo snapshots được save trước khi application shutdown
 *
 * <AUTHOR> nguyen
 */
@Slf4j
@Component
@ConditionalOnProperty(
        prefix = "matching-engine.mongodb.snapshot",
        name = "enabled",
        havingValue = "true"
)
@RequiredArgsConstructor
public class MatchingEngineShutdownHook {

    private final ExchangeCompatibilityService exchangeService;

    private final FutureCoreCompatibilityService futureCoreService;

    @Value("${matching-engine.mongodb.snapshot.enabled:true}")
    private boolean snapshotEnabled;

    /**
     * Handle application shutdown event
     */
    @EventListener
    public void handleContextClosed(ContextClosedEvent event) {
        if (!snapshotEnabled) {
            log.info("Snapshot is disabled, skipping shutdown snapshot save");
            return;
        }

        try {
            log.info("======Matching Engine Shutdown Hook Started======");
            // Shutdown exchange service (includes snapshot save)
            if (exchangeService != null) {
                exchangeService.shutdown();
                log.info("Exchange service shutdown completed");
            }

            // Shutdown future-core service (includes snapshot save)
            if (futureCoreService != null) {
                futureCoreService.shutdown();
                log.info("Future-core service shutdown completed");
            }
            log.info("======Matching Engine Shutdown Hook Completed======");

        } catch (Exception e) {
            log.error("Error during matching engine shutdown", e);
        }
    }
}
