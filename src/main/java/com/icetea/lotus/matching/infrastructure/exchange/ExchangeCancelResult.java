package com.icetea.lotus.matching.infrastructure.exchange;

import lombok.Builder;
import lombok.Data;

/**
 * Exchange Cancel Result - Kết quả hủy order theo Exchange format
 * 
 * <AUTHOR> nguyen
 */
@Data
@Builder
public class ExchangeCancelResult {
    
    private boolean success;
    private String orderId;
    private String symbol;
    private String message;
    private String errorCode;
    private Long timestamp;
    
    /**
     * Create success result
     */
    public static ExchangeCancelResult success(String orderId, String symbol) {
        return ExchangeCancelResult.builder()
                .success(true)
                .orderId(orderId)
                .symbol(symbol)
                .message("Order cancelled successfully")
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * Create error result
     */
    public static ExchangeCancelResult error(String message) {
        return ExchangeCancelResult.builder()
                .success(false)
                .message(message)
                .errorCode("CANCEL_ERROR")
                .timestamp(System.currentTimeMillis())
                .build();
    }
}
