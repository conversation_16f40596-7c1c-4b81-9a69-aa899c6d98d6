package com.icetea.lotus.matching.infrastructure.sharding.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Thông tin load của pod
 * Migrated từ future-core
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PodLoadInfo {
    
    private String podName;
    private double cpuUsage;
    private double memoryUsage;
    private double networkUsage;
    private double diskUsage;
    
    // Order processing metrics
    private int orderRate; // orders per second
    private double avgLatency; // milliseconds
    private int activeConnections;
    private long totalOrdersProcessed;
    
    // Overall load score (0.0 to 1.0)
    private double overallLoad;
    
    // Health status
    private boolean healthy;
    private String healthStatus;
    private LocalDateTime lastUpdated;
    
    // Thresholds
    private static final double OVERLOAD_THRESHOLD = 0.8;
    private static final double UNDERLOAD_THRESHOLD = 0.3;
    
    /**
     * <PERSON><PERSON><PERSON> tra xem pod có bị overload không
     */
    public boolean isOverloaded() {
        return overallLoad > OVERLOAD_THRESHOLD;
    }
    
    /**
     * Kiểm tra xem pod có bị underload không
     */
    public boolean isUnderloaded() {
        return overallLoad < UNDERLOAD_THRESHOLD;
    }
    
    /**
     * Tính toán overall load score
     */
    public void calculateOverallLoad() {
        // Weighted average của các metrics
        this.overallLoad = (cpuUsage * 0.4) + 
                          (memoryUsage * 0.3) + 
                          (networkUsage * 0.2) + 
                          (diskUsage * 0.1);
        
        // Adjust based on order processing metrics
        if (avgLatency > 100) { // > 100ms
            this.overallLoad += 0.1;
        }
        
        if (orderRate > 1000) { // > 1000 orders/sec
            this.overallLoad += 0.1;
        }
        
        // Ensure within bounds
        this.overallLoad = Math.min(1.0, Math.max(0.0, this.overallLoad));
    }
}
