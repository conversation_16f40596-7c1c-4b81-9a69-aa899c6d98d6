package com.icetea.lotus.matching.infrastructure.integration;

import com.icetea.lotus.matching.domain.entity.Order;
import com.icetea.lotus.matching.domain.enums.OrderDirection;
import com.icetea.lotus.matching.domain.valueobject.Money;
import com.icetea.lotus.matching.domain.valueobject.Symbol;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.NavigableMap;
import java.util.Set;
import java.util.TreeMap;

/**
 * Adapter to convert OrderBookSnapshot to TradePlate format for backward compatibility
 * Maintains compatibility with existing WebSocket and API consumers
 */
@Slf4j
@Component
public class TradePlateAdapter {

    private static final int MAX_DEPTH_LEVELS = 20; // Maximum depth levels to include

    /**
     * Convert OrderBookSnapshot to TradePlate format
     *
     * @param orderBookSnapshot Order book snapshot from distributed engine
     * @param symbol            Trading symbol
     * @param direction         Order direction (BUY or SELL)
     * @return TradePlate object
     */
    public Object convertToTradePlate(Object orderBookSnapshot, Symbol symbol, OrderDirection direction) {
        try {
            if (orderBookSnapshot == null) {
                return createEmptyTradePlate(symbol, direction);
            }

            // Extract orders from snapshot based on direction
            NavigableMap<Money, List<Order>> orders = extractOrdersFromSnapshot(orderBookSnapshot, direction);

            // Convert to TradePlate format
            TradePlate tradePlate = new TradePlate(symbol.getValue(), mapDirection(direction));

            // Convert orders to TradePlateItems
            List<TradePlateItem> items = convertOrdersToTradePlateItems(orders, direction);
            tradePlate.setItems(items);

            log.info("Converted {} orders to TradePlate for symbol {} direction {}",
                    items.size(), symbol.getValue(), direction);

            return tradePlate;

        } catch (Exception e) {
            log.error("Failed to convert OrderBookSnapshot to TradePlate for symbol {}", symbol.getValue(), e);
            return createEmptyTradePlate(symbol, direction);
        }
    }

    /**
     * Convert both buy and sell sides to complete TradePlate pair
     *
     * @param orderBookSnapshot Order book snapshot
     * @param symbol            Trading symbol
     * @return Array with [buyTradePlate, sellTradePlate]
     */
    public Object[] convertToTradePlatePair(Object orderBookSnapshot, Symbol symbol) {
        Object buyTradePlate = convertToTradePlate(orderBookSnapshot, symbol, OrderDirection.BUY);
        Object sellTradePlate = convertToTradePlate(orderBookSnapshot, symbol, OrderDirection.SELL);

        return new Object[]{buyTradePlate, sellTradePlate};
    }

    /**
     * Create aggregated market depth from order book
     *
     * @param orderBookSnapshot Order book snapshot
     * @param symbol            Trading symbol
     * @param maxLevels         Maximum price levels to include
     * @return Market depth object
     */
    public Object createMarketDepth(Object orderBookSnapshot, Symbol symbol, int maxLevels) {
        try {
            NavigableMap<Money, List<Order>> buyOrders = extractOrdersFromSnapshot(orderBookSnapshot, OrderDirection.BUY);
            NavigableMap<Money, List<Order>> sellOrders = extractOrdersFromSnapshot(orderBookSnapshot, OrderDirection.SELL);

            List<DepthLevel> buyLevels = aggregateOrdersByPrice(buyOrders, OrderDirection.BUY, maxLevels);
            List<DepthLevel> sellLevels = aggregateOrdersByPrice(sellOrders, OrderDirection.SELL, maxLevels);

            return new MarketDepth(symbol.getValue(), buyLevels, sellLevels);

        } catch (Exception e) {
            log.error("Failed to create market depth for symbol {}", symbol.getValue(), e);
            return new MarketDepth(symbol.getValue(), new ArrayList<>(), new ArrayList<>());
        }
    }

    private List<TradePlateItem> convertOrdersToTradePlateItems(NavigableMap<Money, List<Order>> orders, OrderDirection direction) {
        List<TradePlateItem> items = new ArrayList<>();
        int count = 0;

        // For buy orders, iterate from highest to lowest price
        // For sell orders, iterate from lowest to highest price
        Set<Map.Entry<Money, List<Order>>> ordersIterable = (direction == OrderDirection.BUY) ?
                orders.descendingMap().entrySet() : orders.entrySet();

        for (Map.Entry<Money, List<Order>> priceLevel : ordersIterable) {
            if (count >= MAX_DEPTH_LEVELS) {
                break;
            }

            Money price = priceLevel.getKey();
            List<Order> ordersAtPrice = priceLevel.getValue();

            // Aggregate quantity at this price level
            BigDecimal totalQuantity = ordersAtPrice.stream()
                    .map(order -> order.getRemainingQuantity().getAmount())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (totalQuantity.compareTo(BigDecimal.ZERO) > 0) {
                items.add(new TradePlateItem(price.getAmount(), totalQuantity));
                count++;
            }
        }

        return items;
    }

    private List<DepthLevel> aggregateOrdersByPrice(NavigableMap<Money, List<Order>> orders, OrderDirection direction, int maxLevels) {
        List<DepthLevel> levels = new ArrayList<>();
        int count = 0;

        Set<Map.Entry<Money, List<Order>>> ordersIterable = (direction == OrderDirection.BUY) ?
                orders.descendingMap().entrySet() : orders.entrySet();

        for (Map.Entry<Money, List<Order>> priceLevel : ordersIterable) {
            if (count >= maxLevels) {
                break;
            }

            Money price = priceLevel.getKey();
            List<Order> ordersAtPrice = priceLevel.getValue();

            BigDecimal totalQuantity = ordersAtPrice.stream()
                    .map(order -> order.getRemainingQuantity().getAmount())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            int orderCount = ordersAtPrice.size();

            if (totalQuantity.compareTo(BigDecimal.ZERO) > 0) {
                levels.add(new DepthLevel(price.getAmount(), totalQuantity, orderCount));
                count++;
            }
        }

        return levels;
    }

    @SuppressWarnings("java:S3776")
    private NavigableMap<Money, List<Order>> extractOrdersFromSnapshot(Object snapshot, OrderDirection direction) {
        NavigableMap<Money, List<Order>> orders = new TreeMap<>();

        try {
            if (snapshot == null) {
                return orders;
            }

            // Handle different snapshot types
            if (snapshot instanceof Map) {
                Map<String, Object> snapshotMap = (Map<String, Object>) snapshot;

                // Extract orders based on direction
                String directionKey = direction == OrderDirection.BUY ? "buyOrders" : "sellOrders";
                Object ordersData = snapshotMap.get(directionKey);

                if (ordersData instanceof Map) {
                    Map<String, Object> ordersMap = (Map<String, Object>) ordersData;

                    for (Map.Entry<String, Object> priceEntry : ordersMap.entrySet()) {
                        BigDecimal price = new BigDecimal(priceEntry.getKey());
                        Money priceKey = new Money(price);

                        List<Order> ordersAtPrice = extractOrdersAtPrice(priceEntry.getValue());
                        if (!ordersAtPrice.isEmpty()) {
                            orders.put(priceKey, ordersAtPrice);
                        }
                    }
                }
            }
            // Handle OrderBookSnapshot object directly
            else if (snapshot.getClass().getSimpleName().contains("OrderBookSnapshot")) {
                orders = extractFromOrderBookSnapshot(snapshot, direction);
            }
            // Handle List of orders
            else if (snapshot instanceof List) {
                List<?> ordersList = (List<?>) snapshot;
                orders = groupOrdersByPrice(ordersList, direction);
            }

            log.info("Extracted {} price levels for direction {} from snapshot", orders.size(), direction);

        } catch (Exception e) {
            log.error("Error extracting orders from snapshot for direction {}", direction, e);
        }

        return orders;
    }

    /**
     * Extract orders at specific price level
     */
    private List<Order> extractOrdersAtPrice(Object ordersData) {
        List<Order> orders = new ArrayList<>();

        try {
            if (ordersData instanceof List) {
                List<?> ordersList = (List<?>) ordersData;

                for (Object orderData : ordersList) {
                    Order order = convertToOrder(orderData);
                    if (order != null) {
                        orders.add(order);
                    }
                }
            } else if (ordersData instanceof Map) {
                // Single order as map
                Order order = convertToOrder(ordersData);
                if (order != null) {
                    orders.add(order);
                }
            }
        } catch (Exception e) {
            log.warn("Error extracting orders at price level", e);
        }

        return orders;
    }

    /**
     * Extract from OrderBookSnapshot object using reflection
     */
    private NavigableMap<Money, List<Order>> extractFromOrderBookSnapshot(Object snapshot, OrderDirection direction) {
        NavigableMap<Money, List<Order>> orders = new TreeMap<>();

        try {
            // Use reflection to access snapshot fields
            Class<?> snapshotClass = snapshot.getClass();
            String fieldName = direction == OrderDirection.BUY ? "buyOrders" : "sellOrders";

            java.lang.reflect.Field field = snapshotClass.getDeclaredField(fieldName);
            Object ordersData = field.get(snapshot);

            if (ordersData instanceof NavigableMap) {
                NavigableMap<?, ?> ordersMap = (NavigableMap<?, ?>) ordersData;

                for (Map.Entry<?, ?> entry : ordersMap.entrySet()) {
                    if (entry.getKey() instanceof Money && entry.getValue() instanceof List) {
                        Money price = (Money) entry.getKey();
                        List<?> ordersList = (List<?>) entry.getValue();

                        List<Order> typedOrders = ordersList.stream()
                                .filter(Order.class::isInstance)
                                .map(Order.class::cast)
                                .toList();

                        if (!typedOrders.isEmpty()) {
                            orders.put(price, typedOrders);
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.warn("Error extracting from OrderBookSnapshot using reflection", e);
        }

        return orders;
    }

    /**
     * Group orders by price from a list
     */
    private NavigableMap<Money, List<Order>> groupOrdersByPrice(List<?> ordersList, OrderDirection direction) {
        NavigableMap<Money, List<Order>> orders = new TreeMap<>();

        for (Object orderData : ordersList) {
            try {
                Order order = convertToOrder(orderData);
                if (order != null && order.getDirection() == direction) {
                    Money price = order.getPrice();
                    orders.computeIfAbsent(price, k -> new ArrayList<>()).add(order);
                }
            } catch (Exception e) {
                log.warn("Error converting order data to Order object", e);
            }
        }

        return orders;
    }

    /**
     * Convert generic order data to Order object
     */
    private Order convertToOrder(Object orderData) {
        try {
            if (orderData instanceof Order order) {
                return order;
            }

            if (orderData instanceof Map<?, ?> rawOrderMap) {
                // Safe cast with type checking
                Map<String, Object> orderMap = new HashMap<>();
                for (Map.Entry<?, ?> entry : rawOrderMap.entrySet()) {
                    if (entry.getKey() instanceof String) {
                        orderMap.put((String) entry.getKey(), entry.getValue());
                    }
                }

                // Extract basic order information
                String orderId = String.valueOf(orderMap.get("orderId"));
                BigDecimal price = new BigDecimal(String.valueOf(orderMap.get("price")));
                BigDecimal quantity = new BigDecimal(String.valueOf(orderMap.get("quantity")));
                BigDecimal filledQuantity = orderMap.containsKey("filledQuantity") ?
                        new BigDecimal(String.valueOf(orderMap.get("filledQuantity"))) : BigDecimal.ZERO;
                String direction = String.valueOf(orderMap.get("direction"));

                // Create simplified Order object for TradePlate purposes
                return Order.builder()
                        .orderId(com.icetea.lotus.matching.domain.valueobject.OrderId.of(orderId))
                        .price(new Money(price))
                        .size(new Money(quantity))
                        .filledSize(new Money(filledQuantity))
                        .direction(OrderDirection.valueOf(direction.toUpperCase()))
                        .build();
            }

        } catch (Exception e) {
            log.warn("Error converting order data to Order object", e);
        }

        return null;
    }

    private Object createEmptyTradePlate(Symbol symbol, OrderDirection direction) {
        TradePlate tradePlate = new TradePlate(symbol.getValue(), mapDirection(direction));
        tradePlate.setItems(new ArrayList<>());
        return tradePlate;
    }

    private String mapDirection(OrderDirection direction) {
        return direction == OrderDirection.BUY ? "BUY" : "SELL";
    }

    /**
     * TradePlate class for backward compatibility
     */
    public static class TradePlate {
        private String symbol;
        private String direction;
        private List<TradePlateItem> items;

        public TradePlate(String symbol, String direction) {
            this.symbol = symbol;
            this.direction = direction;
            this.items = new ArrayList<>();
        }

        // Getters and setters
        public String getSymbol() {
            return symbol;
        }

        public void setSymbol(String symbol) {
            this.symbol = symbol;
        }

        public String getDirection() {
            return direction;
        }

        public void setDirection(String direction) {
            this.direction = direction;
        }

        public List<TradePlateItem> getItems() {
            return items;
        }

        public void setItems(List<TradePlateItem> items) {
            this.items = items;
        }


    }

    /**
     * TradePlateItem representing a price level
     */
    public static class TradePlateItem {
        private BigDecimal price;
        private BigDecimal amount;

        public TradePlateItem(BigDecimal price, BigDecimal amount) {
            this.price = price;
            this.amount = amount;
        }

        // Getters and setters
        public BigDecimal getPrice() {
            return price;
        }

        public void setPrice(BigDecimal price) {
            this.price = price;
        }

        public BigDecimal getAmount() {
            return amount;
        }

        public void setAmount(BigDecimal amount) {
            this.amount = amount;
        }

        @Override
        public String toString() {
            return String.format("{\"price\":%s,\"amount\":%s}", price, amount);
        }
    }

    /**
     * Market depth representation
     */
    public static class MarketDepth {
        private String symbol;
        private List<DepthLevel> bids;
        private List<DepthLevel> asks;

        public MarketDepth(String symbol, List<DepthLevel> bids, List<DepthLevel> asks) {
            this.symbol = symbol;
            this.bids = bids;
            this.asks = asks;
        }

        // Getters
        public String getSymbol() {
            return symbol;
        }

        public List<DepthLevel> getBids() {
            return bids;
        }

        public List<DepthLevel> getAsks() {
            return asks;
        }
    }

    /**
     * Depth level with aggregated information
     */
    @Getter
    public static class DepthLevel {
        private BigDecimal price;
        private BigDecimal quantity;
        private int orderCount;

        public DepthLevel(BigDecimal price, BigDecimal quantity, int orderCount) {
            this.price = price;
            this.quantity = quantity;
            this.orderCount = orderCount;
        }
    }
}
