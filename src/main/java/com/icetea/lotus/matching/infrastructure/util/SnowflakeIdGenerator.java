package com.icetea.lotus.matching.infrastructure.util;

/**
 * <AUTHOR> nguyen
 */

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.atomic.AtomicLong;

/**
 * Snowflake ID Generator
 * Migrated from future-core với simplified implementation
 */
@Slf4j
public class SnowflakeIdGenerator {

    // Snowflake algorithm constants
    private static final long EPOCH = 1609459200000L; // 2021-01-01 00:00:00 UTC
    private static final long WORKER_ID_BITS = 5L;
    private static final long DATACENTER_ID_BITS = 5L;
    private static final long SEQUENCE_BITS = 12L;

    private static final long MAX_WORKER_ID = ~(-1L << WORKER_ID_BITS);
    private static final long MAX_DATACENTER_ID = ~(-1L << DATACENTER_ID_BITS);
    private static final long MAX_SEQUENCE = ~(-1L << SEQUENCE_BITS);

    private static final long WORKER_ID_SHIFT = SEQUENCE_BITS;
    private static final long DATACENTER_ID_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS;
    private static final long TIMESTAMP_LEFT_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS + DATACENTER_ID_BITS;

    // Instance configuration
    private final long workerId;
    private final long datacenterId;

    // State
    private final AtomicLong lastTimestamp = new AtomicLong(-1L);
    private final AtomicLong sequence = new AtomicLong(0L);

    // Performance counters
    private final AtomicLong generatedIds = new AtomicLong(0);
    private final AtomicLong clockBackwards = new AtomicLong(0);

    /**
     * Constructor với default worker và datacenter ID
     */
    public SnowflakeIdGenerator() {
        this(1L, 1L);
    }

    /**
     * Constructor
     * @param workerId Worker ID (0-31)
     * @param datacenterId Datacenter ID (0-31)
     */
    public SnowflakeIdGenerator(long workerId, long datacenterId) {
        if (workerId > MAX_WORKER_ID || workerId < 0) {
            throw new IllegalArgumentException(
                    String.format("Worker ID can't be greater than %d or less than 0", MAX_WORKER_ID));
        }
        if (datacenterId > MAX_DATACENTER_ID || datacenterId < 0) {
            throw new IllegalArgumentException(
                    String.format("Datacenter ID can't be greater than %d or less than 0", MAX_DATACENTER_ID));
        }

        this.workerId = workerId;
        this.datacenterId = datacenterId;

        log.info("Initialized SnowflakeIdGenerator with workerId={}, datacenterId={}", workerId, datacenterId);
    }

    /**
     * Generate next unique ID
     * @return Unique ID
     */
    @SuppressWarnings("java:S112")
    public synchronized long nextId() {
        long timestamp = timeGen();

        // Check for clock backwards
        long lastTs = lastTimestamp.get();
        if (timestamp < lastTs) {
            clockBackwards.incrementAndGet();
            log.warn("Clock moved backwards. Refusing to generate id for {} milliseconds", lastTs - timestamp);
            throw new RuntimeException(
                    String.format("Clock moved backwards. Refusing to generate id for %d milliseconds", 
                            lastTs - timestamp));
        }

        // Same millisecond
        if (lastTs == timestamp) {
            long seq = sequence.incrementAndGet() & MAX_SEQUENCE;
            if (seq == 0) {
                // Sequence exhausted, wait for next millisecond
                timestamp = tilNextMillis(lastTs);
                sequence.set(0);
            }
        } else {
            // New millisecond, reset sequence
            sequence.set(0);
        }

        lastTimestamp.set(timestamp);
        generatedIds.incrementAndGet();

        // Generate ID
        return ((timestamp - EPOCH) << TIMESTAMP_LEFT_SHIFT) |
               (datacenterId << DATACENTER_ID_SHIFT) |
               (workerId << WORKER_ID_SHIFT) |
               sequence.get();
    }

    /**
     * Generate next ID as string
     * @return Unique ID string
     */
    public String nextIdAsString() {
        return String.valueOf(nextId());
    }

    /**
     * Generate trade ID
     * @return Trade ID
     */
    public String generateTradeId() {
        return "T" + nextId();
    }

    /**
     * Generate order ID
     * @return Order ID
     */
    public String generateOrderId() {
        return "O" + nextId();
    }

    /**
     * Wait until next millisecond
     * @param lastTimestamp Last timestamp
     * @return Next timestamp
     */
    private long tilNextMillis(long lastTimestamp) {
        long timestamp = timeGen();
        while (timestamp <= lastTimestamp) {
            timestamp = timeGen();
        }
        return timestamp;
    }

    /**
     * Get current timestamp
     * @return Current timestamp
     */
    private long timeGen() {
        return System.currentTimeMillis();
    }

    /**
     * Parse timestamp from ID
     * @param id Snowflake ID
     * @return Timestamp
     */
    public long parseTimestamp(long id) {
        return (id >> TIMESTAMP_LEFT_SHIFT) + EPOCH;
    }

    /**
     * Parse worker ID from ID
     * @param id Snowflake ID
     * @return Worker ID
     */
    public long parseWorkerId(long id) {
        return (id >> WORKER_ID_SHIFT) & MAX_WORKER_ID;
    }

    /**
     * Parse datacenter ID from ID
     * @param id Snowflake ID
     * @return Datacenter ID
     */
    public long parseDatacenterId(long id) {
        return (id >> DATACENTER_ID_SHIFT) & MAX_DATACENTER_ID;
    }

    /**
     * Parse sequence from ID
     * @param id Snowflake ID
     * @return Sequence
     */
    public long parseSequence(long id) {
        return id & MAX_SEQUENCE;
    }

    /**
     * Get performance statistics
     * @return Performance stats
     */
    public SnowflakeStats getStats() {
        return SnowflakeStats.builder()
                .workerId(workerId)
                .datacenterId(datacenterId)
                .generatedIds(generatedIds.get())
                .clockBackwards(clockBackwards.get())
                .currentSequence(sequence.get())
                .lastTimestamp(lastTimestamp.get())
                .build();
    }



    /**
     * Snowflake statistics
     */
    @lombok.Builder
    @lombok.Data
    public static class SnowflakeStats {
        private long workerId;
        private long datacenterId;
        private long generatedIds;
        private long clockBackwards;
        private long currentSequence;
        private long lastTimestamp;
    }

    @Override
    public String toString() {
        return String.format("SnowflakeIdGenerator{worker=%d, datacenter=%d, generated=%d}", 
                workerId, datacenterId, generatedIds.get());
    }
}
