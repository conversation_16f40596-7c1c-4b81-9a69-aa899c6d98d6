package com.icetea.lotus.matching.infrastructure.config;

import com.icetea.lotus.matching.infrastructure.integration.MatchingEngineIntegrationService;
import com.icetea.lotus.matching.infrastructure.sharding.SymbolShardingManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * Tự động khởi tạo các symbols phổ biến khi ứng dụng startup
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DefaultSymbolInitializer {

    private final SymbolShardingManager shardingManager;
    private final MatchingEngineIntegrationService integrationService;

    @Value("${matching-engine.auto-init-symbols:true}")
    private boolean autoInitSymbols;

    @Value("${matching-engine.default-symbols:BTC/USDT,ETH/USDT,BNB/USDT,ADA/USDT,XRP/USDT,SOL/USDT,DOT/USDT,DOGE/USDT,AVAX/USDT,MATIC/USDT}")
    private String defaultSymbolsConfig;

    /**
     * Khởi tạo symbols mặc định sau khi ứng dụng đã sẵn sàng
     */
    @EventListener(ApplicationReadyEvent.class)
    public void initializeDefaultSymbols() {
        if (!autoInitSymbols) {
            log.info("Auto-init symbols is disabled, skipping default symbol initialization");
            return;
        }

        try {
            log.info("======Starting default symbols initialization======");

            List<String> defaultSymbols = parseDefaultSymbols();
            log.info("Default symbols to initialize: {}", defaultSymbols);

            int successCount = 0;
            int skipCount = 0;
            int failCount = 0;

            for (String symbol : defaultSymbols) {
                // Kiểm tra xem symbol đã được sở hữu chưa
                if (shardingManager.isSymbolOwnedByThisPod(symbol)) {
                    log.info("Symbol {} already owned by this pod, skipping", symbol);
                    skipCount++;
                    continue;
                }

                // Thử claim symbol
                boolean claimed = shardingManager.claimSymbol(symbol);

                if (claimed) {
                    // Khởi tạo matching engine cho symbol
                    integrationService.initializeMatchingEngine(symbol);
                    successCount++;
                    log.info("Successfully initialized default symbol: {}", symbol);
                } else {
                    log.info("Symbol {} is owned by another pod, skipping", symbol);
                    skipCount++;
                }
            }

            log.info("======Default symbols initialization completed======");
            log.info("Results: {} success, {} skipped, {} failed out of {} total symbols",
                    successCount, skipCount, failCount, defaultSymbols.size());

        } catch (Exception e) {
            log.error("Error during default symbols initialization", e);
        }
    }

    /**
     * Parse default symbols từ configuration
     */
    private List<String> parseDefaultSymbols() {
        if (defaultSymbolsConfig == null || defaultSymbolsConfig.trim().isEmpty()) {
            return Arrays.asList(
                    "BTC/USDT", "ETH/USDT", "BNB/USDT", "ADA/USDT", "XRP/USDT",
                    "SOL/USDT", "DOT/USDT", "DOGE/USDT", "AVAX/USDT", "MATIC/USDT",
                    "LINK/USDT", "UNI/USDT", "LTC/USDT", "ATOM/USDT", "NEAR/USDT"
            );
        }

        return Arrays.asList(defaultSymbolsConfig.split(","))
                .stream()
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .toList();
    }

}
