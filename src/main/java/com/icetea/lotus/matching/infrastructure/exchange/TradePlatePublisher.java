package com.icetea.lotus.matching.infrastructure.exchange;

import com.icetea.lotus.matching.domain.entity.ExchangeOrder;
import com.icetea.lotus.matching.domain.entity.Order;
import com.icetea.lotus.matching.domain.enums.SpotOrderDirection;
import com.icetea.lotus.matching.domain.valueobject.Symbol;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

import static com.icetea.lotus.matching.infrastructure.constants.CommonConstance.AMOUNT;

/**
 * TradePlate Publisher - Copy từ Exchange module
 * Handle trade plate publishing theo đúng Exchange.sendTradePlateMessage() logic
 * Real-time order book broadcasting system
 *
 * <AUTHOR> nguyen
 */
@Service
@RequiredArgsConstructor
public class TradePlatePublisher {

    private static final Logger logger = LoggerFactory.getLogger(TradePlatePublisher.class);

    private final KafkaTemplate<String, String> kafkaTemplate;

    private final TradePlateConverter tradePlateConverter;

    @Value("${topic-kafka.exchange.trade-plate:exchange-trade-plate}")
    private String exchangeTradePlateTopic;

    // Symbol -> TradePlate mapping
    private final Map<String, TradePlate> buyTradePlates = new ConcurrentHashMap<>();
    private final Map<String, TradePlate> sellTradePlates = new ConcurrentHashMap<>();

    // Async executor for message publishing
    private final Executor messageExecutor = Executors.newFixedThreadPool(4);

    /**
     * Send trade plate message - đồng bộ với CoinTraderV2.sendTradePlateMessage()
     */
    public void sendTradePlateMessage(TradePlate plate) {
        if (plate == null || plate.getSymbol() == null) {
            logger.warn("  Cannot send trade plate message: plate or symbol is null");
            return;
        }

        logger.info(" Attempting to send trade plate message for symbol: {}, direction: {}, topic: {}",
                plate.getSymbol(), plate.getDirection(), exchangeTradePlateTopic);

        // Async publishing to prevent blocking
        messageExecutor.execute(() -> {
            try {
                // Validate trade plate before conversion
                if (!tradePlateConverter.isValidTradePlate(plate)) {
                    logger.warn("  Invalid trade plate, skipping send: symbol={}, direction={}",
                            plate.getSymbol(), plate.getDirection());
                    return;
                }

                // Prevent concurrency issues - copy từ Exchange logic
                synchronized (plate.getItems()) {
                    // CRITICAL FIX: Cleanup completed orders before sending TradePlate - đồng bộ với Exchange
                    plate.cleanupCompletedOrders();

                    //   CRITICAL FIX: Convert to market module format
                    String plateJson = tradePlateConverter.convertToMarketFormat(plate);

                    logger.info(" Sending trade plate to Kafka: topic={}, symbol={}, direction={}, json_length={}",
                            exchangeTradePlateTopic, plate.getSymbol(), plate.getDirection(), plateJson.length());

                    // Log conversion details for debugging
                    tradePlateConverter.logConversionDetails(plate, plateJson);

                    kafkaTemplate.send(exchangeTradePlateTopic, plateJson);

                    logger.info("  Successfully published trade plate for symbol {}, direction {}, depth {}, topic {}",
                            plate.getSymbol(), plate.getDirection(), plate.getDepth(), exchangeTradePlateTopic);
                }
            } catch (Exception e) {
                logger.error("  Failed to send trade plate message for symbol {}: {}",
                        plate.getSymbol(), e.getMessage(), e);
            }
        });
    }

    /**
     * Get current trade plate for symbol (generic method)
     */
    private TradePlate getCurrentTradePlate(String symbol) {
        // Return buy trade plate by default, or create empty one
        return buyTradePlates.getOrDefault(symbol, createEmptyTradePlate(symbol));
    }

    /**
     * Create empty trade plate for symbol
     */
    private TradePlate createEmptyTradePlate(String symbol) {
        return new TradePlate(symbol, SpotOrderDirection.BUY);
    }

    /**
     * Update trade plate after trade execution
     */
    public void updateTradePlateAfterTrade(ExchangeTrade trade) {
        try {
            String symbol = trade.getSymbol();

            // Update both buy and sell trade plates
            updateTradePlateForTradeExecution(symbol, SpotOrderDirection.BUY, trade);
            updateTradePlateForTradeExecution(symbol, SpotOrderDirection.SELL, trade);

        } catch (Exception e) {
            logger.error("Failed to update trade plate after trade: {}", e.getMessage(), e);
        }
    }

    /**
     * Update trade plate for trade execution
     */
    private void updateTradePlateForTradeExecution(String symbol, SpotOrderDirection direction, ExchangeTrade trade) {
        TradePlate tradePlate = getTradePlate(symbol, direction);

        // Find and update the price level
        synchronized (tradePlate.getItems()) {
            for (TradePlateItem item : tradePlate.getItems()) {
                if (item.getPrice().compareTo(trade.getPrice()) == 0) {
                    // Reduce amount at this price level
                    BigDecimal newAmount = item.getAmount().subtract(trade.getAmount());
                    if (newAmount.compareTo(BigDecimal.ZERO) <= 0) {
                        tradePlate.getItems().remove(item);
                    } else {
                        item.setAmount(newAmount);
                    }
                    break;
                }
            }
        }

        // KHÔNG tự động gửi trade plate message để tránh duplicate
        // ExchangeCompatibilityService sẽ gửi khi cần thiết
    }

    /**
     * Get or create trade plate for symbol and direction
     * CRITICAL FIX: Auto-rebuild from order book if trade plate is empty
     */
    public TradePlate getTradePlate(String symbol, SpotOrderDirection direction) {
        Map<String, TradePlate> tradePlateMap =
                (direction == SpotOrderDirection.BUY) ? buyTradePlates : sellTradePlates;

        return tradePlateMap.computeIfAbsent(symbol, s -> new TradePlate(s, direction));
    }

    /**
     * Get current trade plate for symbol and direction
     */
    public TradePlate getCurrentTradePlate(String symbol, SpotOrderDirection direction) {
        return getTradePlate(symbol, direction);
    }

    /**
     * Get both buy and sell trade plates for symbol
     */
    public TradePlate[] getTradePlatePair(String symbol) {
        TradePlate buyPlate = getTradePlate(symbol, SpotOrderDirection.BUY);
        TradePlate sellPlate = getTradePlate(symbol, SpotOrderDirection.SELL);
        return new TradePlate[]{buyPlate, sellPlate};
    }

    /**
     *   CRITICAL FIX: Ensure both BUY and SELL trade plates exist for symbol
     * This method guarantees that both trade plates are initialized in their respective maps
     */
    public void ensureBothTradePlatesExist(String symbol) {
        // Initialize both trade plates to ensure they exist in the maps
        TradePlate buyPlate = getTradePlate(symbol, SpotOrderDirection.BUY);
        TradePlate sellPlate = getTradePlate(symbol, SpotOrderDirection.SELL);

        logger.info("Ensured both trade plates exist for symbol: {} (BUY: {}, SELL: {})",
                symbol, buyPlate != null, sellPlate != null);
    }

    /**
     * Rebuild trade plate from order book data (public method for external services)
     * CRITICAL FIX: Allow ExchangeCompatibilityService to rebuild trade plates
     */
    @SuppressWarnings("java:S3776")
    public void rebuildTradePlateFromOrderBookData(String symbol, SpotOrderDirection direction, Object orderBookData) {
        try {
            logger.info("Rebuilding trade plate from order book data for symbol: {}, direction: {}", symbol, direction);

            TradePlate tradePlate = getTradePlate(symbol, direction);
            tradePlate.clear();

            if (orderBookData instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> orderBook = (Map<String, Object>) orderBookData;

                logger.info(" DEBUG: Order book structure - keys: {}", orderBook.keySet());

                //   CRITICAL FIX: Handle both old and new order book structures
                Object ordersData = null;

                // Try new structure first (ExchangeMatchingEngine format)
                if (orderBook.containsKey("allOrders")) {
                    logger.info(" DEBUG: Using new allOrders structure");
                    Object allOrdersObj = orderBook.get("allOrders");

                    if (allOrdersObj instanceof List) {
                        @SuppressWarnings("unchecked")
                        List<Object> allOrders = (List<Object>) allOrdersObj;

                        logger.info(" DEBUG: Found {} orders in allOrders list", allOrders.size());

                        // Filter orders by direction and convert to trade plate
                        convertAllOrdersToTradePlate(tradePlate, allOrders, direction);
                    }
                } else {
                    // Try old structure (buyOrders/sellOrders format)
                    logger.info(" DEBUG: Using old buyOrders/sellOrders structure");

                    if (direction == SpotOrderDirection.BUY) {
                        ordersData = orderBook.get("buyOrders");
                    } else {
                        ordersData = orderBook.get("sellOrders");
                    }

                    if (ordersData instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> ordersMap = (Map<String, Object>) ordersData;

                        // Convert order book data to trade plate items
                        convertOrderBookDataToTradePlate(tradePlate, ordersMap);
                    }
                }
            }

            logger.info("Successfully rebuilt trade plate for symbol: {}, direction: {}, items: {}",
                    symbol, direction, tradePlate.getDepth());

            //   CRITICAL FIX: Send trade plate message after rebuild
            if (tradePlate.getDepth() > 0) {
                sendTradePlateMessage(tradePlate);
                logger.info("Sent trade plate message after rebuild for symbol: {}, direction: {}", symbol, direction);
            } else {
                logger.info("Trade plate is empty after rebuild, not sending message for symbol: {}, direction: {}", symbol, direction);
            }

        } catch (Exception e) {
            logger.error("Failed to rebuild trade plate from order book data for symbol: {}, direction: {}",
                    symbol, direction, e);
        }
    }

    /**
     *   CRITICAL FIX: Convert allOrders list to trade plate (new ExchangeMatchingEngine format)
     * Handle the new structure: {allOrders: [ExchangeOrderDTO, ExchangeOrderDTO, ...]}
     */
    private void convertAllOrdersToTradePlate(TradePlate tradePlate, List<Object> allOrders, SpotOrderDirection direction) {
        try {
            logger.info("Converting allOrders list to trade plate using standard add logic for symbol: {}, direction: {}, total orders: {}",
                    tradePlate.getSymbol(), tradePlate.getDirection(), allOrders.size());

            int processedCount = 0;
            int addedCount = 0;

            for (Object orderObj : allOrders) {
                processedCount++;

                // Convert order object to ExchangeOrder
                ExchangeOrder exchangeOrder = convertOrderObjectToExchangeOrder(orderObj);

                if (exchangeOrder != null) {
                    // Only add orders that match the requested direction
                    if (exchangeOrder.getDirection() == direction) {
                        //   REUSE: Use the same add logic as processing new orders
                        boolean added = tradePlate.add(exchangeOrder);
                        if (added) {
                            addedCount++;
                            logger.info("Added order to trade plate: orderId={}, price={}, amount={}, direction={}",
                                    exchangeOrder.getOrderId(), exchangeOrder.getPrice(), exchangeOrder.getAmount(), exchangeOrder.getDirection());
                        } else {
                            logger.warn("Failed to add order to trade plate: orderId={}, price={}, amount={}, direction={}",
                                    exchangeOrder.getOrderId(), exchangeOrder.getPrice(), exchangeOrder.getAmount(), exchangeOrder.getDirection());
                        }
                    } else {
                        logger.info("Skipping order with different direction: orderId={}, orderDirection={}, requestedDirection={}",
                                exchangeOrder.getOrderId(), exchangeOrder.getDirection(), direction);
                    }
                } else {
                    logger.warn("Failed to convert order object to ExchangeOrder: {}", orderObj);
                }
            }

            logger.info("Completed converting allOrders to trade plate: symbol={}, direction={}, processed={}, added={}, final items count={}",
                    tradePlate.getSymbol(), tradePlate.getDirection(), processedCount, addedCount, tradePlate.getItems().size());

        } catch (Exception e) {
            logger.error("Error converting allOrders list to trade plate", e);
        }
    }

    /**
     *   CRITICAL FIX: Convert order object to ExchangeOrder (handle ExchangeOrderDTO format)
     */
    private ExchangeOrder convertOrderObjectToExchangeOrder(Object orderObj) {
        try {
            if (orderObj == null) {
                return null;
            }

            logger.info(" DEBUG: Converting order object type: {}", orderObj.getClass().getSimpleName());

            // Handle ExchangeOrderDTO (from ExchangeMatchingEngine)
            if (orderObj.toString().contains("ExchangeOrderDTO")) {
                return parseExchangeOrderDTO(orderObj.toString());
            }

            // Handle Map format (legacy)
            if (orderObj instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> orderMap = (Map<String, Object>) orderObj;
                return convertMapToExchangeOrder(orderMap);
            }

            logger.warn(" DEBUG: Unknown order object format: {}", orderObj.getClass().getSimpleName());
            return null;

        } catch (Exception e) {
            logger.error(" DEBUG: Error converting order object to ExchangeOrder", e);
            return null;
        }
    }

    /**
     *   CRITICAL FIX: Parse ExchangeOrderDTO string format
     * Format: ExchangeOrderDTO[id=E175332891142877, symbol=EOS/ETH, type=LIMIT_PRICE, direction=BUY, status=TRADING, price=123.00000000, amount=1.00000000, tradedAmount=null]
     */
    private ExchangeOrder parseExchangeOrderDTO(String orderStr) {
        try {
            // Extract values using regex or string parsing
            String orderId = extractValue(orderStr, "id=", ",");
            String symbol = extractValue(orderStr, "symbol=", ",");
            String type = extractValue(orderStr, "type=", ",");
            String direction = extractValue(orderStr, "direction=", ",");
            String price = extractValue(orderStr, "price=", ",");
            String amount = extractValue(orderStr, "amount=", ",");
            String tradedAmount = extractValue(orderStr, "tradedAmount=", "]");

            if (orderId == null || symbol == null || direction == null || price == null || amount == null) {
                logger.warn(" DEBUG: Missing required fields in ExchangeOrderDTO: orderId={}, symbol={}, direction={}, price={}, amount={}",
                        orderId, symbol, direction, price, amount);
                return null;
            }

            // Create ExchangeOrder
            ExchangeOrder exchangeOrder = new ExchangeOrder();
            exchangeOrder.setOrderId(orderId);
            exchangeOrder.setSymbol(symbol);
            exchangeOrder.setPrice(new java.math.BigDecimal(price));
            exchangeOrder.setAmount(new java.math.BigDecimal(amount));

            // Set direction
            if ("BUY".equals(direction)) {
                exchangeOrder.setDirection(SpotOrderDirection.BUY);
            } else if ("SELL".equals(direction)) {
                exchangeOrder.setDirection(SpotOrderDirection.SELL);
            }

            // Set type
            if ("LIMIT_PRICE".equals(type)) {
                exchangeOrder.setType(com.icetea.lotus.matching.domain.enums.SpotOrderType.LIMIT_PRICE);
            } else if ("MARKET_PRICE".equals(type)) {
                exchangeOrder.setType(com.icetea.lotus.matching.domain.enums.SpotOrderType.MARKET_PRICE);
            }

            // Set traded amount
            if (tradedAmount != null && !"null".equals(tradedAmount)) {
                exchangeOrder.setTradedAmount(new java.math.BigDecimal(tradedAmount));
            } else {
                exchangeOrder.setTradedAmount(java.math.BigDecimal.ZERO);
            }

            logger.info(" DEBUG: Successfully parsed ExchangeOrderDTO: orderId={}, symbol={}, direction={}, price={}, amount={}",
                    exchangeOrder.getOrderId(), exchangeOrder.getSymbol(), exchangeOrder.getDirection(),
                    exchangeOrder.getPrice(), exchangeOrder.getAmount());

            return exchangeOrder;

        } catch (Exception e) {
            logger.error(" DEBUG: Error parsing ExchangeOrderDTO string", e);
            return null;
        }
    }

    /**
     * Helper method to extract value from string
     */
    private String extractValue(String str, String prefix, String suffix) {
        try {
            int startIndex = str.indexOf(prefix);
            if (startIndex == -1) {
                return null;
            }
            startIndex += prefix.length();

            int endIndex = str.indexOf(suffix, startIndex);
            if (endIndex == -1) {
                endIndex = str.length();
            }

            return str.substring(startIndex, endIndex).trim();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Convert Map format to ExchangeOrder (legacy support)
     */
    private ExchangeOrder convertMapToExchangeOrder(Map<String, Object> orderMap) {
        try {
            String orderId = extractStringValue(orderMap, "orderId");
            Object priceObj = orderMap.get("price");
            Object amountObj = orderMap.get(AMOUNT);
            Object directionObj = orderMap.get("direction");

            if (orderId == null || priceObj == null || amountObj == null || directionObj == null) {
                return null;
            }

            ExchangeOrder exchangeOrder = new ExchangeOrder();
            exchangeOrder.setOrderId(orderId);
            exchangeOrder.setPrice(new java.math.BigDecimal(priceObj.toString()));
            exchangeOrder.setAmount(new java.math.BigDecimal(amountObj.toString()));

            if ("BUY".equals(directionObj.toString())) {
                exchangeOrder.setDirection(SpotOrderDirection.BUY);
            } else if ("SELL".equals(directionObj.toString())) {
                exchangeOrder.setDirection(SpotOrderDirection.SELL);
            }

            exchangeOrder.setType(com.icetea.lotus.matching.domain.enums.SpotOrderType.LIMIT_PRICE);
            exchangeOrder.setTradedAmount(java.math.BigDecimal.ZERO);

            return exchangeOrder;

        } catch (Exception e) {
            logger.error("Error converting Map to ExchangeOrder", e);
            return null;
        }
    }

    /**
     * Convert order book data to trade plate items using standard add logic
     *   REFACTOR: Reuse the same logic as processing new orders
     */
    @SuppressWarnings("java:S3776")
    private void convertOrderBookDataToTradePlate(TradePlate tradePlate, Map<String, Object> ordersMap) {
        try {
            logger.info("Converting order book data to trade plate using standard add logic for symbol: {}, direction: {}",
                    tradePlate.getSymbol(), tradePlate.getDirection());

            for (Map.Entry<String, Object> entry : ordersMap.entrySet()) {
                String priceStr = entry.getKey();
                Object orderListObj = entry.getValue();

                if (orderListObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<Object> orderList = (List<Object>) orderListObj;

                    // Process each order in the price level
                    for (Object orderObj : orderList) {
                        ExchangeOrder exchangeOrder = convertToExchangeOrder(orderObj, priceStr, tradePlate.getDirection());
                        if (exchangeOrder != null) {
                            //   REUSE: Use the same add logic as processing new orders
                            boolean added = tradePlate.add(exchangeOrder);
                            if (added) {
                                logger.info("Added restored order to trade plate: orderId={}, price={}, amount={}",
                                        exchangeOrder.getOrderId(), exchangeOrder.getPrice(), exchangeOrder.getAmount());
                            } else {
                                logger.warn("Failed to add restored order to trade plate: orderId={}, price={}, amount={}",
                                        exchangeOrder.getOrderId(), exchangeOrder.getPrice(), exchangeOrder.getAmount());
                            }
                        }
                    }
                }
            }

            logger.info("Completed converting order book data to trade plate: symbol={}, direction={}, final items count={}",
                    tradePlate.getSymbol(), tradePlate.getDirection(), tradePlate.getItems().size());

        } catch (Exception e) {
            logger.error("Error converting order book data to trade plate", e);
        }
    }

    /**
     * Convert order object to ExchangeOrder for reusing standard add logic
     */
    private ExchangeOrder convertToExchangeOrder(Object orderObj, String priceStr, SpotOrderDirection direction) {
        try {
            if (!(orderObj instanceof Map)) {
                return null;
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> orderMap = (Map<String, Object>) orderObj;

            // Extract order data
            String orderId = extractStringValue(orderMap, "orderId");
            BigDecimal price = new BigDecimal(priceStr);
            BigDecimal amount = extractBigDecimalValue(orderMap, "amount");
            BigDecimal tradedAmount = extractBigDecimalValue(orderMap, "tradedAmount");

            if (orderId == null || amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
                logger.warn("Invalid order data during restore: orderId={}, amount={}", orderId, amount);
                return null;
            }

            // Create ExchangeOrder
            ExchangeOrder exchangeOrder = new ExchangeOrder();
            exchangeOrder.setOrderId(orderId);
            exchangeOrder.setPrice(price);
            exchangeOrder.setAmount(amount);
            exchangeOrder.setTradedAmount(tradedAmount != null ? tradedAmount : BigDecimal.ZERO);
            exchangeOrder.setDirection(direction);
            exchangeOrder.setType(com.icetea.lotus.matching.domain.enums.SpotOrderType.LIMIT_PRICE); // Default to LIMIT for restored orders

            logger.info("Converted order data to ExchangeOrder: orderId={}, price={}, amount={}, tradedAmount={}",
                    orderId, price, amount, exchangeOrder.getTradedAmount());

            return exchangeOrder;

        } catch (Exception e) {
            logger.error("Error converting order object to ExchangeOrder", e);
            return null;
        }
    }

    /**
     * Helper method to extract String value from map
     */
    private String extractStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * Helper method to extract BigDecimal value from map
     */
    private BigDecimal extractBigDecimalValue(Map<String, Object> map, String key) {
        try {
            Object value = map.get(key);
            if (value == null) {
                return null;
            }
            if (value instanceof BigDecimal bigDecimal) {
                return bigDecimal;
            }
            if (value instanceof Number) {
                return new BigDecimal(value.toString());
            }
            return new BigDecimal(value.toString());
        } catch (Exception e) {
            logger.warn("Error extracting BigDecimal value for key: {}, value: {}", key, map.get(key));
            return null;
        }
    }

    /**
     * Health check
     */
    public boolean isHealthy() {
        try {
            // Basic health check
            return kafkaTemplate != null;
        } catch (Exception e) {
            logger.error("Trade plate publisher health check failed", e);
            return false;
        }
    }

    /**
     * Update trade plate for completed order (generic object version)
     * Copy từ Exchange TradePlateService.updateTradePlateForOrderComplete()
     */
    public void updateTradePlateForOrderComplete(Object order) {
        try {
            logger.info("Updating trade plate for completed order: {}", order);

            // Convert to Order object if possible
            if (order instanceof Order orderInstance) {
                updateTradePlateForOrderComplete(orderInstance);
                return;
            }

            // Extract order information for generic objects
            String symbol = extractSymbolFromOrder(order);
            if (symbol == null) {
                logger.warn("Cannot extract symbol from order: {}", order);
                return;
            }

            // Get current trade plate
            TradePlate currentPlate = getCurrentTradePlate(symbol);
            if (currentPlate == null) {
                logger.info("No current trade plate found for symbol: {}, creating new one", symbol);
            }

            // KHÔNG tự động gửi trade plate message để tránh duplicate
            // ExchangeCompatibilityService sẽ gửi khi cần thiết

            logger.info("Successfully updated and published trade plate for symbol: {}", symbol);

        } catch (Exception e) {
            logger.error("Failed to update trade plate for completed order: {}", order, e);
        }
    }

    /**
     * Extract symbol from order object
     */
    private String extractSymbolFromOrder(Object order) {
        try {
            if (order == null) {
                return null;
            }

            // Handle Order object
            if (order instanceof Order orderObj) {
                return orderObj.getSymbol().getValue();
            }

            // Handle Map format
            if (order instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> orderMap = (Map<String, Object>) order;
                Object symbolObj = orderMap.get("symbol");
                return symbolObj != null ? symbolObj.toString() : null;
            }

            // Use reflection for other types
            Class<?> orderClass = order.getClass();
            Method getSymbol = orderClass.getMethod("getSymbol");
            Object symbolObj = getSymbol.invoke(order);
            if (symbolObj != null) {
                // Handle Symbol value object
                if (symbolObj instanceof Symbol) {
                    Method getValue = symbolObj.getClass().getMethod("getValue");
                    Object value = getValue.invoke(symbolObj);
                    return value != null ? value.toString() : null;
                }
                return symbolObj.toString();
            }
            return null;

        } catch (Exception e) {
            logger.warn("Error extracting symbol from order", e);
            return null;
        }
    }

}
