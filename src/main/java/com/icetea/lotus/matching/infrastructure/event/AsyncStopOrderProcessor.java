package com.icetea.lotus.matching.infrastructure.event;

import com.icetea.lotus.matching.domain.entity.ExchangeOrder;
import com.icetea.lotus.matching.domain.entity.Order;
import com.icetea.lotus.matching.infrastructure.futurecore.FutureCoreTradeResult;
import com.icetea.lotus.matching.infrastructure.integration.MatchingEngineIntegrationService;
import com.icetea.lotus.matching.infrastructure.exchange.ExchangeTradeResult;
import com.icetea.lotus.matching.infrastructure.service.MatchingEngineStopOrderManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Async Stop Order Processor for Matching Engine
 * <p>
 * Lắng nghe TradeExecutedEvent và xử lý stop orders một cách bất đồng bộ
 * để tránh làm chậm luồng matching chính.
 *
 * <AUTHOR> nguyen
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AsyncStopOrderProcessor {

    private final MatchingEngineStopOrderManager stopOrderManager;
    private final MatchingEngineIntegrationService integrationService;

    /**
     * Handle TradeExecutedEvent to trigger stop orders
     * Single handler for all events to avoid duplicate processing
     *
     * @param event TradeExecutedEvent from matching engine
     */
    @Async("stopOrderExecutor")
    @EventListener
    public void handleTradeExecuted(TradeExecutedEvent event) {
        // Null check
        if (event == null) {
            log.warn("Null TradeExecutedEvent received, ignoring");
            return;
        }

        log.info("AsyncStopOrderProcessor.handleTradeExecuted called for symbol: {}, price: {}, eventId: {}",
                event.getSymbol(), event.getPrice(), event.getEventId());

        try {
            //   CRITICAL FIX: Use simplified validation for stop order triggering
            // We only need symbol and price to trigger stop orders
            if (!event.isValidForStopOrderTrigger()) {
                log.warn("Invalid TradeExecutedEvent for stop order triggering: symbol={}, price={}",
                        event.getSymbol(), event.getPrice());
                return;
            }

            // Check if event is stale (older than 10 seconds)
            if (event.isStale(10000)) {
                log.warn("Dropping stale TradeExecutedEvent: {} (age: {}ms)",
                        event.getEventId(), event.getEventAge());
                return;
            }

            String symbol = event.getSymbol();
            BigDecimal currentPrice = event.getPrice();

            log.info("Processing stop orders for symbol: {}, current price: {}", symbol, currentPrice);

            // Check and trigger stop orders for this symbol and price
            List<Object> triggeredOrders = stopOrderManager.checkAndTriggerStopOrders(symbol, currentPrice, event.getSource());

            if (triggeredOrders != null && !triggeredOrders.isEmpty()) {
                if (triggeredOrders.get(0) instanceof ExchangeOrder) {
                    log.info("Found {} triggered stop orders for symbol: {}, price: {}",
                            triggeredOrders.size(), symbol, currentPrice);

                    List<ExchangeOrder> exchangeOrders = triggeredOrders.stream()
                            .map(o -> (ExchangeOrder) o)
                            .toList();

                    // Submit triggered orders with proper sequencing for matching
                    this.submitExchangeTriggeredOrdersWithMatching(exchangeOrders, symbol);

                } else if (triggeredOrders.get(0) instanceof Order) {
                    log.info("Found {} triggered future stop orders for symbol: {}, price: {}",
                            triggeredOrders.size(), symbol, currentPrice);

                    List<Order> orders = triggeredOrders.stream()
                            .map(o -> (Order) o)
                            .toList();

                    this.submitFutureTriggeredOrdersWithMatching(orders, symbol);
                }
            } else {
                log.info("No stop orders triggered for symbol: {}, price: {}", symbol, currentPrice);
            }

        } catch (Exception e) {
            log.error("Error processing stop orders for TradeExecutedEvent: {} - {}",
                    event.getEventId(), e.getMessage(), e);

            // Don't rethrow to avoid affecting other event listeners
        }
    }

    private void submitFutureTriggeredOrdersWithMatching(List<Order> orders, String symbol) {
        if (orders == null || orders.isEmpty()) {
            return;
        }
        List<Order> futureOrders = new ArrayList<>(orders);
        log.info("Submitting {} future triggered orders in FIFO sequence for symbol: {}",
                futureOrders.size(), symbol);

        // Submit orders in FIFO sequence
        for (Order order : futureOrders) {
            try {
                // Submit order
                this.integrationService.submitFutureTriggeredStopOrder(order);

            } catch (Exception e) {
                log.error("  Failed to submit triggered order: {} for symbol: {}: {}",
                        order.getOrderId(), symbol, e.getMessage(), e);
                // Continue with next order
            }
        }

        log.info("Completed submission of {} future triggered orders for symbol: {}",
                futureOrders.size(), symbol);

    }


    /**
     * Submit triggered orders with proper sequencing to ensure matching
     *
     * @param triggeredOrders List of triggered orders
     * @param symbol          Trading symbol
     */
    private void submitExchangeTriggeredOrdersWithMatching(List<ExchangeOrder> triggeredOrders, String symbol) {
        if (triggeredOrders == null || triggeredOrders.isEmpty()) {
            return;
        }
        List<ExchangeOrder> fifoOrders = new ArrayList<>(triggeredOrders);

        log.info("Submitting {} exchange triggered orders in FIFO sequence for symbol: {}",
                fifoOrders.size(), symbol);

        // Submit orders in FIFO sequence
        for (int i = 0; i < fifoOrders.size(); i++) {
            ExchangeOrder order = fifoOrders.get(i);
            try {
                // Submit order
                ExchangeTradeResult result = integrationService.submitExchangeTriggeredStopOrder(order);

                log.info("  Submitted triggered order {}/{}: {} {} price={} amount={} → Result: success={}, trades={}, message={}",
                        i + 1, fifoOrders.size(),
                        order.getDirection(), order.getType(),
                        order.getPrice(), order.getAmount(),
                        result.isSuccess(), result.getTradesCount(), result.getMessage());

            } catch (Exception e) {
                log.error("  Failed to submit triggered order: {} for symbol: {}: {}",
                        order.getOrderId(), symbol, e.getMessage(), e);
                // Continue with next order
            }
        }

        log.info("Completed FIFO submission of {} triggered orders for symbol: {}",
                fifoOrders.size(), symbol);
    }
}
