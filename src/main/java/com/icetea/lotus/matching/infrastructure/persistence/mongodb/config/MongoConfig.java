package com.icetea.lotus.matching.infrastructure.persistence.mongodb.config;

import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.config.AbstractMongoClientConfiguration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.IndexOperations;
import org.springframework.data.mongodb.core.index.IndexDefinition;
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

import jakarta.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;

/**
 * MongoDB Configuration cho Matching Engine
 * Copy từ future-core MongoConfig với TTL index setup
 *
 * <AUTHOR> nguyen
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
@EnableMongoRepositories(basePackages = "com.icetea.lotus.matching.infrastructure.persistence.mongodb.repository")
public class MongoConfig extends AbstractMongoClientConfiguration {

    @Value("${spring.data.mongodb.uri:mongodb://localhost:27017/matching_engine}")
    private String mongoUri;

    @Value("${spring.data.mongodb.database:matching_engine}")
    private String database;

    private final MongoCollectionNameResolver collectionNameResolver;

    @Value("${matching-engine.mongodb.indexes.auto-create:true}")
    private boolean autoCreateIndexes;

    @Override
    protected String getDatabaseName() {
        return database;
    }

    /**
     * Tạo MongoClient với custom settings
     */
    @Override
    @Bean
    public MongoClient mongoClient() {
        ConnectionString connectionString = new ConnectionString(mongoUri);
        MongoClientSettings mongoClientSettings = MongoClientSettings.builder()
                .applyConnectionString(connectionString)
                .build();
        return MongoClients.create(mongoClientSettings);
    }

    /**
     * Tạo MongoTemplate bean
     */
    @Bean
    public MongoTemplate mongoTemplate() {
        return new MongoTemplate(mongoClient(), getDatabaseName());
    }

    /**
     * Tự động tạo TTL index cho expires_at field
     * Thay thế cho @Indexed(expireAfterSeconds = 0) đã deprecated
     */
    @PostConstruct
    public void createTtlIndexes() {
        if (!autoCreateIndexes) {
            log.info("Auto-create indexes is disabled, skipping TTL index creation");
            return;
        }

        try {
            if (collectionNameResolver == null) {
                log.warn("MongoCollectionNameResolver not available, using default collection name");
                return;
            }

            MongoTemplate template = mongoTemplate();
            String collectionName = collectionNameResolver.getOrderBookSnapshotsCollection();

            log.info("Creating TTL index for collection: {}", collectionName);

            // Tạo TTL index cho expires_at field
            IndexOperations indexOps = template.indexOps(collectionName);

            // TTL index với expireAfterSeconds = 0 (sử dụng giá trị trong document)
            IndexDefinition ttlIndex = new Index()
                    .on("expires_at", org.springframework.data.domain.Sort.Direction.ASC)
                    .expire(0, TimeUnit.SECONDS);

            indexOps.ensureIndex(ttlIndex);

            log.info("Successfully created TTL index for collection: {}", collectionName);

        } catch (Exception e) {
            log.error("Error creating TTL indexes", e);
            // Không throw exception để không làm fail application startup
        }
    }
}
