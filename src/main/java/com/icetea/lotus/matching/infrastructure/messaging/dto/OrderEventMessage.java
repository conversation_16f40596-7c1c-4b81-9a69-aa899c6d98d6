package com.icetea.lotus.matching.infrastructure.messaging.dto;

import com.icetea.lotus.matching.domain.entity.Trade;
import com.icetea.lotus.matching.infrastructure.constants.OrderEventType;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Order Event Message DTO
 * Extracted từ ConsolidatedKafkaProducer và FutureCoreKafkaProducer
 * 
 * <AUTHOR> nguyen
 */
@Data
@Builder
public class OrderEventMessage {
    private String symbol;
    private OrderEventType eventType;
    private List<Trade> trades;
    private LocalDateTime timestamp;
    private String orderId;
}
