package com.icetea.lotus.matching.infrastructure.persistence.redis.document;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * Optimized OrderDocument for SPOT trading
 * Removed unnecessary fields for better performance and storage efficiency
 * 
 * <AUTHOR> nguyen
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SpotOrderDocument {
    
    //   CORE SPOT FIELDS - REQUIRED
    
    @JsonProperty("order_id")
    private String orderId;

    @JsonProperty("member_id")
    private Long memberId;

    @JsonProperty("direction")
    private String direction; // BUY, SELL

    @JsonProperty("type")
    private String type; // LIMIT_PRICE, MARKET_PRICE

    @JsonProperty("status")
    private String status; // NEW, PARTIAL_FILLED, FILLED, CANCELED

    @JsonProperty("price")
    private MoneyDocument price; // null for market orders

    @JsonProperty("quantity")
    private MoneyDocument quantity;

    @JsonProperty("filled_quantity")
    private MoneyDocument filledQuantity;

    @JsonProperty("turnover")
    private String turnover; // Total trade value

    @JsonProperty("created_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'", timezone = "UTC")
    private Instant createdTime;

    //   OPTIONAL SPOT FIELDS - For order lifecycle tracking
    
    @JsonProperty("updated_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'", timezone = "UTC")
    private Instant updatedTime;

    @JsonProperty("completed_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'", timezone = "UTC")
    private Instant completedTime;

    @JsonProperty("canceled_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'", timezone = "UTC")
    private Instant canceledTime;

    //   REMOVED FIELDS (Not needed for SPOT):
    // - fee: Handled separately in SPOT trading
    // - leverage: FUTURES only
    // - marginTrade: FUTURES only  
    // - stopPrice: Handled by separate stop order system
    // - triggerCondition: FUTURES only
    // - timeInForce: Not used in current SPOT implementation

    /**
     * Money Document for SPOT orders
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class MoneyDocument {
        @JsonProperty("amount")
        private String amount;

        @JsonProperty("currency")
        private String currency;

        public static MoneyDocument of(java.math.BigDecimal amount) {
            return MoneyDocument.builder()
                .amount(amount != null ? amount.toString() : "0")
                .currency("") // Currency handled at symbol level
                .build();
        }

        public static MoneyDocument of(String amount) {
            return MoneyDocument.builder()
                .amount(amount != null ? amount : "0")
                .currency("")
                .build();
        }
    }
}
