package com.icetea.lotus.matching.infrastructure.persistence.redis.util;

import com.icetea.lotus.matching.domain.entity.SimpleStopOrder;
import com.icetea.lotus.matching.domain.entity.Member;
import com.icetea.lotus.matching.domain.enums.OrderDirection;
import com.icetea.lotus.matching.domain.enums.StopOrderStatus;
import com.icetea.lotus.matching.domain.enums.StopOrderType;
import com.icetea.lotus.matching.domain.valueobject.Money;
import com.icetea.lotus.matching.domain.valueobject.OrderId;
import com.icetea.lotus.matching.infrastructure.persistence.redis.document.RedisSnapshotDocument;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Map;

/**
 * Utility class for converting between SimpleStopOrder and RedisSnapshotDocument.OrderDocument
 * Handles stop order specific fields like triggerPrice, executionPrice, stopOrderType
 *
 * <AUTHOR> nguyen
 */
@Slf4j
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class StopOrderConverter {

    /**
     * Convert SimpleStopOrder to RedisSnapshotDocument.OrderDocument
     */
    public static RedisSnapshotDocument.OrderDocument toDocument(SimpleStopOrder stopOrder) {
        if (stopOrder == null) {
            return null;
        }

        try {
            RedisSnapshotDocument.OrderDocument.OrderDocumentBuilder builder =
                    RedisSnapshotDocument.OrderDocument.builder();

            // Core fields
            if (stopOrder.getOrderId() != null) {
                builder.orderId(stopOrder.getOrderId().getValue());
            }

            if (stopOrder.getMember() != null) {
                builder.memberId(stopOrder.getMember().getId());
            }

            if (stopOrder.getDirection() != null) {
                builder.direction(stopOrder.getDirection().toString());
            }

            if (stopOrder.getStopOrderType() != null) {
                builder.type(stopOrder.getStopOrderType().toString());
            }

            if (stopOrder.getStatus() != null) {
                builder.status(stopOrder.getStatus().toString());
            }

            // Stop order specific fields
            // Use triggerPrice as stopPrice (proper field for stop orders)
            if (stopOrder.getTriggerPrice() != null) {
                builder.stopPrice(convertMoneyToDocument(stopOrder.getTriggerPrice()));
            }

            // Use executionPrice as the main price field
            if (stopOrder.getExecutionPrice() != null) {
                builder.price(convertMoneyToDocument(stopOrder.getExecutionPrice()));
            } else if (stopOrder.getTriggerPrice() != null) {
                // Fallback: use triggerPrice as price if no executionPrice
                builder.price(convertMoneyToDocument(stopOrder.getTriggerPrice()));
            }

            if (stopOrder.getQuantity() != null) {
                builder.quantity(convertMoneyToDocument(stopOrder.getQuantity()));
            }

            // Stop orders typically don't have filled quantity, set to zero
            builder.filledQuantity(RedisSnapshotDocument.MoneyDocument.builder()
                    .amount("0")
                    .currency("USD")
                    .scale(8)
                    .build());

            // Timestamps
            if (stopOrder.getTimestamp() != null) {
                builder.createdTime(Instant.ofEpochMilli(stopOrder.getTimestamp()));
            } else {
                builder.createdTime(Instant.now());
            }

            builder.updatedTime(Instant.now());

            return builder.build();

        } catch (Exception e) {
            log.error("Error converting SimpleStopOrder to OrderDocument: {}", stopOrder, e);
            return null;
        }
    }

    /**
     * Convert RedisSnapshotDocument.OrderDocument back to SimpleStopOrder
     * This is for restoration from snapshot
     */
    public static SimpleStopOrder fromDocument(RedisSnapshotDocument.OrderDocument document) {
        if (document == null) {
            return null;
        }

        try {
            SimpleStopOrder.SimpleStopOrderBuilder builder = SimpleStopOrder.builder();

            // Core fields
            if (document.getOrderId() != null) {
                builder.orderId(OrderId.of(document.getOrderId()));
            }

            if (document.getMemberId() != null) {
                builder.member(Member.builder()
                        .id(document.getMemberId())
                        .build());
            }

            if (document.getDirection() != null) {
                builder.direction(OrderDirection.valueOf(document.getDirection()));
            }

            if (document.getType() != null) {
                builder.stopOrderType(StopOrderType.valueOf(document.getType()));
            }

            if (document.getStatus() != null) {
                builder.status(StopOrderStatus.valueOf(document.getStatus()));
            }

            // Stop order specific fields
            // Restore triggerPrice from stopPrice field (MoneyDocument is guaranteed non-null)
            builder.triggerPrice(convertDocumentToMoney(document.getStopPrice()));

            // Restore executionPrice from price field (MoneyDocument is guaranteed non-null)
            builder.executionPrice(convertDocumentToMoney(document.getPrice()));

            // Restore quantity (MoneyDocument is guaranteed non-null)
            builder.quantity(convertDocumentToMoney(document.getQuantity()));

            // Timestamps
            if (document.getCreatedTime() != null) {
                builder.timestamp(document.getCreatedTime().toEpochMilli());
            } else {
                builder.timestamp(System.currentTimeMillis());
            }

            return builder.build();

        } catch (Exception e) {
            log.error("Error converting OrderDocument to SimpleStopOrder: {}", document, e);
            return null;
        }
    }

    /**
     * Convert Object (from snapshot) to stop order document
     */
    public static RedisSnapshotDocument.OrderDocument fromObject(Object stopOrderObj) {
        if (stopOrderObj == null) {
            return null;
        }

        try {
            if (stopOrderObj instanceof SimpleStopOrder simpleStopOrder) {
                log.info("Converting SimpleStopOrder to document: {}", simpleStopOrder.getOrderId().getValue());
                return toDocument(simpleStopOrder);
            } else if (stopOrderObj instanceof Map<?, ?> map) {
                log.info("Converting Map to stop order document. Keys: {}", map.keySet());
                return fromMap(map);
            } else {
                log.info("Converting {} to stop order document using reflection", stopOrderObj.getClass().getSimpleName());
                // Try reflection-based conversion for other stop order types
                return fromReflection(stopOrderObj);
            }
        } catch (Exception e) {
            log.error("Error converting object to stop order document: {}", stopOrderObj, e);
            return null;
        }
    }

    /**
     * Convert Map (from JSON deserialization) to stop order document
     */
    @SuppressWarnings("unchecked")
    private static RedisSnapshotDocument.OrderDocument fromMap(Map<?, ?> map) {
        try {
            RedisSnapshotDocument.OrderDocument.OrderDocumentBuilder builder =
                    RedisSnapshotDocument.OrderDocument.builder();

            // Extract fields from map
            builder.orderId(getStringValue(map, "orderId", "order_id"));
            builder.memberId(getLongValue(map, "memberId", "member_id"));
            builder.direction(getStringValue(map, "direction"));
            builder.type(getStringValue(map, "type", "stopOrderType", "stop_order_type"));
            builder.status(getStringValue(map, "status"));

            // Handle trigger price -> stopPrice field
            Object triggerPrice = map.get("triggerPrice");
            if (triggerPrice == null) {
                triggerPrice = map.get("trigger_price");
            }
            // Handle trigger price -> stopPrice field
            // Ensure stopPrice is always set, even if null
            builder.stopPrice(convertObjectToMoneyDocument(triggerPrice));

            // Handle execution price from 'price' field (from convertStopOrderToMap)
            Object executionPrice = map.get("price");
            if (executionPrice == null) {
                executionPrice = map.get("executionPrice");
            }
            if (executionPrice == null) {
                executionPrice = map.get("execution_price");
            }
            // Handle execution price from 'price' field (from convertStopOrderToMap)
            // Ensure price is always set, even if null
            builder.price(convertObjectToMoneyDocument(executionPrice));

            // Handle quantity
            Object quantity = map.get("quantity");
            // Ensure quantity is always set, even if null
            builder.quantity(convertObjectToMoneyDocument(quantity));

            // Handle timestamps
            Object timestamp = map.get("timestamp");
            if (timestamp != null) {
                long ts = timestamp instanceof Number number ?
                        number.longValue() :
                        Long.parseLong(timestamp.toString());
                builder.createdTime(Instant.ofEpochMilli(ts));

            }

            builder.updatedTime(Instant.now());

            // Set default filled quantity for stop orders
            builder.filledQuantity(RedisSnapshotDocument.MoneyDocument.builder()
                    .amount("0")
                    .currency("USD")
                    .scale(8)
                    .build());

            RedisSnapshotDocument.OrderDocument result = builder.build();
            log.info("  Successfully converted Map to stop order document: orderId={}, type={}, stopPrice={}",
                    result.getOrderId(), result.getType(),
                    result.getStopPrice() != null ? result.getStopPrice().getAmount() : "null");
            return result;

        } catch (Exception e) {
            log.error("Error converting Map to stop order document: {}", map, e);
            return null;
        }
    }

    /**
     * Convert using reflection for unknown stop order types
     */
    private static RedisSnapshotDocument.OrderDocument fromReflection(Object stopOrderObj) {
        try {

            RedisSnapshotDocument.OrderDocument.OrderDocumentBuilder builder =
                    RedisSnapshotDocument.OrderDocument.builder();

            // Try to extract common stop order fields using reflection
            builder.orderId(extractStringField(stopOrderObj, "getOrderId"));
            builder.memberId(extractLongField(stopOrderObj, "getMemberId"));
            builder.direction(extractStringField(stopOrderObj, "getDirection"));
            builder.type(extractStringField(stopOrderObj, "getStopOrderType", "getType"));
            builder.status(extractStringField(stopOrderObj, "getStatus"));

            // Try to extract trigger price -> stopPrice field
            // Try to extract trigger price -> stopPrice field
            // Ensure stopPrice is always set, even if null
            Object triggerPrice = extractField(stopOrderObj, "getTriggerPrice");
            builder.stopPrice(convertObjectToMoneyDocument(triggerPrice));

            // Try to extract execution price -> price field
            // Try to extract execution price -> price field
            // Ensure price is always set, even if null
            Object executionPrice = extractField(stopOrderObj, "getExecutionPrice");
            builder.price(convertObjectToMoneyDocument(executionPrice));

            // Try to extract quantity
            // Ensure quantity is always set, even if null
            Object quantity = extractField(stopOrderObj, "getQuantity");
            builder.quantity(convertObjectToMoneyDocument(quantity));

            // Try to extract timestamp
            Object timestamp = extractField(stopOrderObj, "getTimestamp", "getCreatedTime");
            if (timestamp != null) {
                long ts = timestamp instanceof Number number ?
                        number.longValue() :
                        Long.parseLong(timestamp.toString());
                builder.createdTime(Instant.ofEpochMilli(ts));
            }

            builder.updatedTime(Instant.now());

            // Set default filled quantity for stop orders
            builder.filledQuantity(RedisSnapshotDocument.MoneyDocument.builder()
                    .amount("0")
                    .currency("USD")
                    .scale(8)
                    .build());

            return builder.build();

        } catch (Exception e) {
            log.error("Error converting object using reflection: {}", stopOrderObj, e);
            return null;
        }
    }

    // Helper methods
    private static RedisSnapshotDocument.MoneyDocument convertMoneyToDocument(Money money) {
        if (money == null) {
            // Always return a MoneyDocument, even if the input Money object is null
            return RedisSnapshotDocument.MoneyDocument.builder()
                    .amount("0") // Default amount
                    .currency("USD")  // Default currency
                    .scale(8)  // Default scale
                    .build();
        }

        return RedisSnapshotDocument.MoneyDocument.builder()
                .amount(money.getAmount() != null ? money.getAmount().toString() : "0") // Ensure amount is not null
                .currency("USD")
                .scale(Money.DEFAULT_SCALE)
                .build();
    }

    private static Money convertDocumentToMoney(RedisSnapshotDocument.MoneyDocument document) {
        if (document == null || document.getAmount() == null || document.getAmount().isEmpty()) {
            return Money.of(BigDecimal.ZERO); // Return Money with zero if document or amount is null/empty
        }

        try {
            return Money.of(new BigDecimal(document.getAmount()));
        } catch (NumberFormatException e) {
            log.warn("Invalid number format for Money amount from document: {}", document.getAmount(), e);
            return Money.of(BigDecimal.ZERO); // Fallback to zero on parsing error
        }
    }

    private static RedisSnapshotDocument.MoneyDocument convertObjectToMoneyDocument(Object obj) {
        if (obj == null) {
            // Return a default MoneyDocument instead of null, to ensure the object is always present
            return RedisSnapshotDocument.MoneyDocument.builder()
                    .amount("0") // Default amount (changed from "" to "0" for consistency)
                    .currency("USD") // Default currency
                    .scale(8) // Default scale
                    .build();
        }

        if (obj instanceof Map) {
            Map<?, ?> map = (Map<?, ?>) obj;
            String amount = getStringValue(map, "amount");
            String currency = getStringValue(map, "currency");
            return RedisSnapshotDocument.MoneyDocument.builder()
                    .amount(amount != null ? amount : "") // Ensure amount is not null
                    .currency(currency != null ? currency : "USD") // Ensure currency is not null
                    .scale(getIntValue(map, "scale", 8))
                    .build();
        } else {
            return RedisSnapshotDocument.MoneyDocument.builder()
                    .amount(obj.toString() != null ? obj.toString() : "") // Ensure amount is not null
                    .currency("USD")
                    .scale(8)
                    .build();
        }
    }

    private static String getStringValue(Map<?, ?> map, String... keys) {
        for (String key : keys) {
            Object value = map.get(key);
            if (value != null) {
                return value.toString();
            }
        }
        return null;
    }

    private static Long getLongValue(Map<?, ?> map, String... keys) {
        for (String key : keys) {
            Object value = map.get(key);
            if (value != null) {
                try {
                    return value instanceof Number number ?
                            number.longValue() :
                            Long.parseLong(value.toString());
                } catch (Exception e) {
                    log.info("Could not parse long value for key {}: {}", key, value);
                }
            }
        }
        return null;
    }

    private static Integer getIntValue(Map<?, ?> map, String key, int defaultValue) {
        Object value = map.get(key);
        if (value != null) {
            try {
                return value instanceof Number number ?
                        number.intValue() :
                        Integer.parseInt(value.toString());
            } catch (Exception e) {
                log.info("Could not parse int value for key {}: {}", key, value);
            }
        }
        return defaultValue;
    }

    private static String extractStringField(Object obj, String... methodNames) {
        for (String methodName : methodNames) {
            try {
                java.lang.reflect.Method method = obj.getClass().getMethod(methodName);
                Object result = method.invoke(obj);
                if (result != null) {
                    return result.toString();
                }
            } catch (Exception e) {
                // Try next method name
            }
        }
        return null;
    }

    private static Long extractLongField(Object obj, String... methodNames) {
        for (String methodName : methodNames) {
            try {
                java.lang.reflect.Method method = obj.getClass().getMethod(methodName);
                Object result = method.invoke(obj);
                if (result instanceof Number number) {
                    return number.longValue();
                } else if (result != null) {
                    return Long.parseLong(result.toString());
                }
            } catch (Exception e) {
                // Try next method name
            }
        }
        return null;
    }

    private static Object extractField(Object obj, String... methodNames) {
        for (String methodName : methodNames) {
            try {
                java.lang.reflect.Method method = obj.getClass().getMethod(methodName);
                return method.invoke(obj);
            } catch (Exception e) {
                // Try next method name
            }
        }
        return null;
    }
}
