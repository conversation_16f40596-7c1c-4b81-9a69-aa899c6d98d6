package com.icetea.lotus.matching;

/**
 * <AUTHOR> nguyen
 */


import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.TimeZone;

/**
 * Standalone Matching Engine Microservice
 * 
 * Đây là matching engine độc lập, hoàn toàn tách biệt khỏi future và các service khác.
 * Chỉ chịu trách nhiệm cho việc khớp lệnh và gửi kết quả qua Kafka
 */
@SpringBootApplication
@EnableKafka
@EnableAsync
@EnableScheduling
@EnableDiscoveryClient
@ComponentScan(basePackages = {"com.icetea.lotus"})
public class MatchingEngineApplication {

    public static void main(String[] args) {
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
        SpringApplication.run(MatchingEngineApplication.class, args);
    }
}
