# ===================================================================
# MATCHING ENGINE CONFIGURATION
# Copy từ Future-Core module với complete connection settings
# ===================================================================

server:
  port: 6061
  servlet:
    context-path: /matching-engine
  # Cấu hình Netty - Copy từ Future-Core
  netty:
    connection-timeout: 3000
    max-connections: 1000
    max-idle-time: 10000

# C<PERSON>u hình Spring
spring:
  cloud:
    consul:
      host: ${CONSUL_HOST:************}
      port: ${CONSUL_PORT:30850}
      discovery:
        enabled: true
        service-name: ${spring.application.name}
        health-check-path: ${server.servlet.context-path}/actuator/health
        health-check-interval: 60s
        prefer-ip-address: true
        instance-id: ${spring.application.name}-${random.uuid}
        register-health-check: ${CONSUL_REGISTER_HEALTH_CHECK:true}
        # Add these properties to fix IP registration
        hostname: ${POD_IP:${spring.cloud.client.ip-address:localhost}}
        ip-address: ${POD_IP:${spring.cloud.client.ip-address:127.0.0.1}}
        prefer-agent-address: false
        deregister: true
        # Service mesh configuration
        tags:
          - "connect-enabled"
          - "v1"
          - "matching-engine"
          - "version=${APP_VERSION:latest}"
          - "environment=${ENVIRONMENT:dev}"
          - "service-mesh=enabled"
        metadata:
          connect-enabled: "true"
          service-mesh: "enabled"
          service-type: "matching-engine"
          service-port: "6061"
          mesh-enabled: "true"
  application:
    name: matching-engine

  # Cấu hình bộ nhớ đệm - Copy từ Future-Core
  cache:
    type: none
    jcache:
      config:
    caffeine:
      spec:

  # PostgreSQL removed - matching engine only needs Redis, MongoDB, Kafka

  main:
    allow-bean-definition-overriding: true

  # Cấu hình Redis - Copy từ Future-Core
  data:
    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:30679}
      #      password: 2m0881Xc30Wh
      database: ${REDIS_DATABASE:0}
      timeout: ${REDIS_TIMEOUT:5000}
      lettuce:
        pool:
          max-active: ${REDIS_POOL_MAX_ACTIVE:20}
          max-idle: ${REDIS_POOL_MAX_IDLE:10}
          min-idle: ${REDIS_POOL_MIN_IDLE:5}
          max-wait: ${REDIS_POOL_MAX_WAIT:5000}
      cache:
        enabled: false # từ application-no-cache.yaml
    mongodb:
#      uri: ${SPRING_MONGODB_URI:******************************************************************************************************************************************************************************************************************************
      uri: *****************************************************************************************************************************************************************************************************************************
      database: ${MONGODB_DATABASE:matching_engine}
      # Connection Pool Configuration
      connection-pool:
        max-size: ${MONGODB_POOL_MAX_SIZE:20}
        min-size: ${MONGODB_POOL_MIN_SIZE:5}
        max-wait-time: ${MONGODB_POOL_MAX_WAIT:5000}
        max-connection-idle-time: ${MONGODB_POOL_MAX_IDLE:30000}
        max-connection-life-time: ${MONGODB_POOL_MAX_LIFE:60000}
      # Socket Configuration
      socket:
        connect-timeout: ${MONGODB_SOCKET_CONNECT_TIMEOUT:5000}
        read-timeout: ${MONGODB_SOCKET_READ_TIMEOUT:10000}
      # Server Selection Configuration
      server-selection-timeout: ${MONGODB_SERVER_SELECTION_TIMEOUT:5000}
      # Write Concern
      write-concern:
        w: ${MONGODB_WRITE_CONCERN_W:majority}
        j: ${MONGODB_WRITE_CONCERN_J:true}
        w-timeout: ${MONGODB_WRITE_CONCERN_TIMEOUT:5000}
      # Read Preference
      read-preference: ${MONGODB_READ_PREFERENCE:primary}
      # Retry Configuration
      retry-writes: ${MONGODB_RETRY_WRITES:true}
      retry-reads: ${MONGODB_RETRY_READS:true}

  # Bộ nhớ đệm Freemarker
  freemarker:
    cache: false

  #   CRITICAL FIX: Task Scheduling Configuration
  task:
    scheduling:
      pool:
        size: ${SPRING_TASK_SCHEDULING_POOL_SIZE:5}
      thread-name-prefix: "scheduling-"
    execution:
      pool:
        core-size: ${SPRING_TASK_EXECUTION_POOL_CORE_SIZE:8}
        max-size: ${SPRING_TASK_EXECUTION_POOL_MAX_SIZE:16}
        queue-capacity: ${SPRING_TASK_EXECUTION_POOL_QUEUE_CAPACITY:100}
      thread-name-prefix: "task-execution-"

  # Cấu hình Kafka - Copy từ Future-Core
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP:************:31226}
    properties:
      sasl.mechanism: PLAIN
    listener:
      type: single
      concurrency: 9
      ack-mode: manual
    producer:
      retries: ${KAFKA_PRODUCER_RETRIES:3}
      batch-size: ${KAFKA_PRODUCER_BATCH_SIZE:256}
      linger: ${KAFKA_PRODUCER_LINGER:1}
      buffer-memory: ${KAFKA_PRODUCER_BUFFER_MEMORY:1048576}
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      compression-type: gzip
    consumer:
      group-id: ${KAFKA_CONSUMER_GROUP_ID:matching-engine-group}
      enable-auto-commit: false
      session-timeout: ${KAFKA_CONSUMER_SESSION_TIMEOUT:15000}
      auto-commit-interval: ${KAFKA_CONSUMER_AUTO_COMMIT_INTERVAL:1000}
      properties:
        max.poll.records: 50

# Exchange Topics - Copy từ Exchange module
topic-kafka:
  exchange:
    order: exchange-order
    order-cancel: exchange-order-cancel
    order-cancel-all: exchange-order-cancel-all
    order-cancel-success: exchange-order-cancel-success
    order-cancel-all-completed: exchange-order-cancel-all-completed
    order-completed: exchange-order-completed
    trade: exchange-trade
    trade-plate: exchange-trade-plate
    trade-events: exchange-trade-events
    order-events: exchange-order-events
    last-price: exchange-last-price
    stop-order-triggered: exchange-stop-order-triggered

  # Contract Topics - Copy từ Future-Core module
  contract:
    order-new: contract-order-new
    order-cancel: contract-order-cancel
    order-cancel-broadcast: contract-order-cancel-broadcast
    order-cancel-success: contract-order-cancel-success
    order-completed: contract-order-completed
    trade: contract-trade
    trade-plate: contract-trade-plate
    position: contract-position
    mark-price: contract-mark-price
    index-price: contract-index-price
    funding-rate: contract-funding-rate
    liquidation: contract-liquidation
    last-price: contract-last-price
    order-events: contract-order-events
    order-commands: contract-order-commands
    order-cancel-result: contract-order-cancel-result
    order-cancel-all-result: contract-order-cancel-all-result
    order-triggered: contract-order-triggered
    position-cancel-result: contract-position-cancel-result

# Consumer Groups - Matching Engine groups
consumer-groups:
  exchange:
    orders: matching-engine-exchange-orders
    cancels: matching-engine-exchange-cancels
    trades: matching-engine-exchange-trades

  contract:
    orders: matching-engine-contract-orders
    cancels: matching-engine-contract-cancels
    trades: matching-engine-contract-trades
    events: matching-engine-order-events
    funding-rate: matching-engine-funding-rate

# Kafka Producer Configuration
kafka:
  producer:
    acks: all  # Required for idempotent producer
    retries: 3
    batch-size: 256
    linger-ms: 1
    buffer-memory: 1048576
    compression-type: gzip

  # Consumer Configuration
  consumer-config:
    auto-offset-reset: latest
    enable-auto-commit: false
    auto-commit-interval-ms: 1000
    session-timeout-ms: 15000
    max-poll-records: 50
    fetch-min-bytes: 1
    fetch-max-wait-ms: 500

# Matching Engine Configuration
matching-engine:
  # Auto-initialize default symbols
  auto-init-symbols: ${MATCHING_ENGINE_AUTO_INIT_SYMBOLS:true}
  default-symbols: BTC/USDT,ETH/USDT,BNB/USDT,ADA/USDT,XRP/USDT,SOL/USDT,DOT/USDT,DOGE/USDT,AVAX/USDT,MATIC/USDT,LINK/USDT,UNI/USDT,LTC/USDT,EOS/ETH,BZB/USDT,HT/USDT,EOS/USDT,BSV/USDT,BCH/USDT,ETC/USDT,ETH/BTC,BTC/ETH,XRP/BTC,CANCEL_ALL_ORDER,CANCEL_ALL_POSITION

  # Pod identification for sharding
  pod-name: ${MATCHING_ENGINE_POD_NAME:${HOSTNAME:matching-engine-pod}}

  # Pod-specific symbols configuration
  # Format: symbol1,symbol2,symbol3 (comma-separated)
  # Example: BTC/USDT,ETH/USDT for pod-1, SOL/USDT,DOT/USDT for pod-2
  pod-symbols: BTC/USDT,ETH/USDT,BNB/USDT,ADA/USDT,XRP/USDT,SOL/USDT,DOT/USDT,DOGE/USDT,AVAX/USDT,MATIC/USDT,LINK/USDT,UNI/USDT,LTC/USDT,EOS/ETH,BZB/USDT,HT/USDT,EOS/USDT,BSV/USDT,BCH/USDT,ETC/USDT,ETH/BTC,BTC/ETH,XRP/BTC,CANCEL_ALL_ORDER,CANCEL_ALL_POSITION
  auto-init-pod-symbols: ${MATCHING_ENGINE_AUTO_INIT_POD_SYMBOLS:true}

  # Exchange Configuration
  exchange:
    enabled: true
    default-algorithm: FIFO
    balance-validation: true
    trade-plate-publishing: true
    # Last price persistence configuration (always enabled for stop orders)
    lastprice:
      mongodb-save-enabled: ${MATCHING_ENGINE_LASTPRICE_MONGODB_SAVE_ENABLED:true}  # Always save lastprice to MongoDB

  # Future-Core Configuration
  future-core:
    enabled: true
    default-algorithm: FIFO
    cas-retry-limit: 10
    performance-monitoring: true

  # Performance Configuration
  performance:
    metrics-collection: true
    health-monitoring: true
    throughput-target: 12000  # TPS target
    latency-target: 5         # ms target
    thread-pool-size: ${MATCHING_ENGINE_THREAD_POOL_SIZE:20}
    batch-size: ${MATCHING_ENGINE_BATCH_SIZE:100}
    timeout-ms: ${MATCHING_ENGINE_TIMEOUT_MS:5000}

  # Order Book Configuration
  order-book:
    segment-size: 1000
    cache-validity-ms: 5000
    max-depth: 100

  # Sharding settings
  sharding:
    enabled: ${MATCHING_ENGINE_SHARDING_ENABLED:true}
    cache-size: ${MATCHING_ENGINE_CACHE_SIZE:1000}

  # Algorithm settings
  algorithm:
    default: ${MATCHING_ENGINE_DEFAULT_ALGORITHM:FIFO}
    spot: ${MATCHING_ENGINE_SPOT_ALGORITHM:FIFO}
    futures: ${MATCHING_ENGINE_FUTURES_ALGORITHM:FIFO}

  # MongoDB Snapshot Configuration (DISABLED - using Redis instead)
  mongodb:
    enabled: ${MATCHING_ENGINE_MONGODB_ENABLED:false}
    # Snapshot Settings
    snapshot:
      enabled: ${MATCHING_ENGINE_SNAPSHOT_ENABLED:true}
      auto-save: ${MATCHING_ENGINE_SNAPSHOT_AUTO_SAVE:false}
      save-interval-seconds: ${MATCHING_ENGINE_SNAPSHOT_SAVE_INTERVAL:100}  # 5 minutes
      max-versions-per-symbol: ${MATCHING_ENGINE_SNAPSHOT_MAX_VERSIONS:10}
      compression-enabled: ${MATCHING_ENGINE_SNAPSHOT_COMPRESSION:true}
      async-save: ${MATCHING_ENGINE_SNAPSHOT_ASYNC:true}
      batch-size: ${MATCHING_ENGINE_SNAPSHOT_BATCH_SIZE:100}

  # Redis Snapshot Configuration (NEW)
  redis:
    snapshot:
      enabled: ${MATCHING_ENGINE_REDIS_SNAPSHOT_ENABLED:true}
      auto-save: ${MATCHING_ENGINE_REDIS_SNAPSHOT_AUTO_SAVE:true}
      save-interval-seconds: ${MATCHING_ENGINE_REDIS_SNAPSHOT_SAVE_INTERVAL:100}  # Same as MongoDB
      max-versions-per-symbol: ${MATCHING_ENGINE_REDIS_SNAPSHOT_MAX_VERSIONS:5}  # Reduced from 10 to 5
      ttl-hours: ${MATCHING_ENGINE_REDIS_SNAPSHOT_TTL_HOURS:24}  # 24 hours TTL
      compression-enabled: ${MATCHING_ENGINE_REDIS_SNAPSHOT_COMPRESSION:true}
      async-save: ${MATCHING_ENGINE_REDIS_SNAPSHOT_ASYNC:true}
      # Startup restore configuration
      startup-restore-enabled: ${MATCHING_ENGINE_REDIS_STARTUP_RESTORE_ENABLED:true}  # Enable restore on startup
      cleanup:
        enabled: ${MATCHING_ENGINE_REDIS_CLEANUP_ENABLED:true}
        grace-period-minutes: ${MATCHING_ENGINE_REDIS_CLEANUP_GRACE_PERIOD:5}  # 5 minutes grace period
        batch-size: ${MATCHING_ENGINE_REDIS_CLEANUP_BATCH_SIZE:100}  # Process 100 keys at a time
        aggressive-mode: ${MATCHING_ENGINE_REDIS_CLEANUP_AGGRESSIVE:true}  # Enable aggressive cleanup
    # Cleanup Settings
    cleanup:
      enabled: ${MATCHING_ENGINE_CLEANUP_ENABLED:true}
      retention-days: ${MATCHING_ENGINE_CLEANUP_RETENTION_DAYS:7}
      cleanup-interval-hours: ${MATCHING_ENGINE_CLEANUP_INTERVAL_HOURS:1}
      max-snapshots-per-cleanup: ${MATCHING_ENGINE_CLEANUP_MAX_SNAPSHOTS:1000}
    # Collection Names
    collections:
      order-book-snapshots: ${MONGODB_COLLECTION_ORDER_BOOK_SNAPSHOTS:order_book_snapshots}
      trade-history: ${MONGODB_COLLECTION_TRADE_HISTORY:trade_history}
      performance-metrics: ${MONGODB_COLLECTION_PERFORMANCE_METRICS:performance_metrics}
      system-events: ${MONGODB_COLLECTION_SYSTEM_EVENTS:system_events}
    # Index Configuration
    indexes:
      auto-create: ${MONGODB_INDEXES_AUTO_CREATE:true}
      background: ${MONGODB_INDEXES_BACKGROUND:true}
    # Performance Settings
    performance:
      bulk-write-size: ${MONGODB_BULK_WRITE_SIZE:1000}
      query-timeout-ms: ${MONGODB_QUERY_TIMEOUT:10000}
      cursor-timeout-ms: ${MONGODB_CURSOR_TIMEOUT:30000}

# Logging Configuration
logging:
  level:
    com.icetea.lotus.matching: INFO
    com.icetea.lotus.matching.infrastructure.messaging: DEBUG
    com.icetea.lotus.matching.infrastructure.exchange: INFO
    com.icetea.lotus.matching.infrastructure.futurecore: INFO
    org.apache.kafka: WARN
    org.springframework.kafka: WARN
    org.hibernate.SQL: ${LOGGING_HIBERNATE_SQL:WARN}
    org.hibernate.type.descriptor.sql.BasicBinder: ${LOGGING_HIBERNATE_TYPE:WARN}

  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"

# Management and Monitoring - Copy từ Future-Core
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
    prometheus:
      enabled: true
  tracing:
    sampling:
      probability: 1.0
    baggage:
      remote-fields:
        - transaction-id
      correlation:
        fields:
          - transaction-id


# Circuit Breaker Configuration - Copy từ Future-Core
resilience4j:
  circuitbreaker:
    instances:
      matching-engine:
        registerHealthIndicator: true
        slidingWindowSize: 10
        minimumNumberOfCalls: 5
        permittedNumberOfCallsInHalfOpenState: 3
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 5s
        failureRateThreshold: 50
        eventConsumerBufferSize: 10
        recordExceptions:
          - org.springframework.web.client.HttpServerErrorException
          - java.util.concurrent.TimeoutException
          - java.io.IOException
        ignoreExceptions:
          - org.springframework.web.client.HttpClientErrorException

# Feature Flags - Copy từ Future-Core
features:
  experimental:
    advanced-matching-algorithms: true
    predictive-caching: false
    machine-learning-optimization: false
    adaptive-batching: true
    dynamic-segmentation: false

# Production Optimization - Copy từ Future-Core
production:
  mode: true
  aggressive-optimization: true
  strict-monitoring: true
  auto-tuning: false

# Pod Configuration - Copy từ Future-Core
pod:
  name: ${POD_NAME:matching-engine-pod-1}
  namespace: ${POD_NAMESPACE:default}
  ip: ${POD_IP:127.0.0.1}


# Application Info
info:
  app:
    name: Matching Engine
    description: High-performance order matching engine
    version: 1.0.0
    architecture: Message-driven (Kafka)
    modules:
      - Exchange Module (Spot Trading)
      - Future-Core Module (Advanced Algorithms)
      - Performance Optimization
