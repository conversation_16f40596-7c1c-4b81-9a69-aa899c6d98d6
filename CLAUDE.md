# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

# Cryptocurrency Exchange Matching Engine

## Project Overview

This is a standalone, high-performance matching engine microservice for cryptocurrency exchange operations. It's designed as a distributed, message-driven service that handles both spot trading and futures trading order matching with advanced algorithms and optimization features.

## Architecture

### Technology Stack
- **Language**: Java 17
- **Framework**: Spring Boot 3.1.5
- **Messaging**: Apache Kafka for event-driven architecture
- **Cache/Coordination**: Redis with Redisson for distributed locking
- **Database**: MongoDB for snapshots and persistence
- **Monitoring**: Spring Boot Actuator with Prometheus metrics

### Core Components
- **DistributedOrderBook**: Thread-safe order book with segmented storage and StampedLock optimization
- **FutureCoreMatchingEngine**: Lock-free CAS-based matching engine with 12K+ TPS performance
- **ExchangeMatchingEngine**: Traditional TreeMap-based matching for spot trading
- **DistributedMatchingEngineCoordinator**: Manages distributed coordination across pods
- **SymbolShardingManager**: Handles symbol distribution and load balancing

## Build and Development Commands

### Essential Commands
```bash
# Build the project
mvn clean compile

# Package the application
mvn clean package

# Run tests
mvn test

# Run specific test class
mvn test -Dtest=OrderPerformanceTest

# Skip tests during build
mvn clean package -DskipTests

# Run the application
java -jar target/matching-engine-1.0.0-SNAPSHOT.jar
```

### Performance Testing
```bash
# Quick performance test (50 users, 5 orders each)
./run-performance-test.sh quick

# Heavy load test (100 users, 10 orders each)  
./run-performance-test.sh heavy

# Full performance test (100 CCU, realistic trading)
./run-performance-test.sh full

# Extreme load test (1000 orders/sec for 1 minute)
./run-performance-test.sh extreme

# Gradual load test (Progressive: 10->50->200->500 RPS)
./run-performance-test.sh gradual

# Message loss analysis
./run-performance-test.sh analysis
```

### Test Development
```bash
# Run specific performance test method
mvn test -Dtest=com.icetea.lotus.matching.performance.SimpleLoadTest#quickLoadTest

# Run all performance tests
mvn test -Dtest=com.icetea.lotus.matching.performance.*
```

## Key Architecture Patterns

### Lock-Free Matching Engine
The system uses Compare-And-Swap (CAS) operations for high-performance order matching:
- **AtomicReference<DistributedOrderBookSnapshot>** for lock-free order book updates
- **CAS retry loops** with configurable retry limits
- **Iterator-based order traversal** for efficient matching
- **Performance metrics collection** with atomic counters

### Distributed Coordination
- **Symbol-based sharding** across multiple pods using consistent hashing
- **Redis-based distributed locking** with Redisson
- **Order routing** between pods based on symbol ownership
- **Dynamic pod discovery** and health monitoring

### Message-Driven Architecture
- **Input Topics**: 
  - `contract-order-new` - New order commands
  - `contract-order-cancel` - Order cancellation requests
  - `order-routing` - Cross-pod order routing
- **Output Topics**:
  - `contract-trade` - Trade execution results
  - `contract-order-events` - Order status updates
  - `contract-trade-plate` - Market data updates

## Core Entities and Value Objects

### Domain Entities
- **Order**: Core order entity with OrderId, Symbol, Money amounts, and direction
- **Trade**: Trade execution result with TradeId and participant details
- **SimpleStopOrder**: Stop order implementation with strategy detection
- **Member**: Trading participant representation

### Value Objects
- **OrderId**: Unique order identifier using Snowflake ID generation
- **Symbol**: Trading pair representation
- **Money**: Monetary amounts with BigDecimal precision
- **TurnoverAmount**: Trade volume calculations

## Performance Characteristics

### Benchmarked Performance
- **Target**: 100,000+ orders per second per pod
- **Actual**: 12,000+ TPS confirmed in testing
- **Latency**: < 1ms average order processing time
- **CAS Success Rate**: 95%+ under normal load
- **Memory Efficiency**: Object pooling and reduced GC pressure

### Optimization Features
- **Lock-free operations** with CAS-based updates
- **Iterator pattern** for efficient order book traversal  
- **Batch processing** for multiple operations
- **StampedLock** for optimistic read operations
- **Price range segmentation** for better performance

## Configuration Profiles

### Application Profiles
- **Default**: Standard configuration
- **dev**: Development environment settings
- **test**: Test environment with minimal configuration
- **prod**: Production optimizations

### Key Configuration Areas
```yaml
matching-engine:
  performance:
    thread-pool-size: 20
    batch-size: 100
    timeout-ms: 5000
  
  sharding:
    enabled: true
    cache-size: 1000
  
  algorithm:
    default: FIFO
    spot: FIFO
    futures: FIFO
```

## Integration Points

### External Service Integration
- **Exchange API**: Receives orders from spot trading service
- **Future-Core**: Processes futures/derivatives orders
- **Post-Trade Services**: Delegates position management and balance updates
- **Market Data**: Publishes real-time trade plates and price updates

### Kafka Topic Integration
The service integrates with multiple Kafka topics for different trading types:
- Exchange topics for spot trading
- Contract topics for futures trading
- Cross-pod routing topics for distributed coordination

## Development Guidelines

### Code Organization
- **domain/**: Core business entities and value objects
- **infrastructure/engine/**: Order book and matching engine implementations
- **infrastructure/messaging/**: Kafka consumers and producers
- **infrastructure/sharding/**: Distributed coordination logic
- **infrastructure/optimization/**: Performance optimization utilities

### Testing Strategy
Performance tests are located in `src/test/java/com.icetea.lotus.matching.performance/`:
- **SimpleLoadTest**: Basic load testing scenarios
- **OrderPerformanceTest**: Comprehensive performance validation
- **ExtremeLoadTest**: High-volume stress testing
- **GradualLoadTest**: Progressive load testing
- **MessageLossAnalysisTest**: Message delivery reliability testing

### Performance Monitoring
The application includes comprehensive monitoring:
- **Actuator endpoints**: `/actuator/health`, `/actuator/metrics`
- **Prometheus metrics**: Custom matching engine metrics
- **Performance counters**: Orders processed, trades generated, CAS statistics
- **Health checks**: Pod health, cluster status, symbol distribution

## Deployment

### Docker Support
- **Dockerfile**: Multi-stage build for optimization
- **Port**: 6061 with context path `/matching-engine`
- **Health checks**: Built-in Spring Boot health indicators

### Environment Variables
Key environment variables for configuration:
- `KAFKA_BOOTSTRAP`: Kafka broker addresses
- `REDIS_HOST`: Redis server host for distributed coordination
- `MONGODB_URI`: MongoDB connection for snapshots
- `MATCHING_ENGINE_POD_NAME`: Pod identifier for sharding

### Monitoring and Operations
- **Circuit breaker**: Resilience4j configuration for fault tolerance
- **Distributed tracing**: OpenTelemetry integration
- **Structured logging**: JSON format with correlation IDs
- **Snapshot persistence**: MongoDB-based order book snapshots

## Important Implementation Notes

### Symbol Sharding
Each trading symbol is assigned to a specific pod using consistent hashing. Orders for symbols not owned by the current pod are routed to the appropriate pod via Kafka.

### Matching Algorithms
The system supports multiple matching algorithms:
- **FIFO**: First-In-First-Out (price-time priority)
- **PRO_RATA**: Proportional allocation
- **HYBRID**: Combined FIFO and Pro-Rata
- **TWAP**: Time-Weighted Average Price

### Error Handling
- **CAS failures**: Automatic retry with configurable limits
- **Order validation**: Comprehensive input validation
- **Circuit breakers**: Fault tolerance for external dependencies
- **Graceful degradation**: System continues operating under partial failures

### Security Considerations
- **Order isolation**: Symbol-based access control
- **Message authentication**: Kafka security integration
- **Audit trail**: Complete operation logging for compliance