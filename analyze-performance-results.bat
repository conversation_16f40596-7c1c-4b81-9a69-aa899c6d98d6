@echo off
echo ===================================================
echo      PERFORMANCE RESULTS ANALYZER
echo ===================================================
echo.

REM Check if results directory exists
if not exist "performance-results" (
    echo ❌ No performance results found!
    echo Please run performance tests first using run-automated-performance-test.bat
    pause
    exit /b 1
)

REM Count available test runs
set count=0
for /d %%i in (performance-results\test_*) do set /a count+=1

if %count%==0 (
    echo ❌ No test runs found in performance-results directory!
    pause
    exit /b 1
)

echo 📊 Found %count% test run(s) in performance-results directory
echo.

echo Choose analysis type:
echo 1. Analyze Latest Test Run (Detailed analysis)
echo 2. Compare Multiple Test Runs (Trend analysis)
echo 3. View Latest Summary Report
echo 4. List All Test Runs
echo.

set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" (
    echo.
    echo 📋 Analyzing latest test run...
    java -cp "target/classes;target/test-classes" com.icetea.lotus.matching.performance.PerformanceResultAnalyzer
    
    if %ERRORLEVEL% neq 0 (
        echo ❌ Analysis failed!
    ) else (
        echo ✅ Analysis completed!
        echo.
        echo 📁 Generated files:
        for /f "delims=" %%i in ('dir /b /od performance-results\test_*') do set LATEST_DIR=%%i
        if exist "performance-results\%LATEST_DIR%\DETAILED_ANALYSIS.md" (
            echo   - DETAILED_ANALYSIS.md (Comprehensive analysis)
        )
        if exist "performance-results\%LATEST_DIR%\PERFORMANCE_TRENDS.md" (
            echo   - PERFORMANCE_TRENDS.md (Historical trends)
        )
        echo.
        echo Opening detailed analysis...
        if exist "performance-results\%LATEST_DIR%\DETAILED_ANALYSIS.md" (
            start notepad "performance-results\%LATEST_DIR%\DETAILED_ANALYSIS.md"
        )
    )
    
) else if "%choice%"=="2" (
    if %count% LSS 2 (
        echo ❌ Need at least 2 test runs for comparison!
        echo Please run more performance tests first.
    ) else (
        echo.
        echo 📊 Comparing multiple test runs...
        java -cp "target/classes;target/test-classes" com.icetea.lotus.matching.performance.PerformanceResultAnalyzer compare
        
        if %ERRORLEVEL% neq 0 (
            echo ❌ Comparison failed!
        ) else (
            echo ✅ Comparison completed!
            echo.
            echo 📁 Generated comparison report in performance-results directory
            echo Opening comparison report...
            for /f "delims=" %%i in ('dir /b /od performance-results\COMPARISON_REPORT_*.md') do set LATEST_COMPARISON=%%i
            if defined LATEST_COMPARISON (
                start notepad "performance-results\%LATEST_COMPARISON%"
            )
        )
    )
    
) else if "%choice%"=="3" (
    echo.
    echo 📋 Opening latest summary report...
    for /f "delims=" %%i in ('dir /b /od performance-results\test_*') do set LATEST_DIR=%%i
    if exist "performance-results\%LATEST_DIR%\SUMMARY_REPORT.md" (
        start notepad "performance-results\%LATEST_DIR%\SUMMARY_REPORT.md"
        echo ✅ Summary report opened
    ) else (
        echo ❌ Summary report not found for latest test run
    )
    
) else if "%choice%"=="4" (
    echo.
    echo 📁 Available test runs:
    echo.
    for /f "delims=" %%i in ('dir /b /od performance-results\test_*') do (
        echo   📂 %%i
        if exist "performance-results\%%i\test_suite_results.json" (
            echo      ✅ Complete results available
        ) else (
            echo      ⚠️ Incomplete results
        )
        echo.
    )
    
) else (
    echo ❌ Invalid choice!
)

echo.
echo ===================================================
echo         PERFORMANCE ANALYSIS COMPLETED
echo ===================================================

REM Ask if user wants to perform another analysis
echo.
set /p rerun="Perform another analysis? (Y/N): "
if /i "%rerun%"=="Y" (
    echo.
    goto :start
)

pause
