# Market Order Logic Improvements

## Tổng quan
Đã cải thiện logic xử lý market order trong `FutureCoreMatchingEngine` để phù hợp với nghiệp vụ thực tế của thị trường tài chính.

## Các thay đổi chính

### 1. **Logic xác định giá giao dịch cho Market Order**
- **Trước:** Market order sử dụng giá từ order book price level
- **Sau:** Market order lấy giá từ limit order mà nó khớp với

```java
// Phương thức mới: determineTradePrice()
private Money determineTradePrice(Order takerOrder, Order makerOrder, Money orderBookPrice) {
    // Market order luôn lấy giá từ limit order
    if (takerOrder.getType().name().equals("MARKET")) {
        return makerOrder.getPrice(); // Lấy gi<PERSON> từ maker (limit order)
    }
    if (makerOrder.getType().name().equals("MARKET")) {
        return takerOrder.getPrice(); // Lấy giá từ taker (limit order)
    }
    return makerOrder.getPrice(); // Cả hai đều limit
}
```

### 2. **Market Order không được thêm vào Order Book**
- **Trước:** Market order có thể được thêm vào order book nếu không khớp hết
- **Sau:** Market order KHÔNG BAO GIỜ được thêm vào order book

```java
// Market orders KHÔNG được thêm vào order book
if (!isOrderCompleted(order) && !order.getType().name().equals("MARKET")) {
    addOrderToSnapshot(order, newSnapshot);
} else if (order.getType().name().equals("MARKET") && !isOrderCompleted(order)) {
    // Market order chưa khớp hết -> Set status partially filled
    order.setStatus(OrderStatus.PARTIAL_FILLED);
}
```

### 3. **Validation Market Order**
- **Thêm:** Validation để đảm bảo market order không có giá đầu vào
- **Logic:** Nếu market order có giá, sẽ được clear và log warning

```java
private void validateMarketOrder(Order order) {
    if (order.getType().name().equals("MARKET")) {
        if (order.getPrice() != null && order.getPrice().getValue().compareTo(BigDecimal.ZERO) > 0) {
            logger.warn("Market order {} has input price {} - this will be ignored", 
                order.getOrderId().getValue(), order.getPrice());
            order.setPrice(null); // Clear giá cho market order
        }
    }
}
```

### 4. **Logic khớp lệnh Market Order riêng biệt**
- **Thêm:** `matchMarketOrderFIFO()`, `matchMarketBuyOrderFIFO()`, `matchMarketSellOrderFIFO()`
- **Logic:** Market order được ưu tiên và thực thi ngay lập tức

```java
// Market Buy: Khớp với sell orders từ giá thấp nhất
private void matchMarketBuyOrderFIFO(Order marketBuyOrder, DistributedOrderBookSnapshot snapshot, List<Trade> trades) {
    // Khớp với sell orders theo thứ tự giá tăng dần (giá tốt nhất trước)
    Iterator<Map.Entry<Money, List<Order>>> sellIterator = snapshot.getSellOrdersIterator();
    // ... logic khớp lệnh
}

// Market Sell: Khớp với buy orders từ giá cao nhất  
private void matchMarketSellOrderFIFO(Order marketSellOrder, DistributedOrderBookSnapshot snapshot, List<Trade> trades) {
    // Khớp với buy orders theo thứ tự giá giảm dần (giá tốt nhất trước)
    Iterator<Map.Entry<Money, List<Order>>> buyIterator = snapshot.getBuyOrdersIterator();
    // ... logic khớp lệnh
}
```

### 5. **Cải thiện logic canMatch() cho Market Order**
- **Trước:** `return true; // Market orders can always match`
- **Sau:** Logic chi tiết hơn với logging và validation

```java
private boolean canMatch(Order order1, Order order2) {
    // ... basic validations
    
    boolean order1IsMarket = order1.getType().name().equals("MARKET");
    boolean order2IsMarket = order2.getType().name().equals("MARKET");
    
    // Market order có thể khớp với bất kỳ order nào (không có giới hạn giá)
    if (order1IsMarket || order2IsMarket) {
        logger.debug("Market order matching: order1={} (market={}), order2={} (market={})", 
            order1.getOrderId().getValue(), order1IsMarket,
            order2.getOrderId().getValue(), order2IsMarket);
        return true;
    }
    
    // Cả hai đều limit orders - check price compatibility
    // ... price matching logic
}
```

### 6. **Cập nhật các thuật toán khớp lệnh**
- **Pro-Rata, Hybrid, TWAP:** Market order sử dụng FIFO thay vì thuật toán phức tạp
- **Lý do:** Market order cần thực thi ngay lập tức, không phù hợp với thuật toán phân bổ

```java
private void matchOrderProRata(Order order, DistributedOrderBookSnapshot snapshot, List<Trade> trades) {
    // Market orders sử dụng FIFO cho việc thực thi ngay lập tức
    if (order.getType().name().equals("MARKET")) {
        logger.info("Market order {} using FIFO instead of Pro-Rata for immediate execution", 
            order.getOrderId().getValue());
        matchMarketOrderFIFO(order, snapshot, trades);
    } else {
        // Regular limit order processing với Pro-Rata
        // ... existing logic
    }
}
```

## Nghiệp vụ thực tế được áp dụng

### **Market Buy Order:**
1. Khớp với sell limit orders theo thứ tự giá tăng dần (giá thấp nhất trước)
2. Mỗi trade lấy giá từ sell limit order tương ứng
3. Không có giá trung bình - mỗi trade có giá riêng

### **Market Sell Order:**
1. Khớp với buy limit orders theo thứ tự giá giảm dần (giá cao nhất trước)
2. Mỗi trade lấy giá từ buy limit order tương ứng
3. Không có giá trung bình - mỗi trade có giá riêng

### **Ví dụ thực tế:**
```
Order Book:
Sell: 50,100 USDT (0.5 BTC), 50,200 USDT (0.3 BTC)
Buy:  49,900 USDT (0.4 BTC), 49,800 USDT (0.6 BTC)

Market Buy 0.7 BTC sẽ tạo ra:
- Trade 1: 0.5 BTC @ 50,100 USDT (từ sell limit)
- Trade 2: 0.2 BTC @ 50,200 USDT (từ sell limit)

Market Sell 0.8 BTC sẽ tạo ra:
- Trade 1: 0.4 BTC @ 49,900 USDT (từ buy limit)
- Trade 2: 0.4 BTC @ 49,800 USDT (từ buy limit)
```

## Test Coverage
Đã thêm `MarketOrderTest.java` để test các scenario:
- Market buy khớp với sell limit orders
- Market sell khớp với buy limit orders  
- Market order với input price bị clear
- Market order không được thêm vào order book

## Kết quả
Logic market order hiện tại đã phù hợp với nghiệp vụ thực tế:
- ✅ Market order không cần giá đầu vào
- ✅ Giá giao dịch lấy từ limit order đối ứng
- ✅ Market order không vào order book
- ✅ Thực thi ngay lập tức với giá tốt nhất
- ✅ Logging chi tiết cho debugging
