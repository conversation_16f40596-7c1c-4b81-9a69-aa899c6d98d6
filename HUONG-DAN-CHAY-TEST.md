# Hướng Dẫn Chạy Test Load Cho Matching Engine

## Tổng Quan

Hệ thống test load bao gồm các công cụ để kiểm tra hiệu năng của matching engine với các mức độ tải khác nhau. Có 2 phương pháp test chính:
1. **K6 Test** - Test thông qua HTTP API
2. **Python Kafka Test** - Gửi trực tiếp message vào Kafka topic

## Chuẩn Bị Môi Trường

### 1. Cài Đặt K6
```bash
# Tải K6 từ https://k6.io/docs/get-started/installation/
# Hoặc sử dụng chocolatey trên Windows:
choco install k6

# Kiểm tra cài đặt
k6 version
```

### 2. Cài Đặt Python Dependencies
```bash
# Chạy file setup
setup-real-kafka-test.bat

# Hoặc cài đặt thủ công
pip install kafka-python
```

## Phương Pháp 1: K6 HTTP Test

### Test Scenarios Cơ Bản

#### 1. CCU/CCR Test (Concurrent Users/Requests)
```bash
# Test mức độ low (10 users)
k6 run ccu-ccr-test.js -e LEVEL=low

# Test mức độ medium (50 users) 
k6 run ccu-ccr-test.js -e LEVEL=medium

# Test mức độ high (100 users)
k6 run ccu-ccr-test.js -e LEVEL=high

# Test mức độ extreme (200 users)
k6 run ccu-ccr-test.js -e LEVEL=extreme
```

#### 2. Throughput Test (1000 msg/sec với VU khác nhau)
```bash
k6 run kafka-throughput-test.js
```

#### 3. Market Simulation Test (Kịch bản thị trường thực tế)
```bash
k6 run unified-exchange-test.js
```

### Kết Quả K6 Test
- **Metrics hiển thị**: Latency, RPS, Error Rate, Authentication Success
- **Export kết quả**: K6 tự động xuất báo cáo HTML
- **Real-time monitoring**: Metrics hiển thị trong quá trình chạy

## Phương Pháp 2: Python Kafka Test (Khuyến Nghị)

### Chạy Test Kafka Trực Tiếp

#### 1. Sử dụng Menu Tương Tác
```bash
# Chạy menu lựa chọn
run-real-kafka-test.bat

# Chọn scenario:
# 1. Quick Test (100 msg, 10 VU, 30s)
# 2. Light Load (1000 msg, 50 VU, 2m)  
# 3. Medium Load (5000 msg, 100 VU, 5m)
# 4. Heavy Load (10000 msg, 200 VU, 10m)
# 5. Extreme Load (50000 msg, 500 VU, 20m)
```

#### 2. Chạy Trực Tiếp Với Tham Số
```bash
# Quick test
python real-kafka-test.py --messages 100 --users 10 --duration 30

# Medium test  
python real-kafka-test.py --messages 5000 --users 100 --duration 300

# Heavy test
python real-kafka-test.py --messages 10000 --users 200 --duration 600

# Custom test
python real-kafka-test.py --messages 2000 --users 50 --duration 120
```

### Kafka Test Parameters

| Parameter | Mô tả | Giá trị mặc định |
|-----------|-------|------------------|
| `--messages` | Tổng số message gửi | 1000 |
| `--users` | Số lượng concurrent users | 50 |
| `--duration` | Thời gian chạy (giây) | 120 |
| `--rate` | Rate limit (msg/sec) | Không giới hạn |

## Cấu Hình Test

### 1. Kafka Configuration
```yaml
# File: application-dev.yml
spring:
  kafka:
    bootstrap-servers: ************:30092
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
```

### 2. Authentication Configuration
```javascript
// Token sẽ được cache và sử dụng lại
const AUTH_TOKEN = "your-access-token";
const BASE_URL = "http://************:30113";
```

### 3. Test Data Configuration
```python
# Các trading pairs hỗ trợ
TRADING_PAIRS = ["BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT", "SOLUSDT"]

# Tỷ lệ buy/sell theo từng kịch bản thị trường
MARKET_SCENARIOS = {
    "market_opening": {"buy_ratio": 0.7, "sell_ratio": 0.3},
    "normal_trading": {"buy_ratio": 0.5, "sell_ratio": 0.5}, 
    "news_spike": {"buy_ratio": 0.8, "sell_ratio": 0.2},
    "market_close": {"buy_ratio": 0.3, "sell_ratio": 0.7}
}
```

## Monitoring và Kết Quả

### 1. Real-time Metrics
```
=== KAFKA LOAD TEST STATISTICS ===
Messages Sent: 1250/5000 (25.0%)
Success Rate: 100.0%
Failed Messages: 0
Current Rate: 125.3 msg/sec
Average Rate: 89.2 msg/sec
Elapsed Time: 14.0s
```

### 2. Kafka Topic Monitoring
```bash
# Kiểm tra Kafka topic
kafka-topics.sh --bootstrap-server ************:30092 --describe --topic dev-exchange-order

# Monitor messages trong topic
kafka-console-consumer.sh --bootstrap-server ************:30092 --topic dev-exchange-order --from-beginning
```

### 3. Application Logs
```bash
# Theo dõi logs của matching engine
tail -f logs/matching-engine.log

# Kiểm tra Kafka consumer logs
grep "dev-exchange-order" logs/application.log
```

## Các Kịch Bản Test Phổ Biến

### 1. Smoke Test (Kiểm tra cơ bản)
```bash
python real-kafka-test.py --messages 100 --users 5 --duration 30
```

### 2. Load Test (Kiểm tra tải bình thường)
```bash
python real-kafka-test.py --messages 5000 --users 100 --duration 300
```

### 3. Stress Test (Kiểm tra tải cao)
```bash
python real-kafka-test.py --messages 20000 --users 500 --duration 600
```

### 4. Spike Test (Kiểm tra tải đột biến)
```bash
# Tăng đột ngột từ 50 users lên 500 users
python real-kafka-test.py --messages 10000 --users 500 --duration 180
```

### 5. Volume Test (Kiểm tra khối lượng lớn)
```bash
python real-kafka-test.py --messages 100000 --users 1000 --duration 1800
```

## Troubleshooting

### 1. Kafka Connection Issues
```
Error: Failed to connect to Kafka broker
Solution: 
- Kiểm tra Kafka broker: ************:30092
- Kiểm tra firewall và network connectivity
- Xác nhận topic dev-exchange-order tồn tại
```

### 2. Authentication Failed
```
Error: 401 Unauthorized
Solution:
- Kiểm tra access token trong file test
- Renew token nếu cần thiết
- Xác nhận endpoint authentication
```

### 3. High Error Rate
```
Error: Success rate < 90%
Solution:
- Giảm số lượng concurrent users
- Tăng timeout settings
- Kiểm tra server capacity
```

### 4. Low Throughput
```
Issue: Throughput thấp hơn mong đợi
Solution:
- Tăng batch size trong Kafka producer
- Optimize network settings
- Kiểm tra server resources (CPU, Memory)
```

## Best Practices

### 1. Test Planning
- Bắt đầu với smoke test trước khi chạy load test
- Tăng dần tải từ low → medium → high → extreme
- Monitor server resources trong quá trình test

### 2. Data Management
- Sử dụng test data realistic (BTCUSDT, ETHUSDT, etc.)
- Maintain proper buy/sell ratio theo market scenarios
- Clean up test data sau khi chạy xong

### 3. Environment Preparation
- Đảm bảo test environment ổn định
- Backup data trước khi chạy stress test
- Coordinate với team để tránh conflict

### 4. Result Analysis
- So sánh kết quả với baseline performance
- Document các issues và solutions
- Share kết quả với team development

## Kết Luận

Hệ thống test load cung cấp đầy đủ công cụ để kiểm tra hiệu năng matching engine ở các mức độ khác nhau. Sử dụng Python Kafka test cho kết quả chính xác nhất, và K6 HTTP test cho functional testing.

Liên hệ team development nếu có vấn đề kỹ thuật hoặc cần support thêm.