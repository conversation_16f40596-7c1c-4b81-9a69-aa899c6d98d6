# Matching Engine Architecture Documentation

## Overview

The Matching Engine module is designed as a high-performance, distributed order matching system that supports multiple matching algorithms and provides horizontal scalability through symbol sharding.

## Architecture Components

### 1. Core Matching Engine

#### FutureCoreMatchingEngine
- **Purpose**: Primary matching engine with lock-free CAS operations
- **Algorithms**: FIFO, Pro-Rata, Hybrid, TWAP
- **Features**:
  - Lock-free order book operations
  - CAS (Compare-And-Swap) retry loops
  - Optimized iterator-based order traversal
  - Performance metrics collection

#### DistributedOrderBook
- **Purpose**: Thread-safe order book with segmented storage
- **Features**:
  - Price range segmentation for better performance
  - StampedLock for optimistic reads
  - Iterator methods for efficient traversal
  - Batch operations support

### 2. Distributed Coordination

#### DistributedMatchingEngineCoordinator
- **Purpose**: Coordinates order processing across multiple pods
- **Features**:
  - Symbol ownership management
  - Distributed locking with Redis
  - Order routing between pods
  - Cluster health monitoring

#### SymbolShardingManager
- **Purpose**: Manages symbol distribution across pods
- **Features**:
  - Consistent hashing for symbol assignment
  - Dynamic pod discovery
  - Symbol migration support
  - Load balancing

### 3. Messaging Infrastructure

#### ConsolidatedKafkaProducer
- **Purpose**: Unified Kafka message publishing
- **Features**:
  - Trade result publishing
  - Order event notifications
  - Exchange format compatibility
  - Async message processing

#### Consumers
- **FutureCoreLastPriceConsumer**: Handles price updates and mark price calculations
- **FutureCoreLiquidationConsumer**: Processes liquidation events (delegated to separate service)
- **OrderRoutingConsumer**: Routes orders based on symbol sharding

### 4. Integration Layer

#### FutureCoreCompatibilityService
- **Purpose**: Provides compatibility with Future-Core module
- **Features**:
  - Contract order processing
  - Order cancellation handling
  - Funding rate updates
  - Event processing

## Data Flow

### Order Processing Flow

```
1. Order Received → OrderRoutingConsumer
2. Symbol Sharding Check → SymbolShardingManager
3. If Local: Process → FutureCoreMatchingEngine
4. If Remote: Route → ConsolidatedKafkaProducer
5. Matching → DistributedOrderBook
6. Trade Generation → FutureCoreTradeResult
7. Result Publishing → ConsolidatedKafkaProducer
```

### Price Update Flow

```
1. Price Data → FutureCoreLastPriceConsumer
2. Last Price Update → Cache & Database
3. Mark Price Calculation → Index Price Integration
4. Liquidation Check → Separate Service (delegated)
```

## Design Principles

### 1. Separation of Concerns
- **Matching Engine**: Pure order matching logic only
- **Post-Trade Processing**: Delegated to separate services
- **Position Management**: External service
- **Balance Updates**: External service

### 2. Performance Optimization
- **Lock-Free Operations**: CAS-based order book updates
- **Iterator Pattern**: Efficient order traversal
- **Batch Processing**: Multiple operations in single transaction
- **Optimistic Locking**: StampedLock for read-heavy workloads

### 3. Scalability
- **Horizontal Scaling**: Symbol-based sharding
- **Load Balancing**: Dynamic symbol migration
- **Distributed Coordination**: Redis-based locking
- **Async Processing**: Non-blocking message handling

### 4. Reliability
- **Fault Tolerance**: Graceful degradation on failures
- **Health Monitoring**: Cluster health checks
- **Recovery Mechanisms**: Symbol ownership recovery
- **Error Handling**: Comprehensive exception management

## Technology Stack

### Core Technologies
- **Java 17**: Primary programming language
- **Spring Boot**: Application framework
- **Redis**: Distributed coordination and caching
- **Kafka**: Message streaming
- **Redisson**: Distributed primitives

### Performance Libraries
- **ConcurrentSkipListMap**: Time-priority ordering
- **StampedLock**: Optimistic read locks
- **AtomicReference**: Lock-free updates
- **CompletableFuture**: Async operations

## Configuration

### Matching Algorithms
```yaml
matching-engine:
  default-algorithm: FIFO
  algorithm-by-symbol:
    BTCUSDT: FIFO
    ETHUSDT: PRO_RATA
    BNBUSDT: HYBRID
```

### Sharding Configuration
```yaml
sharding:
  total-pods: 3
  pod-name: ${POD_NAME:matching-engine-0}
  rebalance-threshold: 0.3
```

### Performance Tuning
```yaml
performance:
  order-book-segment-size: 1000
  cas-retry-limit: 100
  batch-size: 50
```

## Monitoring and Metrics

### Key Metrics
- **Order Processing Rate**: Orders per second
- **Trade Generation Rate**: Trades per second
- **CAS Success Rate**: Lock-free operation efficiency
- **Symbol Distribution**: Load balancing effectiveness

### Health Checks
- **Pod Health**: Individual pod status
- **Cluster Health**: Overall system status
- **Symbol Ownership**: Shard distribution
- **Message Queue**: Kafka lag monitoring

## Security Considerations

### Access Control
- **Symbol Ownership**: Prevents unauthorized access
- **Distributed Locks**: Ensures exclusive operations
- **Message Authentication**: Kafka security

### Data Protection
- **Order Privacy**: Symbol-based isolation
- **Trade Confidentiality**: Secure message transport
- **Audit Trail**: Complete operation logging

## Future Enhancements

### Planned Features
- **Multi-Region Support**: Cross-region coordination
- **Advanced Algorithms**: ML-based matching
- **Real-time Analytics**: Live performance monitoring
- **Auto-scaling**: Dynamic pod management

### Performance Improvements
- **Memory Optimization**: Reduced object allocation
- **Network Optimization**: Message compression
- **Storage Optimization**: Efficient data structures
- **Algorithm Tuning**: Adaptive algorithm selection
