# Future Stop Order Activation Flow Analysis

## 📊 Tổng quan

Sau khi loại bỏ `FutureStopOrderManager` khỏi matching-engine module, luồng kích hoạt stop order của future hiện tại được quản lý hoàn toàn bởi **Future-Core module** thông qua `DistributedLockFreeMatchingEngine`.

## 🔄 Luồng kích hoạt Stop Order hiện tại

### **📥 1. Stop Order Entry:**
```java
// DistributedLockFreeMatchingEngine.processOrder()
public List<Trade> processOrder(Order order) {
    // Kiểm tra xem lệnh có phải là stop order không
    if (isStopOrder(order)) {
        log.debug("Xử lý lệnh chờ: {}, symbol: {}", order.getOrderId(), symbol);
        return handleStopOrder(order);
    }
    
    // Xử lý regular order...
}
```

### **🔍 2. Stop Order Detection:**
```java
// DistributedLockFreeMatchingEngine.isStopOrder()
private boolean isStopOrder(Order order) {
    // Kiểm tra các loại stop order theo OrderType
    OrderType orderType = order.getType();
    boolean isStopType = orderType == OrderType.STOP_LOSS ||
                        orderType == OrderType.STOP_LOSS_LIMIT ||
                        orderType == OrderType.STOP_LIMIT ||
                        orderType == OrderType.STOP_MARKET ||
                        orderType == OrderType.TAKE_PROFIT ||
                        orderType == OrderType.TAKE_PROFIT_LIMIT ||
                        orderType == OrderType.TAKE_PROFIT_MARKET;

    // Hoặc có trigger price (backward compatibility)
    boolean hasTriggerPrice = order.getTriggerPrice() != null &&
                             !order.getTriggerPrice().equals(Money.ZERO);

    return isStopType || hasTriggerPrice;
}
```

### **⚡ 3. Stop Order Processing:**
```java
// DistributedLockFreeMatchingEngine.handleStopOrder()
private List<Trade> handleStopOrder(Order order) {
    // 1. Validate stop order
    if (!validateStopOrder(order)) {
        log.error("Invalid stop order: {}", order.getOrderId());
        return Collections.emptyList();
    }

    // 2. Kiểm tra xem stop order có thể kích hoạt ngay không
    if (canTriggerStopOrderImmediately(order)) {
        log.info("Stop order can be triggered immediately: {}", order.getOrderId());
        return triggerStopOrderImmediately(order);
    }

    // 3. Nếu không thể kích hoạt ngay, thêm vào danh sách chờ
    return addStopOrderToWaitingList(order);
}
```

### **🎯 4. Immediate Trigger Check:**
```java
// DistributedLockFreeMatchingEngine.canTriggerStopOrderImmediately()
private boolean canTriggerStopOrderImmediately(Order order) {
    Money triggerPrice = order.getTriggerPrice();
    if (triggerPrice == null) {
        return false;
    }

    // Sử dụng lastPrice để kiểm tra trigger condition
    if (lastPrice == null || lastPrice.getValue().compareTo(BigDecimal.ZERO) <= 0) {
        return false;
    }

    // Kiểm tra điều kiện trigger
    if (order.getDirection() == OrderDirection.BUY) {
        // BUY stop: trigger khi current price >= trigger price
        return lastPrice.getValue().compareTo(triggerPrice.getValue()) >= 0;
    } else {
        // SELL stop: trigger khi current price <= trigger price
        return lastPrice.getValue().compareTo(triggerPrice.getValue()) <= 0;
    }
}
```

### **🔥 5. Immediate Triggering:**
```java
// DistributedLockFreeMatchingEngine.triggerStopOrderImmediately()
private List<Trade> triggerStopOrderImmediately(Order order) {
    log.info("Triggering stop order immediately: {}", order.getOrderId());

    // Chuyển đổi stop order thành limit/market order
    Order triggeredOrder = convertStopOrderToRegularOrder(order);

    if (triggeredOrder == null) {
        log.error("Failed to convert stop order: {}", order.getOrderId());
        return Collections.emptyList();
    }

    log.info("Converted stop order to: Type={}, Price={}",
            triggeredOrder.getType(), triggeredOrder.getPrice());

    // Xử lý lệnh đã được chuyển đổi
    return processRegularOrder(triggeredOrder);
}
```

### **📋 6. Stop Order Conversion:**
```java
// DistributedLockFreeMatchingEngine.convertStopOrderToRegularOrder()
private Order convertStopOrderToRegularOrder(Order stopOrder) {
    OrderType newType;
    Money newPrice = null;

    switch (stopOrder.getType()) {
        case STOP_LIMIT:
        case STOP_LOSS_LIMIT:
        case TAKE_PROFIT_LIMIT:
            newType = OrderType.LIMIT;
            newPrice = stopOrder.getPrice(); // Sử dụng price đã set
            break;

        case STOP_MARKET:
        case STOP_LOSS:
        case TAKE_PROFIT:
        case TAKE_PROFIT_MARKET:
            newType = OrderType.MARKET;
            newPrice = null; // Market order không cần price
            break;

        default:
            log.error("Unknown stop order type: {}", stopOrder.getType());
            return null;
    }

    return stopOrder.toBuilder()
            .type(newType)
            .price(newPrice)
            .triggerPrice(null) // Xóa trigger price
            .status(OrderStatus.NEW)
            .build();
}
```

### **⏳ 7. Waiting List Management:**
```java
// DistributedLockFreeMatchingEngine.addStopOrderToWaitingList()
private List<Trade> addStopOrderToWaitingList(Order order) {
    // Xử lý lệnh theo cơ chế lock-free
    while (true) {
        // Lấy snapshot hiện tại
        DistributedOrderBookSnapshot currentSnapshot = orderBookRef.get();

        // Tạo bản sao tối ưu để thực hiện các thay đổi
        DistributedOrderBookSnapshot newSnapshot = currentSnapshot.createOptimizedCopy();

        // Thêm lệnh chờ vào snapshot
        newSnapshot.addStopOrder(order);

        // Cập nhật snapshot bằng CAS
        if (orderBookRef.compareAndSet(currentSnapshot, newSnapshot)) {
            log.info("Added stop order to waiting list: orderId={}, symbol={}, triggerPrice={}",
                     order.getOrderId(), symbol, order.getTriggerPrice());
            return Collections.emptyList();
        }

        // Nếu CAS thất bại, thử lại
        log.debug("CAS failed, retrying stop order handling: {}", order.getOrderId());
    }
}
```

## 🔄 Periodic Stop Order Checking

### **📈 8. Price Update Triggers:**
```java
// DistributedLockFreeMatchingEngine.checkStopOrders()
public void checkStopOrders(Money currentPrice) {
    if (currentPrice == null || currentPrice.getValue().compareTo(BigDecimal.ZERO) <= 0) {
        return;
    }

    // Cập nhật mark price với giá hiện tại
    this.markPrice = currentPrice;

    // Xử lý theo cơ chế lock-free
    while (true) {
        // Lấy snapshot hiện tại
        DistributedOrderBookSnapshot currentSnapshot = orderBookRef.get();

        // Tạo bản sao tối ưu để thực hiện các thay đổi
        DistributedOrderBookSnapshot newSnapshot = currentSnapshot.createOptimizedCopy();

        // Danh sách lệnh chờ cần kích hoạt
        List<Order> ordersToTrigger = new ArrayList<>();

        // Kiểm tra các lệnh chờ
        for (Order stopOrder : newSnapshot.getStopOrders()) {
            if (isStopOrderTriggered(stopOrder)) {
                ordersToTrigger.add(stopOrder);
            }
        }

        // Nếu không có lệnh chờ nào cần kích hoạt, thoát
        if (ordersToTrigger.isEmpty()) {
            return;
        }

        // Xóa các lệnh chờ đã kích hoạt khỏi danh sách
        for (Order order : ordersToTrigger) {
            newSnapshot.removeStopOrder(order);
        }

        // Cập nhật snapshot bằng CAS
        if (orderBookRef.compareAndSet(currentSnapshot, newSnapshot)) {
            // Xử lý các lệnh chờ đã kích hoạt
            for (Order order : ordersToTrigger) {
                // Chuyển đổi lệnh chờ thành lệnh thường
                Order triggeredOrder = convertStopOrderToRegularOrder(order);

                // Xử lý lệnh
                processOrder(triggeredOrder);
            }

            return;
        }

        // Nếu CAS thất bại, thử lại
        log.debug("CAS thất bại, thử lại kiểm tra lệnh chờ với giá: {}", currentPrice);
    }
}
```

### **🎯 9. Trigger Condition Check:**
```java
// DistributedLockFreeMatchingEngine.isStopOrderTriggered()
private boolean isStopOrderTriggered(Order stopOrder) {
    // Nếu không có giá đánh dấu hoặc giá giao dịch cuối cùng, không kích hoạt
    if (markPrice == null || markPrice.getValue().compareTo(BigDecimal.ZERO) <= 0 ||
        lastPrice == null || lastPrice.getValue().compareTo(BigDecimal.ZERO) <= 0) {
        return false;
    }

    // Lấy giá kích hoạt
    Money triggerPrice = stopOrder.getTriggerPrice();
    if (triggerPrice == null) {
        return false;
    }

    // Kiểm tra điều kiện kích hoạt
    if (stopOrder.getDirection() == OrderDirection.BUY) {
        // Lệnh mua: kích hoạt khi giá >= giá kích hoạt
        return lastPrice.compareTo(triggerPrice) >= 0;
    } else {
        // Lệnh bán: kích hoạt khi giá <= giá kích hoạt
        return lastPrice.compareTo(triggerPrice) <= 0;
    }
}
```

## 📈 Price Update Sources

### **🔄 10. External Price Updates:**
```java
// PriceUpdateScheduler.updatePricesForSymbol()
orderMatchingEngineService.updateIndexPrice(symbol, indexPrice);
orderMatchingEngineService.updateMarkPrice(symbol, markPrice);

// RealTimeMarketPriceUpdater.updatePriceForSymbol()
priceManagementService.updateMarkPrice(symbol, newPrice, LocalDateTime.now());

// Các price updates này sẽ trigger checkStopOrders()
```

## 🔍 So sánh với Spot

| Aspect | Spot (Exchange-Core) | Future (Future-Core) |
|--------|---------------------|---------------------|
| **Stop Order Manager** | SimpleStopOrderManager | Built-in DistributedLockFreeMatchingEngine |
| **Detection Algorithm** | Sign-based detection | Price comparison (traditional) |
| **Trigger Mechanism** | Event-driven (AsyncStopOrderProcessor) | Price update + periodic check |
| **Storage** | ConcurrentHashMap | DistributedOrderBookSnapshot |
| **Concurrency** | Thread-safe collections | Lock-free CAS operations |
| **Price Source** | Trade execution events | Mark price + last price |

## ✅ Kết luận

### **🎯 Future Stop Order Flow hiện tại:**

1. **✅ Complete Integration**: Stop orders được tích hợp hoàn toàn trong DistributedLockFreeMatchingEngine
2. **✅ Lock-free Operations**: Sử dụng CAS operations cho high performance
3. **✅ Immediate + Delayed Triggering**: Hỗ trợ cả trigger ngay và waiting list
4. **✅ Multiple Order Types**: STOP_LIMIT, STOP_MARKET, STOP_LOSS, TAKE_PROFIT
5. **✅ Price-based Triggering**: Sử dụng mark price và last price
6. **✅ Automatic Conversion**: Stop orders → Regular orders → Normal matching

### **🔄 Trigger Sources:**
- **Immediate**: Khi stop order được submit và điều kiện đã thỏa mãn
- **Periodic**: Khi có price updates từ external sources
- **Trade-based**: Sau khi có trades làm thay đổi last price

**Luồng stop order của future hoạt động hoàn toàn độc lập và tích hợp sâu trong matching engine, đảm bảo performance cao và consistency!**
