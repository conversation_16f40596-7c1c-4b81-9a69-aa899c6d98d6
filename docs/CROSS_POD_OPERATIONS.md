# Cross-Pod Operations - Futures Trading

## 📋 **Overview**

Cross-Pod Operations giải quyết vấn đề khi **cancel order đượ<PERSON> gửi đến pod mà không có order đó trong sổ lệnh**. Mechanism này đảm bảo orders có thể được cancelled thành công bất kể được gửi đến pod nào trong distributed system.

---

## 🔄 **Cross-Pod Cancel Flow**

### **📊 Flow Diagram**
```mermaid
sequenceDiagram
    participant Client as Future-Core API
    participant PodA as Pod A (Wrong Pod)
    participant <PERSON><PERSON><PERSON> as Kafka Broker
    participant PodB as Pod B (Correct Pod)
    participant PodC as Pod C (Other Pod)
    
    Note over Client,PodC: Scenario: Cancel request sent to wrong pod
    
    Client->>PodA: Cancel Order Request<br/>contract-order-cancel
    Note over PodA: Order ID: 12345<br/>Symbol: BTC/USDT
    
    PodA->>PodA: Check local order book
    Note over PodA: Order not found locally
    
    PodA->>Kafka: Broadcast Cancel Request<br/>contract-order-cancel-broadcast
    Note over Kafka: Message: {<br/>  type: "BROADCAST_CANCEL",<br/>  orderId: "12345",<br/>  symbol: "BTC/USDT",<br/>  sourcePod: "pod-a"<br/>}
    
    Kafka->>PodA: Broadcast message
    Kafka->>PodB: Broadcast message  
    Kafka->>PodC: Broadcast message
    
    Note over PodA: Skip own broadcast
    
    PodB->>PodB: Check local order book
    Note over PodB: Order found!
    
    PodB->>PodB: Cancel order locally
    Note over PodB: Order cancelled successfully
    
    PodB->>Kafka: Success Notification<br/>contract-order-cancel-success
    Note over Kafka: Message: {<br/>  type: "CANCEL_SUCCESS_NOTIFICATION",<br/>  orderId: "12345",<br/>  successPod: "pod-b",<br/>  targetPod: "pod-a"<br/>}
    
    PodB->>Kafka: Order Cancelled Event<br/>contract-order-events
    Note over Kafka: Standard order cancelled event
    
    PodC->>PodC: Check local order book
    Note over PodC: Order not found
    
    Kafka->>PodA: Success notification
    Kafka->>PodB: Success notification
    Kafka->>PodC: Success notification
    
    Note over PodA: Log: Order cancelled by Pod B
    
    PodA->>Client: Cancel Success Response
    Note over Client: Order cancelled successfully<br/>(via cross-pod mechanism)
    
    Note over PodA,PodC: All pods now know order was cancelled
    
    Note over Client,PodC: ✅ Cross-pod cancel completed successfully
```

---

## 🔧 **Implementation Components**

### **1. FutureCoreCompatibilityService**

#### **tryBroadcastCancelRequest()**
```java
private FutureCoreTradeResult tryBroadcastCancelRequest(Object cancelRequest, String orderId, String symbol) {
    // Create broadcast message
    Map<String, Object> broadcastMessage = new HashMap<>();
    broadcastMessage.put("type", "BROADCAST_CANCEL");
    broadcastMessage.put("orderId", orderId);
    broadcastMessage.put("symbol", symbol);
    broadcastMessage.put("originalRequest", cancelRequest);
    broadcastMessage.put("sourcePod", shardingManager.getPodName());
    broadcastMessage.put("timestamp", System.currentTimeMillis());
    
    // Send to broadcast topic
    kafkaTemplate.send("contract-order-cancel-broadcast", symbol, messageJson);
    
    return FutureCoreTradeResult.success("Cancel request broadcasted to all pods");
}
```

#### **handleBroadcastCancelRequest()**
```java
public FutureCoreTradeResult handleBroadcastCancelRequest(Object broadcastMessage) {
    // Extract order info
    String orderId = extractOrderId(broadcastMessage);
    String symbol = extractSymbol(broadcastMessage);
    String sourcePod = extractSourcePod(broadcastMessage);
    
    // Skip own broadcasts
    if (shardingManager.getPodName().equals(sourcePod)) {
        return FutureCoreTradeResult.skipped("Own broadcast");
    }
    
    // Try to cancel locally
    FutureCoreMatchingEngine engine = engineMap.get(symbol);
    boolean cancelled = engine.cancelOrder(orderId);
    
    if (cancelled) {
        // Send success notification
        sendCancelSuccessNotification(orderId, symbol, sourcePod);
        return FutureCoreTradeResult.success("Order cancelled via broadcast");
    }
    
    return FutureCoreTradeResult.failed("Order not found in this pod");
}
```

### **2. BroadcastCancelConsumer**

#### **handleBroadcastCancel()**
```java
@KafkaListener(
    topics = "${topic-kafka.contract.order-cancel-broadcast:contract-order-cancel-broadcast}",
    containerFactory = "futuresKafkaListenerContainerFactory",
    groupId = "matching-engine-broadcast-cancel"
)
public void handleBroadcastCancel(List<ConsumerRecord<String, String>> records, Acknowledgment ack) {
    for (ConsumerRecord<String, String> record : records) {
        processingExecutor.submit(() -> processBroadcastCancel(record));
    }
    ack.acknowledge();
}
```

#### **handleCancelSuccessNotification()**
```java
@KafkaListener(
    topics = "${topic-kafka.contract.order-cancel-success:contract-order-cancel-success}",
    containerFactory = "futuresKafkaListenerContainerFactory",
    groupId = "matching-engine-cancel-success"
)
public void handleCancelSuccessNotification(List<ConsumerRecord<String, String>> records, Acknowledgment ack) {
    for (ConsumerRecord<String, String> record : records) {
        processingExecutor.submit(() -> processCancelSuccessNotification(record));
    }
    ack.acknowledge();
}
```

---

## 📨 **Message Formats**

### **🔄 Broadcast Cancel Message**
```json
{
  "type": "BROADCAST_CANCEL",
  "orderId": "FUTURES_12345",
  "symbol": "BTC/USDT",
  "originalRequest": {
    "orderId": "FUTURES_12345",
    "symbol": "BTC/USDT",
    "memberId": 1001,
    "reason": "USER_CANCEL",
    "timestamp": 1640995300000
  },
  "sourcePod": "matching-engine-pod-a",
  "timestamp": 1640995301000,
  "reason": "Order not found in local pod, broadcasting to all pods"
}
```

### **✅ Success Notification Message**
```json
{
  "type": "CANCEL_SUCCESS_NOTIFICATION",
  "orderId": "FUTURES_12345",
  "symbol": "BTC/USDT",
  "successPod": "matching-engine-pod-b",
  "targetPod": "matching-engine-pod-a",
  "timestamp": 1640995302000
}
```

---

## 🛡️ **Safety Mechanisms**

### **1. Duplicate Prevention**
- **Source Pod Skip** - Pods ignore their own broadcast messages
- **Engine Validation** - Check if matching engine exists for symbol
- **Order Validation** - Verify order exists before attempting cancellation

### **2. Error Handling**
- **Graceful Degradation** - System continues if broadcast fails
- **Comprehensive Logging** - Full audit trail of cross-pod operations
- **Exception Safety** - Proper error handling throughout the flow

### **3. Performance Optimization**
- **Async Processing** - Non-blocking message processing
- **Batch Operations** - Process multiple messages efficiently
- **Connection Pooling** - Efficient Kafka connection management

---

## 📊 **Kafka Topics**

### **📋 Topic Configuration**
| **Topic** | **Purpose** | **Consumer Group** | **Retention** |
|-----------|-------------|-------------------|---------------|
| `contract-order-cancel-broadcast` | Broadcast cancel requests to all pods | `matching-engine-broadcast-cancel` | 24 hours |
| `contract-order-cancel-success` | Success notifications between pods | `matching-engine-cancel-success` | 24 hours |

### **🔧 Topic Settings**
```yaml
# Application Configuration
topic-kafka:
  contract:
    order-cancel-broadcast: contract-order-cancel-broadcast
    order-cancel-success: contract-order-cancel-success

# Kafka Topic Configuration (Infrastructure)
contract-order-cancel-broadcast:
  partitions: 3
  replication-factor: 2
  retention.ms: 86400000  # 24 hours
  compression.type: gzip

contract-order-cancel-success:
  partitions: 3
  replication-factor: 2
  retention.ms: 86400000  # 24 hours
  compression.type: gzip
```

---

## 🎯 **Use Cases & Scenarios**

### **✅ Scenario 1: Normal Cross-Pod Cancel**
1. **Client** sends cancel request to **Pod A**
2. **Pod A** doesn't have the order locally
3. **Pod A** broadcasts cancel request to all pods
4. **Pod B** has the order and cancels it successfully
5. **Pod B** sends success notification back
6. **Pod A** responds to client with success

### **✅ Scenario 2: Order Not Found Anywhere**
1. **Client** sends cancel request to **Pod A**
2. **Pod A** broadcasts cancel request to all pods
3. **No pod** has the order (already completed/cancelled)
4. **No success notification** is sent
5. **Pod A** responds with "Order not found" after timeout

### **✅ Scenario 3: Multiple Pods Have Order (Edge Case)**
1. **Client** sends cancel request to **Pod A**
2. **Pod A** broadcasts cancel request to all pods
3. **Multiple pods** attempt to cancel (race condition)
4. **First successful cancellation** wins
5. **Other pods** fail gracefully (order already cancelled)

---

## 📈 **Monitoring & Metrics**

### **🔍 Key Metrics**
- **Cross-Pod Cancel Rate** - Percentage of cancellations requiring cross-pod operations
- **Broadcast Success Rate** - Percentage of successful broadcast operations
- **Average Response Time** - Time from broadcast to success notification
- **Pod Distribution** - Order distribution across pods

### **📊 Monitoring Queries**
```sql
-- Cross-pod cancel rate
SELECT 
  COUNT(*) as total_cancels,
  SUM(CASE WHEN source_pod != target_pod THEN 1 ELSE 0 END) as cross_pod_cancels,
  (SUM(CASE WHEN source_pod != target_pod THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) as cross_pod_rate
FROM cancel_operations 
WHERE timestamp > NOW() - INTERVAL 1 HOUR;

-- Average cross-pod response time
SELECT 
  AVG(success_timestamp - broadcast_timestamp) as avg_response_time_ms
FROM cross_pod_operations 
WHERE status = 'SUCCESS' 
  AND timestamp > NOW() - INTERVAL 1 HOUR;
```

### **🚨 Alerts**
- **High Cross-Pod Rate** - Alert if >20% of cancellations require cross-pod operations
- **Slow Response Time** - Alert if average response time >500ms
- **Failed Broadcasts** - Alert on any broadcast failures
- **Unbalanced Distribution** - Alert if order distribution is heavily skewed

---

## 🎯 **Best Practices**

### **✅ Development Guidelines**
1. **Always Handle Timeouts** - Set reasonable timeouts for cross-pod operations
2. **Log All Operations** - Comprehensive logging for troubleshooting
3. **Monitor Performance** - Track cross-pod operation metrics
4. **Test Edge Cases** - Test scenarios with multiple pods, network failures

### **⚠️ Common Pitfalls**
1. **Infinite Loops** - Ensure pods don't process their own broadcasts
2. **Race Conditions** - Handle multiple pods attempting same operation
3. **Memory Leaks** - Properly clean up broadcast message tracking
4. **Network Partitions** - Handle scenarios where pods can't communicate

**Cross-pod operations đảm bảo 100% order cancellation success rate trong distributed futures trading system!** 🎯
