# Futures Message Flow Analysis - Matching-Engine Module

## 📋 **Overview**

Phân tích chi tiết message flow của futures trading trong matching-engine module, bao gồm tất cả input/output topics, consumers, producers và luồng xử lý.

---

## 🔄 **Complete Futures Message Flow**

### **📥 INPUT TOPICS**

| **Topic** | **Consumer** | **Group ID** | **Purpose** |
|-----------|-------------|--------------|-------------|
| `order-routing` | OrderRoutingConsumer | `matching-engine-order-routing` | Primary entry với intelligent sharding |
| `contract-order-commands` | OrderCommandConsumer | `matching-engine-order-commands` | Fallback entry cho direct processing |
| `contract-order-cancel` | FutureCoreOrderConsumer | `matching-engine-contract-cancels` | Order cancellation requests |
| `contract-order-cancel-success` | FutureCoreOrderCancelSuccessConsumer | `matching-engine-contract-cancel-success` | Cancel success notifications |

### **📤 OUTPUT TOPICS**

| **Topic** | **Producer Method** | **Purpose** | **Downstream Consumer** |
|-----------|-------------------|-------------|------------------------|
| `contract-order-completed` | `publishContractOrderCompleted()` | Completed orders | Future-Core Module |
| `contract-trade` | `publishContractTrade()` | Trade executions | Market Module |
| `contract-trade-plate` | `publishContractTradePlate()` | Order book updates | UI/WebSocket |
| `contract-order-cancel-success` | `publishContractOrderCancelSuccess()` | Cancel success events | Post-trade services |
| `contract-order-events` | `publishOrderPlacedEvent()` | Order lifecycle events | Notification Service |

---

## 🚀 **PRIMARY FLOW: Order Routing**

### **1. Entry Point**
```
Future API → order-routing topic
```

### **2. OrderRoutingConsumer Processing**
```java
@KafkaListener(
    topics = "${topic-kafka.contract.order-routing:order-routing}",
    containerFactory = "futuresKafkaListenerContainerFactory",
    groupId = "matching-engine-order-routing"
)
```

**Features:**
- **Intelligent Sharding:** Symbol-based routing với SmartShardingManager
- **Multi-Command Support:** PLACE_ORDER, CANCEL_ORDER, UPDATE_ORDER
- **Fallback Mechanism:** Route to OrderCommandConsumer khi routing fails

### **3. Symbol Sharding Logic**
```java
boolean canProcessSymbol = canProcessSymbolOnThisPod(symbol);
if (!canProcessSymbol) {
    // Route to correct pod hoặc fallback
    routeOrderToCorrectPod(symbol, order);
}
```

### **4. Processing Flow**
```
OrderRoutingConsumer → FutureCoreCompatibilityService → FutureCoreMatchingEngine
```

---

## 🔄 **FALLBACK FLOW: Order Commands**

### **1. Entry Point**
```
Future API → contract-order-commands topic
OrderRoutingConsumer (routing fails) → contract-order-commands topic
```

### **2. OrderCommandConsumer Processing**
```java
@KafkaListener(
    topics = "${topic-kafka.contract.order-commands:contract-order-commands}",
    containerFactory = "futuresKafkaListenerContainerFactory",
    groupId = "matching-engine-order-commands"
)
```

**Features:**
- **Direct Processing:** Không có sharding logic
- **Command Types:** PLACE_ORDER, CANCEL_ORDER, UPDATE_ORDER
- **Fallback Target:** Được gọi từ OrderRoutingConsumer

---

## ❌ **CANCEL FLOW: Order Cancellation**

### **1. Entry Point**
```
Future API → contract-order-cancel topic
```

### **2. FutureCoreOrderConsumer Processing**
```java
@KafkaListener(
    topics = "${topic-kafka.contract.order-cancel:contract-order-cancel}",
    containerFactory = "futuresKafkaListenerContainerFactory",
    groupId = "matching-engine-contract-cancels"
)
```

**Features:**
- **Cancel Only:** Chỉ xử lý cancel requests
- **Success Publishing:** Gửi cancel success message
- **Trade Plate Update:** Update order book sau khi cancel

### **3. Cancel Success Notification**
```java
@KafkaListener(
    topics = "${topic-kafka.contract.order-cancel-success:contract-order-cancel-success}",
    containerFactory = "futuresKafkaListenerContainerFactory",
    groupId = "matching-engine-contract-cancel-success"
)
```

**Features:**
- **Notification Only:** Không update database
- **Local Metrics:** Update monitoring và metrics
- **Simple Processing:** Chỉ log và track

---

## ⚙️ **CORE PROCESSING COMPONENTS**

### **1. FutureCoreCompatibilityService**
```java
public FutureCoreTradeResult processContractOrderInternal(Object order)
```

**Responsibilities:**
- Order validation và conversion
- Business logic processing
- Matching engine coordination
- Result aggregation

### **2. FutureCoreMatchingEngine**
```java
public List<Trade> processOrder(Order order)
```

**Features:**
- **Multiple Algorithms:** FIFO, PRO_RATA, HYBRID, TWAP
- **Lock-free Processing:** CAS operations
- **High Performance:** ~12K TPS
- **Stop Order Support:** FutureStopOrder integration

### **3. FutureCoreKafkaProducer**
```java
// 2-parameter pattern cho consistency
public void publishContractTrade(String symbol, Object trade)
public void publishContractOrderCompleted(String symbol, Object order)
```

**Features:**
- **Async Publishing:** CompletableFuture cho performance
- **Batch Processing:** Trade batch publishing
- **Error Handling:** Comprehensive exception management
- **2-Parameter Pattern:** Consistency với Exchange module

---

## 📊 **MESSAGE FLOW METRICS**

### **Performance Characteristics**
- **Throughput:** ~12K TPS (futures)
- **Latency:** <5ms average processing time
- **Concurrency:** Lock-free CAS operations
- **Scalability:** Horizontal scaling với sharding

### **Topic Retention**
- **Input Topics:** 24 hours
- **Output Topics:** 7 days
- **Cancel Topics:** 24 hours

### **Consumer Groups**
- **Unique Groups:** Mỗi consumer có group ID riêng
- **No Conflicts:** Không có consumer group conflicts
- **Parallel Processing:** Multiple consumers có thể chạy parallel

---

## 🔧 **KAFKA CONFIGURATION**

### **Container Factories**
```yaml
futuresKafkaListenerContainerFactory:
  concurrency: 9
  ack-mode: manual
  session-timeout: 15000
```

### **Topic Configuration**
```yaml
topic-kafka:
  contract:
    order-routing: order-routing
    order-commands: contract-order-commands
    order-cancel: contract-order-cancel
    order-cancel-success: contract-order-cancel-success
    order-completed: contract-order-completed
    trade: contract-trade
    trade-plate: contract-trade-plate
    order-events: contract-order-events
```

---

## 🎯 **KEY FEATURES**

### **1. Intelligent Routing**
- Symbol-based sharding
- Cross-pod coordination
- Automatic fallback

### **2. High Performance**
- Lock-free matching engine
- Async message processing
- Batch trade publishing

### **3. Robust Error Handling**
- Multiple fallback levels
- Comprehensive logging
- Graceful degradation

### **4. Scalability**
- Horizontal scaling support
- Distributed coordination
- Load balancing

---

## 📊 **FLOW COMPARISON TABLE**

| **Aspect** | **Primary Flow (Routing)** | **Fallback Flow (Commands)** | **Cancel Flow** |
|------------|---------------------------|------------------------------|-----------------|
| **Entry Topic** | `order-routing` | `contract-order-commands` | `contract-order-cancel` |
| **Consumer** | OrderRoutingConsumer | OrderCommandConsumer | FutureCoreOrderConsumer |
| **Sharding** | ✅ Intelligent sharding | ❌ Direct processing | ❌ Direct processing |
| **Commands** | PLACE/CANCEL/UPDATE | PLACE/CANCEL/UPDATE | CANCEL only |
| **Fallback** | → OrderCommandConsumer | → Direct processing | ❌ No fallback |
| **Performance** | High (with routing) | Medium (direct) | High (simple) |
| **Use Case** | Normal operations | Routing failures | Cancel requests |

## 🔄 **MESSAGE SEQUENCE DIAGRAM**

### **Successful Order Placement**
```
Future API → order-routing → OrderRoutingConsumer → [Sharding Check] →
FutureCoreCompatibilityService → FutureCoreMatchingEngine →
FutureCoreKafkaProducer → [contract-trade, contract-order-completed, contract-trade-plate]
```

### **Order Cancellation**
```
Future API → contract-order-cancel → FutureCoreOrderConsumer →
FutureCoreCompatibilityService → FutureCoreMatchingEngine →
FutureCoreKafkaProducer → contract-order-cancel-success →
FutureCoreOrderCancelSuccessConsumer → [Local Metrics]
```

### **Routing Failure Fallback**
```
Future API → order-routing → OrderRoutingConsumer → [Sharding Fails] →
contract-order-commands → OrderCommandConsumer →
FutureCoreCompatibilityService → [Same as successful flow]
```

---

## 🔄 **INTEGRATION POINTS**

### **Upstream Services**
- **Future API:** Sends orders to order-routing
- **Future-Core Module:** Reference only (không active)

### **Downstream Services**
- **Future-Core Module:** Consumes completed orders
- **Market Module:** Consumes trades
- **UI/WebSocket:** Consumes trade plates
- **Notification Service:** Consumes order events
- **Post-trade Services:** Consumes cancel success events

---

## 🎯 **SUMMARY**

### **Message Flow Characteristics:**
- **3 Input Paths:** Routing (primary), Commands (fallback), Cancel (specialized)
- **5 Output Topics:** Comprehensive event publishing
- **Intelligent Routing:** Symbol-based sharding với fallback
- **High Performance:** Lock-free processing, async publishing
- **Robust Design:** Multiple fallback levels, error handling

### **Key Advantages:**
- **Scalability:** Horizontal scaling với sharding
- **Reliability:** Multiple fallback mechanisms
- **Performance:** ~12K TPS với low latency
- **Flexibility:** Support multiple order types và algorithms
- **Monitoring:** Comprehensive metrics và logging
