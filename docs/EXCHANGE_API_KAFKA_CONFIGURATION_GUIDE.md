# 📋 Hướng dẫn cấu hình Kafka cho Exchange-API gửi message đến Matching-Engine

## 🎯 Mục tiêu
Hướng dẫn này giúp cấu hình module **exchange-api** để gửi Kafka messages đúng format và topics mà **matching-engine** có thể nhận và xử lý.

## 📊 Mapping Topics giữa Exchange-API và Matching-Engine

### **Topics mà Matching-Engine đang consume:**

| **Chức năng** | **Topic Name** | **Consumer Group** | **Message Format** |
|---------------|----------------|-------------------|-------------------|
| **Order Submission** | `exchange-order` | `matching-engine-exchange-orders` | JSON Order Object |
| **Order Cancellation** | `exchange-order-cancel` | `matching-engine-exchange-cancels` | JSON Cancel Request |
| **Order Completed** | `exchange-order-completed` | `matching-engine-exchange-orders` | JSON Order Array |
| **Trade Data** | `exchange-trade` | `matching-engine-exchange-trades` | JSON Trade Array |
| **Trade Plate** | `exchange-trade-plate` | `matching-engine-exchange-trades` | JSON Trade Plate |

## 🔧 Cấu hình Exchange-API

### **1. Cấu hình application.yml cho Exchange-API**

```yaml
spring:
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP:************:30092}
    producer:
      # Cấu hình giống matching-engine để đảm bảo compatibility
      retries: 3
      batch-size: 256
      linger-ms: 1
      buffer-memory: 1048576
      compression-type: gzip
      # QUAN TRỌNG: Cấu hình idempotence
      acks: all  # Required for idempotent producer
      enable-idempotence: true
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer

# Topic Configuration - PHẢI KHỚP với matching-engine
topic-kafka:
  exchange:
    order: exchange-order                    # Matching-engine consume từ topic này
    order-cancel: exchange-order-cancel      # Matching-engine consume từ topic này  
    order-completed: exchange-order-completed # Matching-engine consume từ topic này
    trade: exchange-trade                    # Matching-engine consume từ topic này
    trade-plate: exchange-trade-plate        # Matching-engine consume từ topic này
```

### **2. Cấu hình application-dev.yml cho Development**

```yaml
spring:
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP:************:30092}
    producer:
      retries: 1  # Less retries for dev
      batch-size: 16  # Smaller batch for dev
      linger-ms: 1
      buffer-memory: 524288  # Smaller buffer for dev
      compression-type: gzip
      acks: all
      enable-idempotence: true

# Development Topics - PHẢI KHỚP với matching-engine dev config
topic-kafka:
  exchange:
    order: dev-exchange-order
    order-cancel: dev-exchange-order-cancel
    order-completed: dev-exchange-order-completed
    trade: dev-exchange-trade
    trade-plate: dev-exchange-trade-plate
```

### **3. Cấu hình application-local.yml cho Local Development**

```yaml
spring:
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP:************:9092}
    producer:
      retries: 1
      batch-size: 16
      linger-ms: 1
      buffer-memory: 524288
      compression-type: gzip
      acks: all
      enable-idempotence: true

# Local Topics với suffix để tránh conflict
topic-kafka:
  exchange:
    order: exchange-order-local-${USER_NAME:default}
    order-cancel: exchange-order-cancel-local-${USER_NAME:default}
    order-completed: exchange-order-completed-local-${USER_NAME:default}
    trade: exchange-trade-local-${USER_NAME:default}
    trade-plate: exchange-trade-plate-local-${USER_NAME:default}
```

## 📝 Message Format Requirements

### **1. Order Submission Message**
**Topic:** `exchange-order`
**Format:** JSON Object (2 parameters: topic, message)

```json
{
  "orderId": "E175032088315322",
  "symbol": "BTC/USDT",
  "amount": 1,
  "price": 106576.88,
  "direction": "BUY",
  "type": "LIMIT_PRICE",
  "memberId": 601255,
  "time": 1750320883153,
  "status": "TRADING",
  "baseSymbol": "USDT",
  "coinSymbol": "BTC",
  "orderResource": "CUSTOMER",
  "selfTradePreventionMode": "CANCEL_MAKER",
  "completed": false,
  "tradedAmount": 0,
  "turnover": 0,
  "useDiscount": 0
}
```

### **2. Order Cancellation Message**
**Topic:** `exchange-order-cancel`
**Format:** JSON Object

```json
{
  "orderId": "E175032088315322",
  "symbol": "BTC/USDT",
  "memberId": 601255,
  "reason": "USER_CANCEL",
  "timestamp": 1750320883153
}
```

### **3. Order Completed Message**
**Topic:** `exchange-order-completed`
**Format:** JSON Array

```json
[
  {
    "orderId": "E175032088315322",
    "symbol": "BTC/USDT",
    "status": "COMPLETED",
    "completedAmount": 1,
    "completedTime": 1750320883153
  }
]
```

## 🔧 Producer Configuration Class

### **Cập nhật KafkaProducerConfiguration.java**

```java
@Configuration
@EnableKafka
public class KafkaProducerConfiguration {

    @Value("${spring.kafka.bootstrap-servers}")
    private String servers;
    
    @Value("${spring.kafka.producer.retries}")
    private int retries;
    
    @Value("${spring.kafka.producer.batch-size}")
    private int batchSize;
    
    @Value("${spring.kafka.producer.buffer-memory}")
    private int bufferMemory;

    public Map<String, Object> producerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, servers);
        props.put(ProducerConfig.RETRIES_CONFIG, retries);
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, batchSize);
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, bufferMemory);
        props.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, "gzip");
        
        // QUAN TRỌNG: Cấu hình idempotence như matching-engine
        props.put(ProducerConfig.ACKS_CONFIG, "all");
        props.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, true);
        props.put(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION, 5);
        
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        return props;
    }

    @Bean
    public ProducerFactory<String, String> producerFactory() {
        return new DefaultKafkaProducerFactory<>(producerConfigs());
    }

    @Bean
    public KafkaTemplate<String, String> kafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }
}
```

## 📤 Producer Service Pattern

### **Tạo ExchangeKafkaProducerService.java**

```java
@Service
@Slf4j
public class ExchangeKafkaProducerService {

    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ObjectMapper objectMapper;
    
    @Value("${topic-kafka.exchange.order}")
    private String exchangeOrderTopic;
    
    @Value("${topic-kafka.exchange.order-cancel}")
    private String exchangeOrderCancelTopic;

    public ExchangeKafkaProducerService(KafkaTemplate<String, String> kafkaTemplate, 
                                       ObjectMapper objectMapper) {
        this.kafkaTemplate = kafkaTemplate;
        this.objectMapper = objectMapper;
    }

    /**
     * Gửi order submission - SỬ DỤNG 2 PARAMETERS như Exchange module
     */
    public void sendOrderSubmission(Object order) {
        try {
            String orderJson = objectMapper.writeValueAsString(order);
            kafkaTemplate.send(exchangeOrderTopic, orderJson);  // 2 parameters
            log.debug("Sent order submission to topic: {}", exchangeOrderTopic);
        } catch (JsonProcessingException e) {
            log.error("Error serializing order to JSON", e);
        }
    }

    /**
     * Gửi order cancellation - SỬ DỤNG 2 PARAMETERS như Exchange module
     */
    public void sendOrderCancellation(Object cancelRequest) {
        try {
            String cancelJson = objectMapper.writeValueAsString(cancelRequest);
            kafkaTemplate.send(exchangeOrderCancelTopic, cancelJson);  // 2 parameters
            log.debug("Sent order cancellation to topic: {}", exchangeOrderCancelTopic);
        } catch (JsonProcessingException e) {
            log.error("Error serializing cancel request to JSON", e);
        }
    }
}
```

## ⚠️ Lưu ý quan trọng

### **1. Message Pattern**
- **Exchange-API PHẢI sử dụng 2 parameters**: `kafkaTemplate.send(topic, message)`
- **KHÔNG sử dụng 3 parameters**: `kafkaTemplate.send(topic, key, message)`

### **2. Topic Names**
- **Production**: Sử dụng topic names chuẩn (`exchange-order`, `exchange-trade`, etc.)
- **Development**: Sử dụng prefix `dev-` (`dev-exchange-order`, `dev-exchange-trade`, etc.)
- **Local**: Sử dụng suffix với username (`exchange-order-local-duongnt`)

### **3. Kafka Configuration**
- **PHẢI có `acks: all`** và `enable-idempotence: true`
- **Bootstrap servers** phải khớp với matching-engine
- **Serializers** phải là `StringSerializer`

### **4. Message Format**
- **Tất cả messages** phải là JSON format
- **Order messages** phải chứa đầy đủ required fields
- **Optional fields** (như `stopPrice`) có thể không có

## 🧪 Testing Configuration

### **Kiểm tra kết nối**
```bash
# Kiểm tra topics có tồn tại
kafka-topics --bootstrap-server ************:30092 --list | grep exchange

# Kiểm tra consumer groups
kafka-consumer-groups --bootstrap-server ************:30092 --list | grep matching-engine
```

### **Test message flow**
```bash
# Gửi test message
kafka-console-producer --bootstrap-server ************:30092 --topic exchange-order

# Kiểm tra matching-engine có nhận được không
# Check logs của matching-engine service
```

## 🎯 Checklist

- [ ] ✅ Cấu hình Kafka producer với `acks: all` và `enable-idempotence: true`
- [ ] ✅ Sử dụng đúng topic names theo environment
- [ ] ✅ Sử dụng 2 parameters cho `kafkaTemplate.send()`
- [ ] ✅ Message format là JSON với đầy đủ required fields
- [ ] ✅ Bootstrap servers khớp với matching-engine
- [ ] ✅ Test kết nối và message flow

**Sau khi cấu hình theo hướng dẫn này, exchange-api sẽ gửi messages đúng format mà matching-engine có thể nhận và xử lý thành công!** 🚀
