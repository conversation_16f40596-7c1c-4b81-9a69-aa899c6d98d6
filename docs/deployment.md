# Matching Engine Deployment Guide

## Overview

This document provides comprehensive deployment instructions for the Matching Engine module, including containerization, Kubernetes deployment, and production configuration.

## Prerequisites

### System Requirements

#### Minimum Requirements (Development)
- **CPU**: 4 cores
- **Memory**: 8GB RAM
- **Storage**: 50GB SSD
- **Network**: 1Gbps

#### Recommended Requirements (Production)
- **CPU**: 16 cores (Intel Xeon or AMD EPYC)
- **Memory**: 32GB RAM
- **Storage**: 200GB NVMe SSD
- **Network**: 10Gbps

### Software Dependencies
- **Java**: OpenJDK 17 or later
- **Docker**: 20.10+ 
- **Kubernetes**: 1.24+
- **Redis**: 7.0+
- **Kafka**: 3.0+
- **PostgreSQL**: 14+

## Docker Configuration

### Dockerfile

```dockerfile
FROM openjdk:17-jdk-slim

# Set working directory
WORKDIR /app

# Copy application JAR
COPY target/matching-engine-*.jar app.jar

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

# JVM optimization
ENV JAVA_OPTS="-Xms2g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=10"

# Start application
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

### Docker Compose (Development)

```yaml
version: '3.8'

services:
  matching-engine:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - REDIS_HOST=redis
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - DB_HOST=postgres
    depends_on:
      - redis
      - kafka
      - postgres
    networks:
      - matching-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - matching-network

  kafka:
    image: confluentinc/cp-kafka:latest
    environment:
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    depends_on:
      - zookeeper
    networks:
      - matching-network

  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
    networks:
      - matching-network

  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: matching_engine
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    networks:
      - matching-network

networks:
  matching-network:
    driver: bridge
```

## Kubernetes Deployment

### Namespace Configuration

```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: matching-engine
  labels:
    name: matching-engine
```

### ConfigMap

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: matching-engine-config
  namespace: matching-engine
data:
  application.yml: |
    spring:
      profiles:
        active: kubernetes
    
    matching-engine:
      default-algorithm: FIFO
      use-distributed: true
      
    sharding:
      total-pods: 3
      rebalance-threshold: 0.3
      
    performance:
      order-book-segment-size: 1000
      cas-retry-limit: 100
      batch-size: 50
      
    redis:
      host: redis-service
      port: 6379
      
    kafka:
      bootstrap-servers: kafka-service:9092
      
    database:
      host: postgres-service
      port: 5432
      name: matching_engine
```

### Secret Configuration

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: matching-engine-secrets
  namespace: matching-engine
type: Opaque
data:
  database-username: cG9zdGdyZXM=  # postgres (base64)
  database-password: cGFzc3dvcmQ=  # password (base64)
  redis-password: ""
```

### Deployment Configuration

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: matching-engine
  namespace: matching-engine
  labels:
    app: matching-engine
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: matching-engine
  template:
    metadata:
      labels:
        app: matching-engine
    spec:
      containers:
      - name: matching-engine
        image: matching-engine:latest
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 8081
          name: management
        env:
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: TOTAL_PODS
          value: "3"
        - name: SPRING_PROFILES_ACTIVE
          value: "kubernetes"
        - name: DATABASE_USERNAME
          valueFrom:
            secretKeyRef:
              name: matching-engine-secrets
              key: database-username
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: matching-engine-secrets
              key: database-password
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /actuator/health/liveness
            port: 8081
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8081
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: config-volume
        configMap:
          name: matching-engine-config
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - matching-engine
              topologyKey: kubernetes.io/hostname
```

### Service Configuration

```yaml
apiVersion: v1
kind: Service
metadata:
  name: matching-engine-service
  namespace: matching-engine
  labels:
    app: matching-engine
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  - port: 8081
    targetPort: 8081
    protocol: TCP
    name: management
  selector:
    app: matching-engine
```

### Horizontal Pod Autoscaler

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: matching-engine-hpa
  namespace: matching-engine
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: matching-engine
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 25
        periodSeconds: 60
```

## Production Configuration

### Environment-Specific Settings

#### Production Application Properties
```yaml
spring:
  profiles:
    active: production
    
logging:
  level:
    com.icetea.lotus.matching: INFO
    org.springframework.kafka: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

management:
  endpoints:
    web:
      exposure:
        include: health,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

matching-engine:
  performance:
    order-book-segment-size: 2000
    cas-retry-limit: 200
    batch-size: 100
  
  monitoring:
    metrics-enabled: true
    health-check-interval: 30s
```

### Security Configuration

#### Network Policies
```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: matching-engine-network-policy
  namespace: matching-engine
spec:
  podSelector:
    matchLabels:
      app: matching-engine
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: api-gateway
    ports:
    - protocol: TCP
      port: 8080
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: database
    ports:
    - protocol: TCP
      port: 5432
  - to:
    - namespaceSelector:
        matchLabels:
          name: redis
    ports:
    - protocol: TCP
      port: 6379
```

## Monitoring and Observability

### Prometheus Configuration

```yaml
apiVersion: v1
kind: ServiceMonitor
metadata:
  name: matching-engine-metrics
  namespace: matching-engine
spec:
  selector:
    matchLabels:
      app: matching-engine
  endpoints:
  - port: management
    path: /actuator/prometheus
    interval: 30s
```

### Grafana Dashboard

Key metrics to monitor:
- **Order Processing Rate**: orders/second
- **Trade Generation Rate**: trades/second
- **CAS Success Rate**: percentage
- **Memory Usage**: heap utilization
- **CPU Usage**: percentage
- **Response Time**: P50, P95, P99 latencies

### Alerting Rules

```yaml
groups:
- name: matching-engine-alerts
  rules:
  - alert: HighOrderProcessingLatency
    expr: histogram_quantile(0.95, rate(order_processing_duration_seconds_bucket[5m])) > 0.005
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High order processing latency detected"
      
  - alert: LowCASSuccessRate
    expr: cas_success_rate < 0.9
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "CAS success rate below threshold"
```

## Deployment Procedures

### Rolling Deployment

```bash
# Update deployment image
kubectl set image deployment/matching-engine \
  matching-engine=matching-engine:v1.2.0 \
  -n matching-engine

# Monitor rollout
kubectl rollout status deployment/matching-engine -n matching-engine

# Verify deployment
kubectl get pods -n matching-engine
```

### Blue-Green Deployment

```bash
# Deploy green environment
kubectl apply -f deployment-green.yaml

# Switch traffic
kubectl patch service matching-engine-service \
  -p '{"spec":{"selector":{"version":"green"}}}' \
  -n matching-engine

# Cleanup blue environment
kubectl delete deployment matching-engine-blue -n matching-engine
```

### Canary Deployment

```bash
# Deploy canary version (10% traffic)
kubectl apply -f deployment-canary.yaml

# Monitor metrics and gradually increase traffic
# If successful, promote canary to production
```

## Troubleshooting

### Common Issues

#### Pod Startup Issues
```bash
# Check pod logs
kubectl logs -f deployment/matching-engine -n matching-engine

# Check events
kubectl get events -n matching-engine --sort-by='.lastTimestamp'
```

#### Performance Issues
```bash
# Check resource usage
kubectl top pods -n matching-engine

# Check JVM metrics
kubectl exec -it <pod-name> -- jstat -gc 1
```

#### Network Issues
```bash
# Test connectivity
kubectl exec -it <pod-name> -- nc -zv redis-service 6379
kubectl exec -it <pod-name> -- nc -zv kafka-service 9092
```

### Recovery Procedures

#### Pod Recovery
```bash
# Restart deployment
kubectl rollout restart deployment/matching-engine -n matching-engine

# Scale down and up
kubectl scale deployment matching-engine --replicas=0 -n matching-engine
kubectl scale deployment matching-engine --replicas=3 -n matching-engine
```

#### Data Recovery
```bash
# Restore from backup
kubectl exec -it postgres-pod -- psql -U postgres -d matching_engine < backup.sql

# Clear Redis cache
kubectl exec -it redis-pod -- redis-cli FLUSHALL
```
