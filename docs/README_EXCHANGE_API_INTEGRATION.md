# 🔗 Exchange-API ↔ Matching-Engine Integration Guide

## 📋 Tổng quan

Hướng dẫn này cung cấp tất cả thông tin cần thiết để cấu hình **exchange-api** gửi <PERSON>fka messages đúng format đến **matching-engine**.

## 📁 Cấu trúc tài liệu

```
matching-engine/docs/
├── EXCHANGE_API_KAFKA_CONFIGURATION_GUIDE.md  # Hướng dẫn chi tiết
├── config-samples/                            # File cấu hình mẫu
│   ├── exchange-api-application-prod.yml      # Production config
│   ├── exchange-api-application-dev.yml       # Development config
│   └── exchange-api-application-local.yml     # Local development config
├── code-samples/                              # Code mẫu
│   └── ExchangeKafkaProducerService.java      # Producer service mẫu
└── README_EXCHANGE_API_INTEGRATION.md         # File này
```

## 🚀 Quick Start

### **1. Chọn environment và copy config**

**Production:**
```bash
cp matching-engine/docs/config-samples/exchange-api-application-prod.yml exchange-api/src/main/resources/application-prod.yml
```

**Development:**
```bash
cp matching-engine/docs/config-samples/exchange-api-application-dev.yml exchange-api/src/main/resources/application-dev.yml
```

**Local:**
```bash
cp matching-engine/docs/config-samples/exchange-api-application-local.yml exchange-api/src/main/resources/application-local.yml
```

### **2. Copy Producer Service**

```bash
cp matching-engine/docs/code-samples/ExchangeKafkaProducerService.java exchange-api/src/main/java/com/icetea/lotus/service/kafka/
```

### **3. Set environment variables**

**Production:**
```bash
export KAFKA_BOOTSTRAP=************:30092
```

**Development:**
```bash
export KAFKA_BOOTSTRAP=************:30092
```

**Local:**
```bash
export KAFKA_BOOTSTRAP=10.242.30.45:9092
export USER_NAME=your_username  # e.g., duongnt, anhems
```

### **4. Start application**

```bash
# Production
java -jar exchange-api.jar --spring.profiles.active=prod

# Development  
java -jar exchange-api.jar --spring.profiles.active=dev

# Local
java -jar exchange-api.jar --spring.profiles.active=local
```

## 🔧 Key Configuration Points

### **1. Topic Mapping**

| **Environment** | **Exchange-API Topic** | **Matching-Engine Consumer** |
|----------------|------------------------|------------------------------|
| **Production** | `exchange-order` | ✅ Consumes |
| **Development** | `dev-exchange-order` | ✅ Consumes |
| **Local** | `exchange-order-local-{username}` | ⚠️ Manual setup needed |

### **2. Producer Pattern**

**✅ ĐÚNG - Exchange module pattern (2 parameters):**
```java
kafkaTemplate.send(topic, message);
```

**❌ SAI - Future-core pattern (3 parameters):**
```java
kafkaTemplate.send(topic, key, message);  // KHÔNG sử dụng
```

### **3. Required Kafka Settings**

```yaml
spring:
  kafka:
    producer:
      acks: all                    # BẮT BUỘC
      enable-idempotence: true     # BẮT BUỘC
      retries: 3                   # Recommended
      compression-type: gzip       # Recommended
```

## 📊 Message Flow Diagram

```
Exchange-API                    Matching-Engine
     │                               │
     │ 1. Order Submission           │
     ├─────────────────────────────→ │ exchange-order
     │                               │
     │ 2. Order Cancellation         │
     ├─────────────────────────────→ │ exchange-order-cancel
     │                               │
     │ 3. Order Completed            │
     ├─────────────────────────────→ │ exchange-order-completed
     │                               │
     │ 4. Trade Data                 │
     ├─────────────────────────────→ │ exchange-trade
     │                               │
     │ 5. Trade Plate                │
     ├─────────────────────────────→ │ exchange-trade-plate
```

## 🧪 Testing & Validation

### **1. Health Check**

```bash
# Check application health
curl http://localhost:8080/actuator/health

# Check Kafka connectivity
curl http://localhost:8080/actuator/kafka
```

### **2. Topic Verification**

```bash
# List topics
kafka-topics --bootstrap-server ************:30092 --list | grep exchange

# Check consumer groups
kafka-consumer-groups --bootstrap-server ************:30092 --list | grep matching-engine
```

### **3. Message Testing**

```bash
# Send test message
kafka-console-producer --bootstrap-server ************:30092 --topic exchange-order

# Monitor matching-engine logs
kubectl logs -f deployment/matching-engine | grep "Received.*exchange order"
```

## ⚠️ Common Issues & Solutions

### **Issue 1: Topic not found**
```
Error: Topic 'exchange-order' does not exist
```
**Solution:** Ensure topic names match exactly between exchange-api and matching-engine configs.

### **Issue 2: Idempotence error**
```
Error: Must set acks=all when idempotence is enabled
```
**Solution:** Add `acks: all` to producer configuration.

### **Issue 3: Consumer group not receiving**
```
No messages received by matching-engine
```
**Solution:** Check consumer group names and topic names match.

### **Issue 4: Serialization error**
```
Error: Cannot serialize message to JSON
```
**Solution:** Ensure ObjectMapper is configured with JavaTimeModule.

## 📞 Support & Contact

### **Development Team Contacts:**
- **Matching-Engine Team:** <EMAIL>
- **Exchange-API Team:** <EMAIL>
- **DevOps Team:** <EMAIL>

### **Documentation Updates:**
- **Location:** `matching-engine/docs/`
- **Last Updated:** 2025-06-19
- **Version:** 1.0.0

### **Slack Channels:**
- `#matching-engine-support`
- `#exchange-api-integration`
- `#kafka-infrastructure`

## 🎯 Checklist

**Pre-deployment checklist:**

- [ ] ✅ Config file copied and customized for environment
- [ ] ✅ Producer service implemented with 2-parameter pattern
- [ ] ✅ Environment variables set correctly
- [ ] ✅ Kafka bootstrap servers configured
- [ ] ✅ Topic names match matching-engine configuration
- [ ] ✅ Producer idempotence enabled (`acks: all`)
- [ ] ✅ Health checks passing
- [ ] ✅ Test messages sent and received successfully
- [ ] ✅ Consumer groups verified
- [ ] ✅ Logging configured for monitoring

**Post-deployment verification:**

- [ ] ✅ Messages flowing from exchange-api to matching-engine
- [ ] ✅ No errors in application logs
- [ ] ✅ Kafka consumer lag is minimal
- [ ] ✅ Performance metrics within acceptable range
- [ ] ✅ Monitoring alerts configured

## 🔄 Version History

| **Version** | **Date** | **Changes** |
|-------------|----------|-------------|
| 1.0.0 | 2025-06-19 | Initial integration guide |

---

**🎉 Sau khi hoàn thành checklist, exchange-api sẽ gửi messages thành công đến matching-engine!**
