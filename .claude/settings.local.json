{"permissions": {"allow": ["<PERSON><PERSON>(sudo apt:*)", "Bash(sudo apt install:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(wget:*)", "Bash(tar:*)", "Bash(ls:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./k6:*)", "Bash(./health-checker.sh:*)", "Bash(bash:*)", "Bash(./master-control.sh:*)", "Bash(./demo-test.sh:*)", "Bash(./run-ccu-ccr-test.sh:*)", "Bash(./run-unified-test.sh:*)", "<PERSON><PERSON>(python:*)", "Bash(pip install:*)", "Bash(pip3 install:*)", "Bash(./kafka-connection-test.sh:*)", "<PERSON><PERSON>(k6 run:*)", "Bash(find:*)", "Bash(rg:*)"], "deny": []}}