# ==================== ULTRA-HIGH PERFORMANCE JVM OPTIMIZATION ====================
# JVM Settings for Maximum Matching Engine Performance
# Target: 10,000+ TPS with minimal latency

# ==================== MEMORY OPTIMIZATION ====================
# Heap Size - Large enough to avoid GC pressure
-Xms8g
-Xmx8g
-XX:NewRatio=1
-XX:SurvivorRatio=8

# Direct Memory for off-heap operations
-XX:MaxDirectMemorySize=4g

# ==================== GARBAGE COLLECTION OPTIMIZATION ====================
# Use G1GC for low-latency, high-throughput
-XX:+UseG1GC
-XX:MaxGCPauseMillis=10
-XX:G1HeapRegionSize=16m
-XX:G1NewSizePercent=30
-XX:G1MaxNewSizePercent=40
-XX:G1MixedGCCountTarget=8
-XX:G1MixedGCLiveThresholdPercent=85

# Aggressive GC tuning for ultra-low latency
-XX:+UnlockExperimentalVMOptions
-XX:+UseG1GC
-XX:+G1UseAdaptiveIHOP
-XX:G1MixedGCCountTarget=4
-XX:G1OldCSetRegionThresholdPercent=5

# ==================== COMPILATION OPTIMIZATION ====================
# Aggressive JIT compilation
-XX:+UnlockDiagnosticVMOptions
-XX:+TraceClassLoading
-XX:+LogVMOutput
-XX:CompileThreshold=1000
-XX:Tier4CompileThreshold=5000

# Enable aggressive optimizations
-XX:+UseStringDeduplication
-XX:+OptimizeStringConcat
-XX:+UseCompressedOops
-XX:+UseCompressedClassPointers

# ==================== CPU & THREADING OPTIMIZATION ====================
# Optimize for multi-core systems
-XX:+UseParallelGC
-XX:ParallelGCThreads=8
-XX:ConcGCThreads=4

# Thread optimization
-XX:+UseBiasedLocking
-XX:+UseThreadPriorities
-XX:ThreadPriorityPolicy=1

# ==================== MEMORY ALLOCATION OPTIMIZATION ====================
# TLAB (Thread Local Allocation Buffer) optimization
-XX:+UseTLAB
-XX:TLABSize=1m
-XX:+ResizeTLAB
-XX:TLABWasteTargetPercent=1

# Large object optimization
-XX:+UseLargePages
-XX:LargePageSizeInBytes=2m

# ==================== PERFORMANCE MONITORING ====================
# Enable performance monitoring (disable in production)
-XX:+PrintGC
-XX:+PrintGCDetails
-XX:+PrintGCTimeStamps
-XX:+PrintGCApplicationStoppedTime
-Xloggc:gc.log

# JFR for performance analysis
-XX:+FlightRecorder
-XX:StartFlightRecording=duration=60s,filename=matching-engine.jfr

# ==================== ULTRA-PERFORMANCE FLAGS ====================
# Experimental flags for maximum performance
-XX:+UnlockExperimentalVMOptions
-XX:+UseEpsilonGC
-XX:+AlwaysPreTouch
-XX:+UseTransparentHugePages

# CPU-specific optimizations
-XX:+UseAVX
-XX:+UseAVX2
-XX:+UseFMA

# ==================== DEBUGGING & PROFILING ====================
# Enable for development/testing only
-XX:+PrintCompilation
-XX:+PrintInlining
-XX:+TraceClassLoading
-XX:+LogVMOutput

# JMX for monitoring
-Dcom.sun.management.jmxremote
-Dcom.sun.management.jmxremote.port=9999
-Dcom.sun.management.jmxremote.authenticate=false
-Dcom.sun.management.jmxremote.ssl=false

# ==================== APPLICATION-SPECIFIC OPTIMIZATION ====================
# Matching Engine specific settings
-Dmatching.engine.mode=ultra-performance
-Dmatching.engine.threads=16
-Dmatching.engine.queue.size=10000
-Dmatching.engine.batch.size=100

# Kafka optimization
-Dkafka.producer.batch.size=65536
-Dkafka.producer.linger.ms=1
-Dkafka.producer.compression.type=lz4

# MongoDB optimization
-Dmongo.connection.pool.size=50
-Dmongo.connection.timeout=5000
-Dmongo.socket.timeout=10000

# ==================== SYSTEM PROPERTIES ====================
# File encoding
-Dfile.encoding=UTF-8
-Dsun.jnu.encoding=UTF-8

# Network optimization
-Djava.net.preferIPv4Stack=true
-Dsun.net.useExclusiveBind=false

# Security optimization (disable for performance)
-Djava.security.egd=file:/dev/./urandom

# ==================== NUMA OPTIMIZATION ====================
# NUMA-aware memory allocation
-XX:+UseNUMA
-XX:+UseNUMAInterleaving

# CPU affinity (requires additional setup)
-XX:+UseParallelOldGC
-XX:+UseAdaptiveSizePolicy

# ==================== FINAL OPTIMIZATION FLAGS ====================
# Ultimate performance flags (use with caution)
-XX:+AggressiveOpts
-XX:+UseFastAccessorMethods
-XX:+UseFastEmptyMethods
-XX:+OptimizeStringConcat

# Disable safety checks for maximum speed
-XX:-UseGCOverheadLimit
-XX:+DisableExplicitGC
-XX:-UseGCOverheadLimit

# ==================== PRODUCTION SETTINGS ====================
# For production deployment
-server
-XX:+UseServerVM
-XX:+TieredCompilation
-XX:TieredStopAtLevel=4

# Error handling
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=/tmp/matching-engine-heap-dump.hprof
-XX:OnOutOfMemoryError="kill -9 %p"

# ==================== STARTUP OPTIMIZATION ====================
# Fast startup
-XX:+UnlockDiagnosticVMOptions
-XX:+UseAOT
-XX:AOTLibrary=./matching-engine.so

# Class data sharing
-Xshare:on
-XX:+UseAppCDS
-XX:SharedArchiveFile=matching-engine.jsa

# ==================== END OF CONFIGURATION ====================
