# Performance Test Runner Script
# Runs load tests against the Exchange API

param(
    [string]$TestType = "quick",  # quick, heavy, or full
    [int]$Users = 50,
    [int]$OrdersPerUser = 5
)

Write-Host "Exchange API Performance Test Runner" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Check if <PERSON>ven is available
if (!(Get-Command mvn -ErrorAction SilentlyContinue)) {
    Write-Host "Maven not found. Please install Maven first." -ForegroundColor Red
    exit 1
}

# Set test parameters based on type
switch ($TestType.ToLower()) {
    "quick" {
        $TestClass = "SimpleLoadTest"
        $TestMethod = "quickLoadTest"
        Write-Host "Running Quick Load Test (50 users, 5 orders each)" -ForegroundColor Yellow
    }
    "heavy" {
        $TestClass = "SimpleLoadTest"
        $TestMethod = "heavyLoadTest"
        Write-Host "Running Heavy Load Test (100 users, 10 orders each)" -ForegroundColor Yellow
    }
    "full" {
        $TestClass = "OrderPerformanceTest"
        $TestMethod = "testOrderPerformanceWith100CCU"
        Write-Host "Running Full Performance Test (100 CCU, realistic trading)" -ForegroundColor Yellow
    }
    "extreme" {
        $TestClass = "ExtremeLoadTest"
        $TestMethod = "extremeLoadTest"
        Write-Host "Running EXTREME Load Test (1000 orders/sec for 1 minute)" -ForegroundColor Red
        Write-Host "WARNING: This will generate 60,000 orders!" -ForegroundColor Red
    }
    "gradual" {
        $TestClass = "GradualLoadTest"
        $TestMethod = "gradualLoadTest"
        Write-Host "Running GRADUAL Load Test (Progressive: 10->50->200->500 RPS)" -ForegroundColor Cyan
        Write-Host "INFO: Tests system behavior under increasing load" -ForegroundColor Cyan
    }
    "analysis" {
        $TestClass = "MessageLossAnalysisTest"
        $TestMethod = "messageLossAnalysisTest"
        Write-Host "Running MESSAGE LOSS Analysis (Track API to Kafka)" -ForegroundColor Magenta
        Write-Host "INFO: Analyzes message delivery reliability" -ForegroundColor Magenta
    }
    default {
        Write-Host "Invalid test type. Use: quick, heavy, full, extreme, gradual, or analysis" -ForegroundColor Red
        exit 1
    }
}

Write-Host "Test Configuration:" -ForegroundColor Cyan
Write-Host "   - Test Type: $TestType" -ForegroundColor White
Write-Host "   - Target URL: http://************:30113/exchange-api/order/add" -ForegroundColor White
Write-Host "   - Test Class: $TestClass" -ForegroundColor White
Write-Host "   - Test Method: $TestMethod" -ForegroundColor White
Write-Host ""

# Compile the project first
Write-Host "Compiling project..." -ForegroundColor Yellow
$compileResult = mvn clean compile test-compile -q
if ($LASTEXITCODE -ne 0) {
    Write-Host "Compilation failed!" -ForegroundColor Red
    exit 1
}
Write-Host "Compilation successful" -ForegroundColor Green

# Run the specific test
Write-Host "Running performance test..." -ForegroundColor Yellow
Write-Host "Start time: $(Get-Date)" -ForegroundColor Cyan

$testCommand = "mvn test `"-Dtest=com.icetea.lotus.matching.performance.$TestClass#$TestMethod`" -q"
Write-Host "Command: $testCommand" -ForegroundColor Gray

# Execute the test
$startTime = Get-Date
& mvn test "-Dtest=com.icetea.lotus.matching.performance.$TestClass#$TestMethod" -q
$endTime = Get-Date
$duration = $endTime - $startTime

Write-Host ""
Write-Host "End time: $(Get-Date)" -ForegroundColor Cyan
Write-Host "Total duration: $($duration.TotalSeconds) seconds" -ForegroundColor Cyan

if ($LASTEXITCODE -eq 0) {
    Write-Host "Performance test completed successfully!" -ForegroundColor Green
} else {
    Write-Host "Performance test failed!" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Test Summary:" -ForegroundColor Green
Write-Host "   - Test Type: $TestType" -ForegroundColor White
Write-Host "   - Duration: $($duration.TotalSeconds) seconds" -ForegroundColor White
Write-Host "   - Status: $(if ($LASTEXITCODE -eq 0) { 'SUCCESS' } else { 'FAILED' })" -ForegroundColor $(if ($LASTEXITCODE -eq 0) { 'Green' } else { 'Red' })

Write-Host ""
Write-Host "Usage examples:" -ForegroundColor Cyan
Write-Host "   .\run-performance-test.ps1 -TestType quick" -ForegroundColor Gray
Write-Host "   .\run-performance-test.ps1 -TestType heavy" -ForegroundColor Gray
Write-Host "   .\run-performance-test.ps1 -TestType full" -ForegroundColor Gray
Write-Host "   .\run-performance-test.ps1 -TestType extreme" -ForegroundColor Red
Write-Host "   .\run-performance-test.ps1 -TestType gradual" -ForegroundColor Cyan
Write-Host "   .\run-performance-test.ps1 -TestType analysis" -ForegroundColor Magenta
